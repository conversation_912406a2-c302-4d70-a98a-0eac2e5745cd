#!/usr/bin/env python3
"""
JARVIS HUD Interface Demo
Showcases the futuristic AI interface without requiring Ollama
"""

import sys
import time
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QFont
from src.core.config import Config
from src.gui.jarvis_hud import Jarvis<PERSON>ore
from src.gui.hud_panels import SystemInfoPanel, TimePanel, StatusPanel

class JarvisHUDDemo(QMainWindow):
    """Demo window for JARVIS HUD interface"""
    
    def __init__(self):
        super().__init__()
        self.config = Config.load_from_env()
        self.init_ui()
        self.setup_demo_timer()
        
    def init_ui(self):
        """Initialize the demo UI"""
        self.setWindowTitle("J.A.R.V.I.S. HUD Interface Demo")
        self.setGeometry(50, 50, 1400, 900)
        self.setStyleSheet("background-color: #000000;")
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Left panel
        self.create_left_panel(main_layout)
        
        # Center area
        self.create_center_area(main_layout)
        
        # Right panel
        self.create_right_panel(main_layout)
        
    def create_left_panel(self, parent_layout):
        """Create left HUD panel"""
        left_panel = QWidget()
        left_panel.setFixedWidth(300)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(20)
        
        # Title
        from PyQt6.QtWidgets import QLabel
        title = QLabel("J.A.R.V.I.S.")
        title.setStyleSheet(f"""
            font-size: 32px;
            font-weight: bold;
            color: {self.config.THEME_PRIMARY_COLOR};
            font-family: 'Consolas', 'Monaco', monospace;
            text-align: center;
            letter-spacing: 3px;
            margin: 20px 0;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(title)
        
        # System panel
        self.system_panel = SystemInfoPanel(self.config)
        left_layout.addWidget(self.system_panel)
        
        # Time panel
        self.time_panel = TimePanel(self.config)
        left_layout.addWidget(self.time_panel)
        
        # Status panel
        self.status_panel = StatusPanel(self.config)
        left_layout.addWidget(self.status_panel)
        
        left_layout.addStretch()
        parent_layout.addWidget(left_panel)
        
    def create_center_area(self, parent_layout):
        """Create center area with JARVIS core"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setContentsMargins(0, 0, 0, 0)
        center_layout.setSpacing(20)
        
        # JARVIS Core
        core_container = QWidget()
        core_container.setFixedHeight(420)
        core_container_layout = QHBoxLayout(core_container)
        core_container_layout.setContentsMargins(0, 0, 0, 0)
        
        self.jarvis_core = JarvisCore(self.config)
        core_container_layout.addStretch()
        core_container_layout.addWidget(self.jarvis_core)
        core_container_layout.addStretch()
        
        center_layout.addWidget(core_container)
        
        # Demo controls
        self.create_demo_controls(center_layout)
        
        center_layout.addStretch()
        parent_layout.addWidget(center_widget, 1)
        
    def create_right_panel(self, parent_layout):
        """Create right panel"""
        right_panel = QWidget()
        right_panel.setFixedWidth(300)
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(20)
        
        # Demo info
        from PyQt6.QtWidgets import QLabel, QFrame
        info_frame = QFrame()
        info_frame.setStyleSheet(f"""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0, 50, 100, 0.8),
                stop:1 rgba(0, 30, 60, 0.6));
            border: 2px solid {self.config.THEME_PRIMARY_COLOR};
            border-radius: 10px;
            padding: 15px;
        """)
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("DEMO MODE")
        info_title.setStyleSheet(f"""
            color: {self.config.THEME_PRIMARY_COLOR};
            font-size: 14px;
            font-weight: bold;
            font-family: 'Consolas', 'Monaco', monospace;
            text-align: center;
        """)
        info_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_layout.addWidget(info_title)
        
        info_text = QLabel("This demonstrates the JARVIS HUD interface with animated core visualization and real-time system monitoring.")
        info_text.setStyleSheet(f"""
            color: {self.config.THEME_TEXT_COLOR};
            font-size: 10px;
            font-family: 'Consolas', 'Monaco', monospace;
        """)
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        
        right_layout.addWidget(info_frame)
        right_layout.addStretch()
        
        parent_layout.addWidget(right_panel)
        
    def create_demo_controls(self, parent_layout):
        """Create demo control buttons"""
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        
        # Speaking mode button
        self.speaking_button = QPushButton("ACTIVATE SPEAKING MODE")
        self.speaking_button.setStyleSheet(f"""
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {self.config.THEME_PRIMARY_COLOR},
                stop:1 {self.config.THEME_SECONDARY_COLOR});
            border: none;
            border-radius: 8px;
            color: #000000;
            font-weight: bold;
            font-size: 12px;
            padding: 12px 20px;
            font-family: 'Consolas', 'Monaco', monospace;
        """)
        self.speaking_button.clicked.connect(self.toggle_speaking_mode)
        controls_layout.addWidget(self.speaking_button)
        
        # Status button
        self.status_button = QPushButton("SIMULATE AI RESPONSE")
        self.status_button.setStyleSheet(f"""
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {self.config.THEME_ACCENT_COLOR},
                stop:1 #CC4400);
            border: none;
            border-radius: 8px;
            color: #000000;
            font-weight: bold;
            font-size: 12px;
            padding: 12px 20px;
            font-family: 'Consolas', 'Monaco', monospace;
        """)
        self.status_button.clicked.connect(self.simulate_ai_response)
        controls_layout.addWidget(self.status_button)
        
        parent_layout.addWidget(controls_widget)
        
    def setup_demo_timer(self):
        """Setup demo automation timer"""
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self.demo_cycle)
        self.demo_timer.start(10000)  # Every 10 seconds
        
        self.demo_state = 0
        
    def demo_cycle(self):
        """Automatic demo cycle"""
        if self.demo_state == 0:
            self.simulate_ai_response()
        elif self.demo_state == 1:
            self.toggle_speaking_mode()
        elif self.demo_state == 2:
            self.toggle_speaking_mode()  # Turn off speaking mode
            
        self.demo_state = (self.demo_state + 1) % 3
        
    def toggle_speaking_mode(self):
        """Toggle speaking mode demo"""
        current_speaking = self.jarvis_core.is_speaking
        self.jarvis_core.set_speaking_mode(not current_speaking)
        
        if not current_speaking:
            self.speaking_button.setText("DEACTIVATE SPEAKING MODE")
            self.status_panel.update_tts_status("SPEAKING", "#00FF00")
        else:
            self.speaking_button.setText("ACTIVATE SPEAKING MODE")
            self.status_panel.update_tts_status("READY", self.config.THEME_PRIMARY_COLOR)
            
    def simulate_ai_response(self):
        """Simulate AI response"""
        self.status_panel.update_ai_status("PROCESSING", "#FFFF00")
        
        # Reset after 3 seconds
        QTimer.singleShot(3000, lambda: self.status_panel.update_ai_status("ONLINE", "#00FF00"))

def main():
    """Run the JARVIS HUD demo"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("JARVIS HUD Demo")
    app.setApplicationVersion("1.0.0")
    
    # Create and show demo window
    demo = JarvisHUDDemo()
    demo.show()
    
    print("🚀 JARVIS HUD Demo launched!")
    print("Features demonstrated:")
    print("  • Animated JARVIS core visualization")
    print("  • Real-time system monitoring")
    print("  • HUD panels with glow effects")
    print("  • Speaking mode animation")
    print("  • Futuristic sci-fi styling")
    print("\nPress Ctrl+C to exit")
    
    # Start the application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
