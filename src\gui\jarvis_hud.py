"""
JARVIS HUD Interface - Central animated AI visualization
Inspired by Iron Man's JARVIS interface
"""

import math
import random
from src.gui.qt_compat import (QWidget, QTimer, QPropertyAnimation, QEasingCurve,
                               pyqtProperty, QPointF, QRectF, Qt, QPen, QBrush,
                               QColor, QPainter, QRadialGradient, QLinearGradient)
from src.core.config import Config

class JarvisCore(QWidget):
    """Central JARVIS AI core visualization widget"""
    
    def __init__(self, config: Config, parent=None):
        super().__init__(parent)
        self.config = config
        self.setFixedSize(400, 400)
        
        # Animation state
        self.is_speaking = False
        self.core_radius = 80
        self.pulse_intensity = 0.3
        self.rotation_angle = 0
        self.ring_scales = [1.0, 1.0, 1.0]
        self.particle_positions = []
        
        # Initialize particles
        self.init_particles()
        
        # Setup animations
        self.setup_animations()
        
    def init_particles(self):
        """Initialize floating particles around the core"""
        for i in range(12):
            angle = (i / 12) * 2 * math.pi
            radius = 120 + random.uniform(-20, 20)
            self.particle_positions.append({
                'angle': angle,
                'radius': radius,
                'speed': random.uniform(0.01, 0.03),
                'size': random.uniform(2, 6),
                'opacity': random.uniform(0.3, 0.8)
            })
    
    def setup_animations(self):
        """Setup core animations"""
        # Rotation animation
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self.update_rotation)
        self.rotation_timer.start(50)  # 20 FPS
        
        # Pulse animation
        self.pulse_animation = QPropertyAnimation(self, b"pulse_intensity")
        self.pulse_animation.setDuration(2000)
        self.pulse_animation.setStartValue(0.3)
        self.pulse_animation.setEndValue(1.0)
        self.pulse_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        self.pulse_animation.setLoopCount(-1)
        self.pulse_animation.start()
        
        # Ring scale animations
        self.ring_animations = []
        for i in range(3):
            anim = QPropertyAnimation(self, f"ring_scale_{i}".encode())
            anim.setDuration(3000 + i * 500)
            anim.setStartValue(0.8)
            anim.setEndValue(1.2)
            anim.setEasingCurve(QEasingCurve.Type.InOutSine)
            anim.setLoopCount(-1)
            self.ring_animations.append(anim)
            anim.start()
    
    @pyqtProperty(float)
    def pulse_intensity(self):
        return self._pulse_intensity
    
    @pulse_intensity.setter
    def pulse_intensity(self, value):
        self._pulse_intensity = value
        self.update()
    
    @pyqtProperty(float)
    def ring_scale_0(self):
        return self.ring_scales[0]
    
    @ring_scale_0.setter
    def ring_scale_0(self, value):
        self.ring_scales[0] = value
        self.update()
    
    @pyqtProperty(float)
    def ring_scale_1(self):
        return self.ring_scales[1]
    
    @ring_scale_1.setter
    def ring_scale_1(self, value):
        self.ring_scales[1] = value
        self.update()
    
    @pyqtProperty(float)
    def ring_scale_2(self):
        return self.ring_scales[2]
    
    @ring_scale_2.setter
    def ring_scale_2(self, value):
        self.ring_scales[2] = value
        self.update()
    
    def update_rotation(self):
        """Update rotation and particle positions"""
        self.rotation_angle += 1
        if self.rotation_angle >= 360:
            self.rotation_angle = 0
            
        # Update particles
        for particle in self.particle_positions:
            particle['angle'] += particle['speed']
            if particle['angle'] >= 2 * math.pi:
                particle['angle'] = 0
                
        self.update()
    
    def set_speaking_mode(self, speaking: bool):
        """Set speaking mode for reactive animation"""
        self.is_speaking = speaking

        if speaking:
            # Intense speaking animation - immediate response
            self.pulse_animation.stop()
            self.pulse_animation.setDuration(200)  # Even faster
            self.pulse_animation.setStartValue(1.0)
            self.pulse_animation.setEndValue(1.8)  # More intense
            self.pulse_animation.start()
            self.rotation_timer.setInterval(20)  # Much faster rotation
        else:
            # Calm idle animation - smooth transition back
            self.pulse_animation.stop()
            self.pulse_animation.setDuration(1500)  # Slightly faster return
            self.pulse_animation.setStartValue(0.4)
            self.pulse_animation.setEndValue(1.0)
            self.pulse_animation.start()
            self.rotation_timer.setInterval(50)  # Normal rotation
    
    def paintEvent(self, event):
        """Paint the JARVIS core visualization"""
        try:
            painter = QPainter(self)
            if not painter.isActive():
                return  # Prevent threading issues
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            center = QPointF(self.width() / 2, self.height() / 2)

            # Draw background glow
            self.draw_background_glow(painter, center)

            # Draw outer rings
            self.draw_outer_rings(painter, center)

            # Draw particles
            self.draw_particles(painter, center)

            # Draw central core
            self.draw_central_core(painter, center)

            # Draw energy lines
            self.draw_energy_lines(painter, center)

        except Exception as e:
            # Silently handle painting errors to prevent terminal spam
            pass
    
    def draw_background_glow(self, painter, center):
        """Draw the background glow effect"""
        gradient = QRadialGradient(center, 200)
        
        if self.is_speaking:
            gradient.setColorAt(0, QColor(0, 150, 255, 100))
            gradient.setColorAt(0.5, QColor(0, 100, 200, 50))
            gradient.setColorAt(1, QColor(0, 50, 100, 10))
        else:
            gradient.setColorAt(0, QColor(0, 255, 255, 60))
            gradient.setColorAt(0.5, QColor(0, 150, 255, 30))
            gradient.setColorAt(1, QColor(0, 100, 150, 5))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(center, 200, 200)
    
    def draw_outer_rings(self, painter, center):
        """Draw the outer rotating rings"""
        ring_radii = [140, 160, 180]
        
        for i, radius in enumerate(ring_radii):
            scale = self.ring_scales[i]
            actual_radius = radius * scale
            
            # Ring color based on speaking state
            if self.is_speaking:
                color = QColor(0, 150, 255, 150)
            else:
                color = QColor(0, 255, 255, 100)
            
            pen = QPen(color, 2)
            painter.setPen(pen)
            painter.setBrush(Qt.BrushStyle.NoBrush)
            
            # Draw ring segments
            for j in range(8):
                start_angle = (j * 45 + self.rotation_angle * (1 + i * 0.5)) % 360
                span_angle = 30
                
                rect = QRectF(center.x() - actual_radius, center.y() - actual_radius,
                             actual_radius * 2, actual_radius * 2)
                painter.drawArc(rect, int(start_angle * 16), int(span_angle * 16))
    
    def draw_particles(self, painter, center):
        """Draw floating particles"""
        for particle in self.particle_positions:
            x = center.x() + particle['radius'] * math.cos(particle['angle'])
            y = center.y() + particle['radius'] * math.sin(particle['angle'])
            
            # Particle color
            if self.is_speaking:
                color = QColor(100, 200, 255, int(particle['opacity'] * 255))
            else:
                color = QColor(0, 255, 255, int(particle['opacity'] * 200))
            
            painter.setBrush(QBrush(color))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawEllipse(QPointF(x, y), particle['size'], particle['size'])
    
    def draw_central_core(self, painter, center):
        """Draw the central AI core"""
        # Core gradient
        gradient = QRadialGradient(center, self.core_radius)
        
        intensity = self.pulse_intensity
        if self.is_speaking:
            # Bright, reactive core
            gradient.setColorAt(0, QColor(255, 255, 255, int(200 * intensity)))
            gradient.setColorAt(0.3, QColor(100, 200, 255, int(180 * intensity)))
            gradient.setColorAt(0.7, QColor(0, 150, 255, int(120 * intensity)))
            gradient.setColorAt(1, QColor(0, 100, 200, int(60 * intensity)))
        else:
            # Calm, pulsing core
            gradient.setColorAt(0, QColor(200, 255, 255, int(150 * intensity)))
            gradient.setColorAt(0.3, QColor(0, 255, 255, int(120 * intensity)))
            gradient.setColorAt(0.7, QColor(0, 200, 255, int(80 * intensity)))
            gradient.setColorAt(1, QColor(0, 150, 200, int(40 * intensity)))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(center, self.core_radius * intensity, self.core_radius * intensity)
        
        # Core outline
        outline_color = QColor(0, 255, 255, int(255 * intensity)) if not self.is_speaking else QColor(100, 200, 255, 255)
        painter.setPen(QPen(outline_color, 3))
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawEllipse(center, self.core_radius, self.core_radius)
    
    def draw_energy_lines(self, painter, center):
        """Draw energy lines radiating from core"""
        if not self.is_speaking:
            return
            
        painter.setPen(QPen(QColor(150, 200, 255, 150), 2))
        
        for i in range(6):
            angle = (i * 60 + self.rotation_angle * 2) * math.pi / 180
            start_radius = self.core_radius + 10
            end_radius = start_radius + 40 * self.pulse_intensity
            
            start_x = center.x() + start_radius * math.cos(angle)
            start_y = center.y() + start_radius * math.sin(angle)
            end_x = center.x() + end_radius * math.cos(angle)
            end_y = center.y() + end_radius * math.sin(angle)
            
            painter.drawLine(QPointF(start_x, start_y), QPointF(end_x, end_y))
