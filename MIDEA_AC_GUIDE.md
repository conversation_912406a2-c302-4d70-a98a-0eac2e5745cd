# 🌡️ JARVIS V6 Midea AC Control Guide

Your JARVIS V6 now includes full support for controlling your Midea Air Conditioner using natural language commands!

## ✨ Features

- **Voice Control**: Talk to JARVIS to control your AC naturally
- **Power Control**: Turn your AC on/off with simple commands
- **Temperature Control**: Set precise temperatures
- **Mode Control**: Switch between cooling, heating, auto, fan, and dry modes
- **Status Monitoring**: Check AC status and settings
- **Demo Mode**: Test commands without real AC (enabled by default)

## 🚀 Quick Start

### 1. Enable Smart Home System
1. Launch JARVIS V6
2. Click the **🏠 SMART HOME** button in the AI Systems panel
3. JARVIS will initialize with your Midea AC in demo mode

### 2. Try These AC Commands
```
"Turn on the AC"
"Turn off air conditioning"
"Set AC to 24 degrees"
"Set AC to cooling mode"
"Check AC status"
```

## 🗣️ Voice Commands for Your Midea AC

### 🔌 Power Control
- **Turn On**: 
  - "Turn on the AC"
  - "Switch on air conditioning"
  - "Turn on living room AC"
  - "Enable the AC"

- **Turn Off**:
  - "Turn off the AC"
  - "Switch off air conditioning"
  - "Turn off bedroom AC"
  - "Disable the AC"

### 🌡️ Temperature Control
- **Set Temperature**:
  - "Set AC to 24 degrees"
  - "Set air conditioning to 22 degrees"
  - "AC temperature 26"
  - "Set living room AC to 20 degrees"
  - "Air conditioning 18 degrees"

### 🔄 Mode Control
- **Cooling Mode**:
  - "Set AC to cooling mode"
  - "Cooling mode on the AC"
  - "Set AC to cool"

- **Heating Mode**:
  - "Set AC to heating mode"
  - "Heating mode on the AC"
  - "Set AC to heat"

- **Auto Mode**:
  - "Set AC to auto mode"
  - "Auto mode on the AC"
  - "Set AC to automatic"

- **Fan Mode**:
  - "Set AC to fan mode"
  - "Fan mode on the AC"
  - "Set AC to fan only"

- **Dry Mode**:
  - "Set AC to dry mode"
  - "Dry mode on the AC"
  - "Set AC to dehumidify"

### 📊 Status & Information
- **Check Status**:
  - "Status of the AC"
  - "Check AC status"
  - "How is the AC"
  - "What's the AC temperature"
  - "Show me AC settings"

## ⚙️ Configuration

### Current Setup (Demo Mode)
Your JARVIS is configured with demo Midea ACs:
- **Living Room AC**: 24°C target, cooling mode
- **Bedroom AC**: 22°C target, cooling mode

### Configuration File: `smart_home_config.json`
```json
{
  "platforms": {
    "midea_ac": {
      "enabled": true,
      "demo_mode": true,
      "devices": [
        {
          "id": "living_room_ac",
          "name": "Living Room AC",
          "room": "Living Room",
          "state": "off",
          "target_temperature": 24,
          "current_temperature": 22,
          "mode": "cool",
          "fan_speed": "auto"
        }
      ]
    }
  }
}
```

### Setting Up Real Midea AC Control

To control your actual Midea AC, you'll need to:

1. **Find Your AC's Network Details**
   - IP address on your local network
   - Model number and capabilities

2. **Update Configuration**
   ```json
   "midea_ac": {
     "enabled": true,
     "demo_mode": false,
     "devices": [
       {
         "id": "your_ac_id",
         "name": "Your Midea AC",
         "ip": "192.168.1.XXX",
         "room": "Your Room"
       }
     ]
   }
   ```

3. **Real Device Integration**
   - The current implementation includes demo mode
   - Real device control would require Midea cloud API or local protocol
   - Future updates will include full Midea integration

## 🎯 Example Commands & Responses

### Power Control
```
You: "Turn on the AC"
JARVIS: "✅ Turned on Living Room AC"

You: "Turn off air conditioning"
JARVIS: "✅ Turned off Living Room AC"
```

### Temperature Control
```
You: "Set AC to 24 degrees"
JARVIS: "🌡️ Set Living Room AC to 24°C"

You: "AC temperature 22"
JARVIS: "🌡️ Set Living Room AC to 22°C"
```

### Mode Control
```
You: "Set AC to cooling mode"
JARVIS: "🌡️ Set Living Room AC to cool mode"

You: "Auto mode on the AC"
JARVIS: "🌡️ Set Living Room AC to auto mode"
```

### Status Check
```
You: "Check AC status"
JARVIS: "🔴 Living Room AC: OFF
         🌡️ Target: 24°C
         🔄 Mode: cool"
```

## 🔧 Troubleshooting

### AC Commands Not Working
- Ensure Smart Home system is enabled (🏠 button pressed)
- Check that "midea_ac" is enabled in config
- Try different command variations

### Device Not Found
- Use "AC" or "air conditioning" in your commands
- Try "living room AC" or "bedroom AC" for specific units
- Check device names in configuration

### Temperature Not Setting
- Use format: "Set AC to [number] degrees"
- Temperature range typically 16-30°C
- AC automatically turns on when temperature is set

## 🎉 Advanced Features

### Voice Feedback
- JARVIS speaks responses when TTS is enabled
- Confirms all AC actions with voice
- Reports errors clearly

### Room-Specific Control
- "Turn on living room AC"
- "Set bedroom AC to 22 degrees"
- "Check office AC status"

### Smart Integration
- AC status appears in smart home overview
- Integrates with other smart devices
- Part of room-based control system

## 🔮 Coming Soon

Future enhancements for Midea AC control:
- **Real Device Integration**: Direct control of actual Midea ACs
- **Scheduling**: "Turn on AC at 6 PM"
- **Smart Scenes**: "Movie mode" with AC settings
- **Energy Monitoring**: Track AC usage
- **Advanced Controls**: Fan speed, swing, sleep mode

---

**Enjoy controlling your Midea AC with JARVIS V6! 🌡️✨**

*Your AC is now just a voice command away!*
