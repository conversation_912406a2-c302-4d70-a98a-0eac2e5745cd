#!/usr/bin/env python3
"""
Test Midea AC Commands for JARVIS V6
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from plugins.smart_home_plugin import SmartHomeManager
from ai.smart_home_commands import SmartHomeCommandProcessor

async def test_ac_commands():
    """Test AC-specific commands"""
    print("🌡️ JARVIS V6 Midea AC Command Test")
    print("=" * 50)
    
    # Initialize smart home manager
    manager = SmartHomeManager()
    await manager.initialize()
    
    # Initialize command processor
    processor = SmartHomeCommandProcessor()
    
    # AC-specific test commands
    ac_commands = [
        "turn on the ac",
        "turn off air conditioning",
        "switch on living room ac",
        "turn off bedroom ac",
        "set ac to 24 degrees",
        "set living room ac to 22 degrees",
        "ac temperature 26",
        "set ac to cooling mode",
        "set ac to heating mode",
        "cooling mode on the ac",
        "auto mode on living room ac",
        "status of the ac",
        "check ac status"
    ]
    
    print(f"🧪 Testing {len(ac_commands)} AC commands...")
    print()
    
    for i, command_text in enumerate(ac_commands, 1):
        print(f"{i:2d}. 🗣️ '{command_text}'")
        
        # Parse the command
        command = processor.parse_command(command_text)
        
        if command and command.confidence > 0.5:
            print(f"    ✅ Type: {command.command_type.value}")
            print(f"    🎯 Target: {command.target}")
            print(f"    💡 Value: {command.value}")
            print(f"    📊 Confidence: {command.confidence:.2f}")
            
            # Execute the command
            try:
                if command.command_type.value == "turn_on":
                    result = await manager.turn_on_device(command.target)
                    print(f"    🔄 Result: {'✅ Success' if result else '❌ Failed'}")
                
                elif command.command_type.value == "turn_off":
                    result = await manager.turn_off_device(command.target)
                    print(f"    🔄 Result: {'✅ Success' if result else '❌ Failed'}")
                
                elif command.command_type.value == "set_temperature":
                    device = manager.find_device_by_name(command.target)
                    if device:
                        platform_key = device.platform.lower().replace(" ", "_").replace("-", "_")
                        platform = manager.platforms.get(platform_key)
                        if platform and hasattr(platform, 'set_temperature'):
                            result = await platform.set_temperature(device.id, command.value)
                            print(f"    🔄 Result: {'✅ Temperature set' if result else '❌ Failed'}")
                        else:
                            print(f"    🔄 Result: ❌ Temperature control not supported")
                    else:
                        print(f"    🔄 Result: ❌ Device not found")
                
                elif command.command_type.value == "set_ac_mode":
                    device = manager.find_device_by_name(command.target)
                    if device:
                        platform_key = device.platform.lower().replace(" ", "_").replace("-", "_")
                        platform = manager.platforms.get(platform_key)
                        if platform and hasattr(platform, 'set_mode'):
                            result = await platform.set_mode(device.id, command.value)
                            print(f"    🔄 Result: {'✅ Mode set' if result else '❌ Failed'}")
                        else:
                            print(f"    🔄 Result: ❌ Mode control not supported")
                    else:
                        print(f"    🔄 Result: ❌ Device not found")
                
                elif command.command_type.value == "status":
                    device = manager.find_device_by_name(command.target)
                    if device:
                        state_icon = "🟢" if device.state.value == "on" else "🔴"
                        print(f"    🔄 Status: {state_icon} {device.name}")
                        if hasattr(device, 'target_temperature') and device.target_temperature:
                            print(f"        🌡️ Target: {device.target_temperature}°C")
                        if hasattr(device, 'mode') and device.mode:
                            print(f"        🔄 Mode: {device.mode}")
                    else:
                        print(f"    🔄 Result: ❌ Device not found")
                
            except Exception as e:
                print(f"    ❌ Error: {e}")
        
        else:
            print(f"    ❌ Could not parse command (confidence: {command.confidence if command else 0:.2f})")
        
        print()
    
    # Show final AC status
    print("🏠 Final AC Status:")
    print("=" * 30)
    for device in manager.devices.values():
        if device.device_type.value == "ac":
            state_icon = "🟢" if device.state.value == "on" else "🔴"
            print(f"{state_icon} {device.name} ({device.room})")
            if hasattr(device, 'target_temperature') and device.target_temperature:
                print(f"   🌡️ Target: {device.target_temperature}°C")
            if hasattr(device, 'current_temperature') and device.current_temperature:
                print(f"   📊 Current: {device.current_temperature}°C")
            if hasattr(device, 'mode') and device.mode:
                print(f"   🔄 Mode: {device.mode}")
            if hasattr(device, 'fan_speed') and device.fan_speed:
                print(f"   💨 Fan: {device.fan_speed}")

def show_ac_examples():
    """Show example AC commands"""
    print("\n📝 Midea AC Voice Commands:")
    print("=" * 50)
    
    examples = [
        "🔌 Power Control:",
        "  • Turn on the AC",
        "  • Turn off air conditioning",
        "  • Switch on living room AC",
        "  • Turn off bedroom AC",
        "",
        "🌡️ Temperature Control:",
        "  • Set AC to 24 degrees",
        "  • Set living room AC to 22 degrees",
        "  • AC temperature 26",
        "  • Air conditioning 20 degrees",
        "",
        "🔄 Mode Control:",
        "  • Set AC to cooling mode",
        "  • Set AC to heating mode",
        "  • Cooling mode on the AC",
        "  • Auto mode on living room AC",
        "  • Fan mode on bedroom AC",
        "  • Dry mode on the AC",
        "",
        "📊 Status Check:",
        "  • Status of the AC",
        "  • Check AC status",
        "  • How is the living room AC",
        "  • What's the AC temperature"
    ]
    
    for example in examples:
        print(example)

async def main():
    """Main test function"""
    try:
        await test_ac_commands()
        show_ac_examples()
        
        print(f"\n🎉 AC command testing completed!")
        print(f"💡 Your Midea AC is now ready to be controlled by JARVIS!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
