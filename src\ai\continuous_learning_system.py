"""
Continuous Learning System for JARVIS V6
========================================
Enables JARVIS to continuously learn from interactions and improve over time

Features:
- Real-time learning from conversations
- Automatic knowledge extraction and storage
- Pattern recognition and adaptation
- Continuous skill development
- Performance optimization
- Behavioral learning and improvement
"""

import os
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import re

@dataclass
class LearningEvent:
    timestamp: str
    event_type: str  # 'conversation', 'error', 'success', 'pattern', 'feedback'
    content: str
    context: Dict[str, Any]
    importance: float
    learned_concepts: List[str]
    improvement_suggestions: List[str]

@dataclass
class SkillMetric:
    skill_name: str
    proficiency_level: float  # 0.0 to 1.0
    usage_count: int
    success_rate: float
    last_used: str
    improvement_areas: List[str]

class ContinuousLearningSystem:
    """System for continuous learning and self-improvement"""
    
    def __init__(self, config=None):
        self.config = config
        self.learning_active = True
        self.learning_events = deque(maxlen=10000)  # Keep last 10k events
        self.skills = {}
        self.patterns = defaultdict(list)
        self.knowledge_graph = defaultdict(set)
        self.learning_rate = 0.1
        
        # Learning directories
        self.learning_dir = "data/continuous_learning"
        self.skills_file = os.path.join(self.learning_dir, "skills.json")
        self.patterns_file = os.path.join(self.learning_dir, "patterns.json")
        self.knowledge_file = os.path.join(self.learning_dir, "knowledge_graph.json")
        
        # Ensure directories exist
        os.makedirs(self.learning_dir, exist_ok=True)
        
        # Load existing learning data
        self._load_learning_data()
        
        # Start continuous learning thread
        self.learning_thread = threading.Thread(target=self._continuous_learning_loop, daemon=True)
        self.learning_thread.start()
        
        print("🧠 Continuous Learning System initialized")
        print(f"📊 Loaded {len(self.skills)} skills and {len(self.patterns)} patterns")
    
    def _load_learning_data(self):
        """Load existing learning data"""
        try:
            # Load skills
            if os.path.exists(self.skills_file):
                with open(self.skills_file, 'r') as f:
                    skills_data = json.load(f)
                    self.skills = {name: SkillMetric(**data) for name, data in skills_data.items()}
            
            # Load patterns
            if os.path.exists(self.patterns_file):
                with open(self.patterns_file, 'r') as f:
                    self.patterns = defaultdict(list, json.load(f))
            
            # Load knowledge graph
            if os.path.exists(self.knowledge_file):
                with open(self.knowledge_file, 'r') as f:
                    knowledge_data = json.load(f)
                    self.knowledge_graph = defaultdict(set, {k: set(v) for k, v in knowledge_data.items()})
                    
        except Exception as e:
            print(f"⚠️ Error loading learning data: {e}")
    
    def _save_learning_data(self):
        """Save learning data to files"""
        try:
            # Save skills
            skills_data = {name: asdict(skill) for name, skill in self.skills.items()}
            with open(self.skills_file, 'w') as f:
                json.dump(skills_data, f, indent=2)
            
            # Save patterns
            with open(self.patterns_file, 'w') as f:
                json.dump(dict(self.patterns), f, indent=2)
            
            # Save knowledge graph
            knowledge_data = {k: list(v) for k, v in self.knowledge_graph.items()}
            with open(self.knowledge_file, 'w') as f:
                json.dump(knowledge_data, f, indent=2)
                
        except Exception as e:
            print(f"❌ Error saving learning data: {e}")
    
    def learn_from_conversation(self, user_message: str, ai_response: str, 
                              context: Dict[str, Any] = None) -> List[str]:
        """Learn from a conversation interaction"""
        if not self.learning_active:
            return []
        
        try:
            # Extract concepts and patterns
            concepts = self._extract_concepts(user_message, ai_response)
            patterns = self._identify_patterns(user_message, ai_response)
            improvements = self._suggest_improvements(user_message, ai_response, context)
            
            # Create learning event
            event = LearningEvent(
                timestamp=datetime.now().isoformat(),
                event_type='conversation',
                content=f"User: {user_message[:200]}... AI: {ai_response[:200]}...",
                context=context or {},
                importance=self._calculate_importance(user_message, ai_response),
                learned_concepts=concepts,
                improvement_suggestions=improvements
            )
            
            self.learning_events.append(event)
            
            # Update skills and knowledge
            self._update_skills(concepts, True)
            self._update_knowledge_graph(concepts)
            self._store_patterns(patterns)
            
            return concepts
            
        except Exception as e:
            print(f"❌ Error in conversation learning: {e}")
            return []
    
    def learn_from_error(self, error_type: str, error_details: str, 
                        context: Dict[str, Any] = None) -> List[str]:
        """Learn from errors to prevent future occurrences"""
        try:
            # Analyze error for learning opportunities
            concepts = [f"error_handling_{error_type}", "debugging", "error_prevention"]
            improvements = [
                f"Implement better error handling for {error_type}",
                "Add validation checks",
                "Improve error recovery mechanisms"
            ]
            
            # Create learning event
            event = LearningEvent(
                timestamp=datetime.now().isoformat(),
                event_type='error',
                content=f"Error: {error_type} - {error_details[:200]}",
                context=context or {},
                importance=0.8,  # Errors are important for learning
                learned_concepts=concepts,
                improvement_suggestions=improvements
            )
            
            self.learning_events.append(event)
            
            # Update skills (mark as needing improvement)
            self._update_skills(concepts, False)
            
            return improvements
            
        except Exception as e:
            print(f"❌ Error in error learning: {e}")
            return []
    
    def learn_from_success(self, task_type: str, success_details: str,
                          context: Dict[str, Any] = None) -> List[str]:
        """Learn from successful interactions to reinforce good patterns"""
        try:
            concepts = [f"success_{task_type}", "effective_response", "user_satisfaction"]
            
            event = LearningEvent(
                timestamp=datetime.now().isoformat(),
                event_type='success',
                content=f"Success: {task_type} - {success_details[:200]}",
                context=context or {},
                importance=0.6,
                learned_concepts=concepts,
                improvement_suggestions=[]
            )
            
            self.learning_events.append(event)
            self._update_skills(concepts, True)
            
            return concepts
            
        except Exception as e:
            print(f"❌ Error in success learning: {e}")
            return []
    
    def _extract_concepts(self, user_message: str, ai_response: str) -> List[str]:
        """Extract key concepts from conversation"""
        concepts = []
        
        # Technical concepts
        tech_patterns = [
            r'\b(python|javascript|html|css|sql|api|database|server|client)\b',
            r'\b(machine learning|ai|neural network|algorithm|data science)\b',
            r'\b(programming|coding|development|software|application)\b',
            r'\b(error|bug|debug|fix|solution|problem)\b'
        ]
        
        text = (user_message + " " + ai_response).lower()
        
        for pattern in tech_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            concepts.extend(matches)
        
        # Domain-specific concepts
        if any(word in text for word in ['train', 'learn', 'study', 'knowledge']):
            concepts.append('learning_request')
        
        if any(word in text for word in ['help', 'assist', 'support', 'guide']):
            concepts.append('assistance_request')
        
        if any(word in text for word in ['create', 'build', 'make', 'develop']):
            concepts.append('creation_request')
        
        return list(set(concepts))  # Remove duplicates
    
    def _identify_patterns(self, user_message: str, ai_response: str) -> List[str]:
        """Identify conversation patterns"""
        patterns = []
        
        # Question patterns
        if user_message.strip().endswith('?'):
            patterns.append('question_pattern')
        
        # Request patterns
        if any(word in user_message.lower() for word in ['can you', 'please', 'help me']):
            patterns.append('polite_request_pattern')
        
        # Technical query patterns
        if any(word in user_message.lower() for word in ['how to', 'what is', 'explain']):
            patterns.append('explanation_request_pattern')
        
        # Code-related patterns
        if any(word in user_message.lower() for word in ['code', 'function', 'class', 'method']):
            patterns.append('code_request_pattern')
        
        return patterns
    
    def _suggest_improvements(self, user_message: str, ai_response: str, 
                            context: Dict[str, Any]) -> List[str]:
        """Suggest improvements based on interaction"""
        improvements = []
        
        # Response length analysis
        if len(ai_response) < 50:
            improvements.append("Provide more detailed responses")
        elif len(ai_response) > 2000:
            improvements.append("Make responses more concise")
        
        # Context awareness
        if context and 'previous_topic' in context:
            if context['previous_topic'] not in ai_response.lower():
                improvements.append("Better context awareness and continuity")
        
        # Technical accuracy
        if any(word in user_message.lower() for word in ['code', 'programming', 'technical']):
            improvements.append("Enhance technical knowledge and code examples")
        
        return improvements
    
    def _calculate_importance(self, user_message: str, ai_response: str) -> float:
        """Calculate importance score for learning event"""
        importance = 0.5  # Base importance
        
        # Technical content is more important
        if any(word in (user_message + ai_response).lower() 
               for word in ['code', 'programming', 'error', 'bug', 'algorithm']):
            importance += 0.2
        
        # Learning requests are important
        if any(word in user_message.lower() for word in ['learn', 'train', 'teach']):
            importance += 0.3
        
        # Long conversations indicate engagement
        if len(user_message) + len(ai_response) > 500:
            importance += 0.1
        
        return min(importance, 1.0)
    
    def _update_skills(self, concepts: List[str], success: bool):
        """Update skill metrics based on learning"""
        for concept in concepts:
            if concept not in self.skills:
                self.skills[concept] = SkillMetric(
                    skill_name=concept,
                    proficiency_level=0.1,
                    usage_count=0,
                    success_rate=0.0,
                    last_used=datetime.now().isoformat(),
                    improvement_areas=[]
                )
            
            skill = self.skills[concept]
            skill.usage_count += 1
            skill.last_used = datetime.now().isoformat()
            
            # Update proficiency and success rate
            if success:
                skill.proficiency_level = min(1.0, skill.proficiency_level + self.learning_rate)
                skill.success_rate = (skill.success_rate * (skill.usage_count - 1) + 1.0) / skill.usage_count
            else:
                skill.success_rate = (skill.success_rate * (skill.usage_count - 1) + 0.0) / skill.usage_count
                if skill.proficiency_level < 0.8:
                    skill.improvement_areas.append(f"Needs improvement in {concept}")
    
    def _update_knowledge_graph(self, concepts: List[str]):
        """Update knowledge graph with concept relationships"""
        for i, concept1 in enumerate(concepts):
            for concept2 in concepts[i+1:]:
                self.knowledge_graph[concept1].add(concept2)
                self.knowledge_graph[concept2].add(concept1)
    
    def _store_patterns(self, patterns: List[str]):
        """Store identified patterns"""
        timestamp = datetime.now().isoformat()
        for pattern in patterns:
            self.patterns[pattern].append(timestamp)
            # Keep only recent patterns (last 1000)
            if len(self.patterns[pattern]) > 1000:
                self.patterns[pattern] = self.patterns[pattern][-1000:]
    
    def _continuous_learning_loop(self):
        """Continuous learning background process"""
        while self.learning_active:
            try:
                # Analyze recent learning events
                self._analyze_learning_trends()
                
                # Save learning data periodically
                self._save_learning_data()
                
                # Sleep for 5 minutes before next analysis
                time.sleep(300)
                
            except Exception as e:
                print(f"❌ Error in continuous learning loop: {e}")
                time.sleep(60)  # Wait 1 minute on error
    
    def _analyze_learning_trends(self):
        """Analyze learning trends and suggest improvements"""
        if len(self.learning_events) < 10:
            return
        
        # Analyze recent events (last hour)
        recent_events = [
            event for event in self.learning_events
            if datetime.fromisoformat(event.timestamp) > datetime.now() - timedelta(hours=1)
        ]
        
        if recent_events:
            # Identify trending concepts
            concept_counts = defaultdict(int)
            for event in recent_events:
                for concept in event.learned_concepts:
                    concept_counts[concept] += 1
            
            # Focus learning on trending concepts
            trending = sorted(concept_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            if trending:
                print(f"🧠 Trending learning concepts: {[concept for concept, count in trending]}")
    
    def get_learning_summary(self) -> Dict[str, Any]:
        """Get summary of learning progress"""
        total_events = len(self.learning_events)
        total_skills = len(self.skills)
        avg_proficiency = sum(skill.proficiency_level for skill in self.skills.values()) / max(total_skills, 1)
        
        # Top skills
        top_skills = sorted(self.skills.values(), key=lambda x: x.proficiency_level, reverse=True)[:5]
        
        # Recent learning
        recent_events = [
            event for event in self.learning_events
            if datetime.fromisoformat(event.timestamp) > datetime.now() - timedelta(hours=24)
        ]
        
        return {
            'total_learning_events': total_events,
            'total_skills': total_skills,
            'average_proficiency': avg_proficiency,
            'top_skills': [{'name': skill.skill_name, 'proficiency': skill.proficiency_level} for skill in top_skills],
            'recent_events_24h': len(recent_events),
            'learning_active': self.learning_active,
            'knowledge_connections': len(self.knowledge_graph),
            'pattern_types': len(self.patterns)
        }
    
    def stop_learning(self):
        """Stop continuous learning"""
        self.learning_active = False
        self._save_learning_data()
        print("🛑 Continuous learning stopped")
    
    def resume_learning(self):
        """Resume continuous learning"""
        self.learning_active = True
        print("▶️ Continuous learning resumed")
