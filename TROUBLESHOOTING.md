# 🔧 JARVIS V6 Troubleshooting Guide

## ✅ **MAIN.PY ISSUE FIXED**

The issue with running `main.py` has been resolved! Missing imports have been added to the Qt compatibility layer.

---

## 🚀 **How to Start JARVIS V6**

### **Method 1: Using the Launcher Script (Recommended)**
```bash
python start_jarvis.py
```

### **Method 2: Using the Batch File (Windows)**
```bash
start_jarvis.bat
```

### **Method 3: Direct Python Execution**
```bash
# Activate virtual environment first
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/Mac

# Then run JARVIS
python main.py
```

### **Method 4: Using the Executable**
```bash
dist\JARVIS_V6.exe
```

---

## 🐛 **Common Issues and Solutions**

### **1. Import Errors**

#### **Problem**: `ImportError: cannot import name 'QDialog' from 'src.gui.qt_compat'`
**✅ FIXED**: Added missing Qt imports to `qt_compat.py`

#### **Problem**: `NameError: name 'Optional' is not defined`
**✅ FIXED**: Added missing typing imports to `main_window.py`

#### **Problem**: `ModuleNotFoundError: No module named 'PySide6'`
**Solution**:
```bash
pip install PySide6
```

### **2. Virtual Environment Issues**

#### **Problem**: Virtual environment not activated
**Solution**:
```bash
# Windows
.venv\Scripts\activate

# Linux/Mac
source .venv/bin/activate
```

#### **Problem**: Virtual environment doesn't exist
**Solution**:
```bash
python -m venv .venv
.venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### **3. Python Version Issues**

#### **Problem**: Python version too old
**Solution**: JARVIS V6 requires Python 3.8 or higher
```bash
python --version  # Check your version
```

### **4. Graphics/QPainter Warnings**

#### **Problem**: QPainter warnings in console
```
QPainter::begin: Paint device returned engine == 0, type: 3
QPainter::setCompositionMode: Painter not active
```
**Status**: ⚠️ Cosmetic issue only - doesn't affect functionality
**Impact**: None - JARVIS works perfectly despite these warnings

### **5. Missing Dependencies**

#### **Problem**: Various import errors for packages
**Solution**: Install all requirements
```bash
pip install -r requirements.txt
```

**Or install individually**:
```bash
pip install PySide6 requests psutil pygame numpy
```

### **6. Ollama Connection Issues**

#### **Problem**: Ollama not responding
**Solution**:
1. Check if Ollama is running: `ollama list`
2. Start Ollama service if needed
3. Pull Mixtral model: `ollama pull mixtral:8x7b`

### **7. TTS (Text-to-Speech) Issues**

#### **Problem**: ElevenLabs TTS not working
**Solution**:
1. Check API key in `.env` file
2. Verify internet connection
3. Check ElevenLabs service status

---

## 📋 **System Requirements**

### **Minimum Requirements**
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **OS**: Windows 10/11, Linux, macOS

### **Recommended Requirements**
- **Python**: 3.10 or higher
- **RAM**: 16GB for optimal performance
- **Storage**: 5GB free space
- **GPU**: Optional, for faster AI processing

---

## 🔍 **Diagnostic Commands**

### **Check Python Version**
```bash
python --version
```

### **Check Virtual Environment**
```bash
# Should show .venv path when activated
which python  # Linux/Mac
where python   # Windows
```

### **Check Installed Packages**
```bash
pip list
```

### **Check JARVIS Components**
```bash
python -c "from src.gui.qt_compat import QApplication; print('Qt imports OK')"
python -c "from src.gui.main_window import JarvisMainWindow; print('Main window OK')"
```

### **Check Ollama Connection**
```bash
curl http://localhost:11434/api/tags
```

---

## 🛠️ **Step-by-Step Troubleshooting**

### **If JARVIS Won't Start**

1. **Check Python Version**
   ```bash
   python --version
   ```
   Must be 3.8 or higher

2. **Activate Virtual Environment**
   ```bash
   .venv\Scripts\activate  # Windows
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Test Qt Imports**
   ```bash
   python -c "from PySide6.QtWidgets import QApplication; print('PySide6 OK')"
   ```

5. **Run JARVIS**
   ```bash
   python main.py
   ```

### **If You Get Import Errors**

1. **Check if in virtual environment**
   ```bash
   which python  # Should show .venv path
   ```

2. **Reinstall packages**
   ```bash
   pip uninstall PySide6
   pip install PySide6
   ```

3. **Clear Python cache**
   ```bash
   find . -name "*.pyc" -delete  # Linux/Mac
   del /s *.pyc                  # Windows
   ```

---

## 📞 **Getting Help**

### **Debug Information to Collect**

When reporting issues, please include:

1. **Python Version**: `python --version`
2. **Operating System**: Windows/Linux/macOS version
3. **Virtual Environment Status**: Active/Inactive
4. **Installed Packages**: `pip list`
5. **Full Error Message**: Complete traceback
6. **JARVIS Version**: V6 with Enhanced AI

### **Common Solutions Summary**

| Issue | Solution |
|-------|----------|
| Import errors | Install missing packages with `pip install` |
| Qt not found | `pip install PySide6` |
| Virtual env issues | Activate with `.venv\Scripts\activate` |
| Python too old | Upgrade to Python 3.8+ |
| Ollama not working | Start Ollama service and pull models |
| Graphics warnings | Ignore - cosmetic only |

---

## ✅ **Verification Checklist**

Before running JARVIS, verify:

- [ ] Python 3.8+ installed
- [ ] Virtual environment created and activated
- [ ] All dependencies installed (`pip install -r requirements.txt`)
- [ ] PySide6 working (`python -c "import PySide6"`)
- [ ] Ollama running (optional, for local AI)
- [ ] In correct directory (contains `main.py`)

---

## 🎉 **Success Indicators**

When JARVIS starts successfully, you should see:

```
🚀 Starting JARVIS V6 AI Assistant...
✅ Application initialized
🧠 Enhanced Memory System initialized for [username]
📊 Session ID: session_[timestamp]
🧠 Semantic Understanding System initialized - Autonomous intelligence active!
🚀 Enhanced AI Processor initialized with multi-provider support
ElevenLabs TTS Plugin initialized successfully
✅ JARVIS V6 interface ready!
🎉 Enjoy your AI assistant!
```

**Note**: QPainter warnings are normal and don't affect functionality.

---

## 🔮 **Advanced Troubleshooting**

### **Clean Installation**
If all else fails, try a clean installation:

1. **Delete virtual environment**
   ```bash
   rmdir /s .venv  # Windows
   rm -rf .venv    # Linux/Mac
   ```

2. **Create new virtual environment**
   ```bash
   python -m venv .venv
   .venv\Scripts\activate
   ```

3. **Install fresh dependencies**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **Test JARVIS**
   ```bash
   python main.py
   ```

### **Alternative Python Installation**
If Python issues persist:

1. **Download Python 3.10** from python.org
2. **Install with "Add to PATH" option**
3. **Create new virtual environment**
4. **Install JARVIS dependencies**

---

## 📊 **Status Summary**

**✅ RESOLVED ISSUES:**
- Missing Qt imports in `qt_compat.py`
- Missing typing imports in `main_window.py`
- Import path issues
- Virtual environment activation

**⚠️ KNOWN COSMETIC ISSUES:**
- QPainter warnings (don't affect functionality)
- Some graphics rendering messages

**🚀 WORKING FEATURES:**
- Main application startup
- Enhanced AI processing
- Semantic understanding
- Progress tracking
- Memory system
- TTS integration
- Smart home controls

**JARVIS V6 is now fully functional and ready to use!** 🎊
