#!/usr/bin/env python3
"""
Test Enhanced Memory System for JARVIS V6
=========================================

Test the new conversation tracking, knowledge database, and user profiling features.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai.advanced_memory import AdvancedMemorySystem

def test_enhanced_memory():
    """Test the enhanced memory system"""
    print("🧠 Testing Enhanced JARVIS Memory System")
    print("=" * 60)
    
    # Initialize memory system
    memory = AdvancedMemorySystem()
    
    print(f"✅ Memory system initialized for user: {memory.current_user}")
    print(f"📅 Session ID: {memory.current_session_id}")
    print()
    
    # Test conversation storage
    print("💬 Testing Conversation Storage...")
    conversations = [
        ("Hello JARVIS, how are you today?", "Hello Austin! I'm functioning optimally and ready to assist you with your AI development projects."),
        ("Can you help me with my Midea AC integration?", "Absolutely! I remember you have a Midea AC at ************. The network connectivity is working, but we need authentication keys for full control."),
        ("What's my preferred AI model?", "Based on our conversations, you prefer Mixtral 8x7B via Ollama API with local-only architecture for your AI assistant development."),
        ("Remember that I like short responses", "Understood! I'll keep my responses concise and to the point. Your preference for shorter responses has been noted."),
        ("What smart home devices do I have?", "You have a Midea AC unit (IP: ************, MAC: 9c:c9:eb:67:bf:b1) that we've successfully integrated with JARVIS for voice control.")
    ]
    
    for user_msg, jarvis_response in conversations:
        memory.learn_from_conversation(user_msg, jarvis_response)
        print(f"  ✅ Stored: '{user_msg[:30]}...'")
    
    print()
    
    # Test knowledge storage
    print("📚 Testing Knowledge Database...")
    knowledge_entries = [
        {
            "topic": "Austin's Development Preferences",
            "content": "Austin prefers modular code structure, PyQt6 GUI with JARVIS-style HUD design, and ElevenLabs TTS for voice output.",
            "source": "conversation_analysis",
            "confidence": 0.9,
            "tags": ["user_preferences", "development", "gui_design"]
        },
        {
            "topic": "Smart Home Configuration",
            "content": "Austin's smart home setup includes a Midea AC unit with network connectivity established but requiring authentication for full control.",
            "source": "technical_analysis",
            "confidence": 0.95,
            "tags": ["smart_home", "midea_ac", "technical_specs"]
        },
        {
            "topic": "AI Assistant Requirements",
            "content": "Austin wants AI assistants to be faster, shorter in responses, with local-only architecture using Ollama and Mixtral 8x7B model.",
            "source": "user_feedback",
            "confidence": 0.9,
            "tags": ["ai_preferences", "performance", "local_ai"]
        }
    ]
    
    for entry in knowledge_entries:
        knowledge_id = memory.store_knowledge(**entry)
        print(f"  ✅ Stored knowledge: '{entry['topic']}'")
    
    print()
    
    # Test memory retrieval
    print("🔍 Testing Memory Retrieval...")
    
    # Test conversation history
    recent_conversations = memory.get_conversation_history(limit=3)
    print(f"📝 Recent conversations: {len(recent_conversations)} found")
    for conv in recent_conversations:
        print(f"  • User: {conv.user_message[:40]}...")
        print(f"    JARVIS: {conv.jarvis_response[:40]}...")
    
    print()
    
    # Test knowledge search
    knowledge_results = memory.search_knowledge("AI preferences", limit=3)
    print(f"🔍 Knowledge search results: {len(knowledge_results)} found")
    for knowledge in knowledge_results:
        print(f"  • {knowledge.topic}: {knowledge.content[:50]}...")
    
    print()
    
    # Test contextual response data
    print("🎯 Testing Contextual Response Data...")
    context_data = memory.get_contextual_response_data("What are my AI preferences?")
    
    print(f"👤 User Profile: {context_data['user_profile']['name']}")
    print(f"💬 Recent Conversations: {len(context_data['recent_conversations'])}")
    print(f"📚 Relevant Knowledge: {len(context_data['relevant_knowledge'])}")
    print(f"🧠 Relevant Memories: {len(context_data['relevant_memories'])}")
    
    print()
    
    # Test memory statistics
    print("📊 Testing Memory Statistics...")
    stats = memory.get_memory_stats()
    
    print(f"🧠 Total Memories: {stats['total_memories']}")
    print(f"💬 Total Conversations: {stats['total_conversations']}")
    print(f"📚 Knowledge Entries: {stats['total_knowledge_entries']}")
    print(f"✅ Verified Knowledge: {stats['verified_knowledge']}")
    print(f"🎯 Current Session Messages: {stats['current_session_messages']}")
    print(f"📅 Total Sessions: {stats['total_sessions']}")
    print(f"⭐ Average Importance: {stats['average_importance']}")
    
    print()
    
    # Test personalized context
    print("🎭 Testing Personalized Context...")
    personalized_context = memory.get_personalized_context()
    print("Context for AI responses:")
    print(personalized_context[:300] + "..." if len(personalized_context) > 300 else personalized_context)
    
    print()
    
    # Test user profile
    print("👤 Testing User Profile...")
    profile = memory.user_profile
    print(f"Name: {profile.name}")
    print(f"Communication Style: {profile.communication_style}")
    print(f"Learning Style: {profile.learning_style}")
    print(f"Favorite Topics: {', '.join(profile.favorite_topics)}")
    print(f"Goals & Interests: {', '.join(profile.goals_and_interests)}")
    
    print()
    print("🎉 Enhanced Memory System Test Complete!")
    print("=" * 60)
    
    # Show final summary
    final_stats = memory.get_memory_stats()
    print(f"📈 Final Statistics:")
    print(f"   • Memories: {final_stats['total_memories']}")
    print(f"   • Conversations: {final_stats['total_conversations']}")
    print(f"   • Knowledge: {final_stats['total_knowledge_entries']}")
    print(f"   • Session: {final_stats['session_id'][:16]}...")
    
    return memory

def test_memory_learning():
    """Test memory learning capabilities"""
    print("\n🎓 Testing Memory Learning Capabilities")
    print("=" * 50)
    
    memory = AdvancedMemorySystem()
    
    # Test preference learning
    print("📝 Testing Preference Learning...")
    preference_conversations = [
        ("I prefer short responses", "Got it! I'll keep my responses brief."),
        ("I like detailed technical explanations", "I'll provide comprehensive technical details when explaining concepts."),
        ("Please use more emojis in responses", "Absolutely! I'll add more emojis to make responses more engaging! 😊"),
    ]
    
    for user_msg, jarvis_response in preference_conversations:
        memory.learn_from_conversation(user_msg, jarvis_response)
        print(f"  ✅ Learned from: '{user_msg}'")
    
    # Check if preferences were updated
    updated_profile = memory.user_profile
    print(f"📊 Updated preferences: {updated_profile.response_preferences}")
    
    print()
    
    # Test emotional pattern tracking
    print("😊 Testing Emotional Pattern Tracking...")
    emotional_conversations = [
        ("I'm really excited about this new AI system!", "That's wonderful! Your enthusiasm is contagious."),
        ("This is amazing work on the memory system", "Thank you! I'm glad you're pleased with the enhanced capabilities."),
        ("I love how JARVIS is evolving", "It's great to see your satisfaction with the improvements!"),
    ]
    
    for user_msg, jarvis_response in emotional_conversations:
        memory.learn_from_conversation(user_msg, jarvis_response)
        emotional_analysis = memory.analyze_emotional_context(user_msg)
        print(f"  😊 Emotional valence: {emotional_analysis['valence']:.2f} for '{user_msg[:30]}...'")
    
    print()
    print("🎓 Memory Learning Test Complete!")

if __name__ == "__main__":
    try:
        # Test basic memory system
        memory_system = test_enhanced_memory()
        
        # Test learning capabilities
        test_memory_learning()
        
        print(f"\n✅ All tests completed successfully!")
        print(f"🗃️ Memory database location: {memory_system.db_path}")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
