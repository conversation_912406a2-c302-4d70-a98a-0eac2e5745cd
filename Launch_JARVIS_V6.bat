@echo off
title JARVIS V6 AI Assistant
color 0A
echo.
echo ========================================
echo    🤖 JARVIS V6 AI Assistant 🤖
echo ========================================
echo.
echo Starting JARVIS V6...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.10 or higher.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check if required packages are installed
echo 📦 Checking dependencies...
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo Installing PySide6...
    pip install PySide6
)

python -c "import pygame" >nul 2>&1
if errorlevel 1 (
    echo Installing pygame...
    pip install pygame
)

python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo Installing requests...
    pip install requests
)

echo ✅ Dependencies ready!
echo.
echo 🚀 Launching JARVIS V6...
echo.

REM Launch JARVIS
python jarvis_clean.py

echo.
echo JARVIS V6 has closed.
pause
