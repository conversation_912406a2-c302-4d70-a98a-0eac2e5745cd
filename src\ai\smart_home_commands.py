"""
JARVIS V6 Smart Home Command Processor
Handles natural language commands for smart home control
"""

import re
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class CommandType(Enum):
    TURN_ON = "turn_on"
    TURN_OFF = "turn_off"
    SET_BRIGHTNESS = "set_brightness"
    SET_TEMPERATURE = "set_temperature"
    STATUS = "status"
    ROOM_CONTROL = "room_control"
    SCENE = "scene"

@dataclass
class SmartHomeCommand:
    """Represents a parsed smart home command"""
    command_type: CommandType
    target: str  # Device name or room name
    value: Optional[Any] = None  # Brightness, temperature, etc.
    room: Optional[str] = None
    confidence: float = 0.0

class SmartHomeCommandProcessor:
    """Processes natural language commands for smart home control"""
    
    def __init__(self):
        self.command_patterns = self._build_command_patterns()
        self.device_aliases = self._build_device_aliases()
        self.room_aliases = self._build_room_aliases()
    
    def _build_command_patterns(self) -> Dict[CommandType, List[str]]:
        """Build regex patterns for different command types"""
        return {
            CommandType.TURN_ON: [
                r"turn on (?:the )?(.+)",
                r"switch on (?:the )?(.+)",
                r"enable (?:the )?(.+)",
                r"activate (?:the )?(.+)",
                r"start (?:the )?(.+)",
                r"(.+) on",
                r"lights? on in (?:the )?(.+)",
                r"turn (?:the )?(.+) on"
            ],
            CommandType.TURN_OFF: [
                r"turn off (?:the )?(.+)",
                r"switch off (?:the )?(.+)",
                r"disable (?:the )?(.+)",
                r"deactivate (?:the )?(.+)",
                r"stop (?:the )?(.+)",
                r"(.+) off",
                r"lights? off in (?:the )?(.+)",
                r"turn (?:the )?(.+) off"
            ],
            CommandType.SET_BRIGHTNESS: [
                r"set (?:the )?(.+) (?:brightness )?to (\d+)%?",
                r"dim (?:the )?(.+) to (\d+)%?",
                r"brighten (?:the )?(.+) to (\d+)%?",
                r"make (?:the )?(.+) (\d+)%? bright",
                r"(.+) brightness (\d+)%?"
            ],
            CommandType.SET_TEMPERATURE: [
                r"set (?:the )?(.+) to (\d+) degrees?",
                r"set temperature to (\d+) in (?:the )?(.+)",
                r"make it (\d+) degrees? in (?:the )?(.+)",
                r"thermostat (\d+) degrees?"
            ],
            CommandType.STATUS: [
                r"status of (?:the )?(.+)",
                r"what(?:'s| is) the status of (?:the )?(.+)",
                r"how (?:is|are) (?:the )?(.+)",
                r"check (?:the )?(.+)",
                r"show me (?:the )?(.+) status",
                r"smart home status",
                r"device status",
                r"home status"
            ],
            CommandType.ROOM_CONTROL: [
                r"turn (?:on|off) (?:all )?(?:the )?lights? in (?:the )?(.+)",
                r"(?:turn on|turn off|switch on|switch off) (?:the )?(.+) room",
                r"lights? (on|off) in (?:the )?(.+)",
                r"(.+) room (on|off)",
                r"all lights? (on|off) in (?:the )?(.+)"
            ]
        }
    
    def _build_device_aliases(self) -> Dict[str, List[str]]:
        """Build common device name aliases"""
        return {
            "living_room_light": ["living room light", "main light", "ceiling light"],
            "bedroom_light": ["bedroom light", "bed light", "night light"],
            "kitchen_light": ["kitchen light", "cooking light"],
            "office_light": ["office light", "desk light", "work light"],
            "thermostat": ["thermostat", "temperature", "heating", "cooling", "ac"],
            "fan": ["fan", "ceiling fan", "air circulation"],
            "tv": ["tv", "television", "smart tv"],
            "speaker": ["speaker", "music", "sound system"]
        }
    
    def _build_room_aliases(self) -> Dict[str, List[str]]:
        """Build common room name aliases"""
        return {
            "living_room": ["living room", "lounge", "family room", "main room"],
            "bedroom": ["bedroom", "bed room", "master bedroom", "sleeping room"],
            "kitchen": ["kitchen", "cooking area", "dining room"],
            "office": ["office", "study", "work room", "den"],
            "bathroom": ["bathroom", "bath", "restroom"],
            "garage": ["garage", "car port"]
        }
    
    def parse_command(self, text: str) -> Optional[SmartHomeCommand]:
        """Parse natural language text into a smart home command"""
        text = text.lower().strip()
        
        # Remove common prefixes
        prefixes = ["jarvis", "hey jarvis", "ok jarvis", "please", "can you", "could you"]
        for prefix in prefixes:
            if text.startswith(prefix):
                text = text[len(prefix):].strip()
        
        # Try to match against each command type
        for command_type, patterns in self.command_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    return self._create_command_from_match(command_type, match, text)
        
        return None
    
    def _create_command_from_match(self, command_type: CommandType, match: re.Match, original_text: str) -> SmartHomeCommand:
        """Create a SmartHomeCommand from a regex match"""
        groups = match.groups()
        
        if command_type in [CommandType.TURN_ON, CommandType.TURN_OFF]:
            target = groups[0] if groups else ""
            
            # Check if this is a room control command
            if any(word in original_text for word in ["room", "all lights", "everything"]):
                room = self._normalize_room_name(target)
                if room:
                    return SmartHomeCommand(
                        command_type=CommandType.ROOM_CONTROL,
                        target=room,
                        value="on" if command_type == CommandType.TURN_ON else "off",
                        confidence=0.9
                    )
            
            # Regular device control
            device = self._normalize_device_name(target)
            return SmartHomeCommand(
                command_type=command_type,
                target=device or target,
                confidence=0.8 if device else 0.6
            )
        
        elif command_type == CommandType.SET_BRIGHTNESS:
            if len(groups) >= 2:
                device = self._normalize_device_name(groups[0])
                brightness = int(groups[1])
                return SmartHomeCommand(
                    command_type=command_type,
                    target=device or groups[0],
                    value=max(0, min(100, brightness)),
                    confidence=0.9
                )
        
        elif command_type == CommandType.SET_TEMPERATURE:
            if len(groups) >= 2:
                if groups[1].isdigit():  # Temperature first, then room
                    temp = int(groups[0])
                    room = self._normalize_room_name(groups[1]) if len(groups) > 1 else None
                else:  # Room first, then temperature
                    temp = int(groups[1])
                    room = self._normalize_room_name(groups[0])
                
                return SmartHomeCommand(
                    command_type=command_type,
                    target="thermostat",
                    value=temp,
                    room=room,
                    confidence=0.9
                )
        
        elif command_type == CommandType.STATUS:
            target = groups[0] if groups else "all"
            if target in ["smart home", "device", "home", "all"]:
                target = "all"
            else:
                target = self._normalize_device_name(target) or target
            
            return SmartHomeCommand(
                command_type=command_type,
                target=target,
                confidence=0.8
            )
        
        elif command_type == CommandType.ROOM_CONTROL:
            if len(groups) >= 2:
                room = self._normalize_room_name(groups[0])
                action = groups[1]
                return SmartHomeCommand(
                    command_type=command_type,
                    target=room or groups[0],
                    value=action,
                    confidence=0.9 if room else 0.7
                )
        
        # Default fallback
        return SmartHomeCommand(
            command_type=command_type,
            target=groups[0] if groups else "",
            confidence=0.5
        )
    
    def _normalize_device_name(self, name: str) -> Optional[str]:
        """Normalize device name using aliases"""
        name_lower = name.lower().strip()
        for canonical_name, aliases in self.device_aliases.items():
            if any(alias in name_lower for alias in aliases):
                return canonical_name
        return None
    
    def _normalize_room_name(self, name: str) -> Optional[str]:
        """Normalize room name using aliases"""
        name_lower = name.lower().strip()
        for canonical_name, aliases in self.room_aliases.items():
            if any(alias in name_lower for alias in aliases):
                return canonical_name
        return None
    
    def get_command_suggestions(self, partial_text: str) -> List[str]:
        """Get command suggestions based on partial input"""
        suggestions = []
        text_lower = partial_text.lower()
        
        if "turn" in text_lower or "switch" in text_lower:
            suggestions.extend([
                "turn on the living room light",
                "turn off all lights",
                "switch on the bedroom light"
            ])
        
        if "set" in text_lower or "brightness" in text_lower:
            suggestions.extend([
                "set living room light to 50%",
                "dim the bedroom light to 25%"
            ])
        
        if "status" in text_lower or "check" in text_lower:
            suggestions.extend([
                "check smart home status",
                "status of all devices"
            ])
        
        if "temperature" in text_lower or "thermostat" in text_lower:
            suggestions.extend([
                "set temperature to 72 degrees",
                "thermostat 68 degrees"
            ])
        
        return suggestions[:5]  # Return top 5 suggestions

# Example usage and testing
def test_command_processor():
    """Test the command processor with various inputs"""
    processor = SmartHomeCommandProcessor()
    
    test_commands = [
        "turn on the living room light",
        "switch off bedroom light",
        "set kitchen light to 75%",
        "dim the office light to 25%",
        "turn on all lights in the living room",
        "lights off in bedroom",
        "set temperature to 72 degrees",
        "check smart home status",
        "what's the status of the thermostat",
        "turn off everything in the kitchen"
    ]
    
    print("🏠 Smart Home Command Processor Test")
    print("=" * 50)
    
    for command_text in test_commands:
        command = processor.parse_command(command_text)
        if command:
            print(f"Input: '{command_text}'")
            print(f"  Type: {command.command_type.value}")
            print(f"  Target: {command.target}")
            print(f"  Value: {command.value}")
            print(f"  Room: {command.room}")
            print(f"  Confidence: {command.confidence:.2f}")
            print()
        else:
            print(f"❌ Could not parse: '{command_text}'")

if __name__ == "__main__":
    test_command_processor()
