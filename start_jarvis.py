#!/usr/bin/env python3
"""
JARVIS V6 Launcher
==================

Simple launcher script for JARVIS V6 AI Assistant.
This script handles environment setup and error checking.
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    return True

def check_virtual_environment():
    """Check if virtual environment exists and is activated"""
    venv_path = Path(".venv")
    
    if not venv_path.exists():
        print("❌ Virtual environment not found")
        print("Please create one with: python -m venv .venv")
        return False
    
    # Check if we're in the virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment is active")
        return True
    else:
        print("⚠️ Virtual environment exists but not activated")
        print("Please activate it with:")
        print("  Windows: .venv\\Scripts\\activate")
        print("  Linux/Mac: source .venv/bin/activate")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = {
        'PySide6': 'PySide6',
        'requests': 'requests',
        'psutil': 'psutil',
        'pygame': 'pygame',
        'numpy': 'numpy'
    }

    missing_packages = []

    for display_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(display_name)

    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install them with:")
        print(f"  pip install {' '.join(missing_packages)}")
        return False

    print("✅ All required dependencies are installed")
    return True

def start_jarvis():
    """Start JARVIS V6"""
    print("🚀 Starting JARVIS V6 AI Assistant...")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check virtual environment
    if not check_virtual_environment():
        return False
    
    # Check dependencies
    if not check_dependencies():
        return False
    
    print("\n🎯 All checks passed! Starting JARVIS...")
    print("-" * 50)
    
    try:
        # Import and run main
        from main import main
        main()
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're in the correct directory and all files are present")
        return False
    
    except Exception as e:
        print(f"❌ Error starting JARVIS: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main launcher function"""
    print("🤖 JARVIS V6 Launcher")
    print("=" * 30)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    success = start_jarvis()
    
    if not success:
        print("\n❌ Failed to start JARVIS V6")
        print("Please check the error messages above and try again")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
