#!/usr/bin/env python3
"""
Test TTS timing and animation sync
"""

import sys
import time

def test_tts_timing():
    """Test TTS generation and playback timing"""
    print("Testing TTS timing and animation sync...")
    
    try:
        # Import our modules
        from src.gui.qt_compat import QApplication
        from src.core.config import Config
        from src.plugins.elevenlabs_tts_plugin import ElevenLabsTTSPlugin
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Load config
        config = Config.load_from_env()
        
        # Test message
        test_message = "Hello, this is a test of the improved TTS timing system."
        
        print(f"Testing message: '{test_message}'")
        print("Timing TTS generation and playback...")
        
        # Create TTS plugin
        tts_plugin = ElevenLabsTTSPlugin()
        
        # Track timing
        generation_started = False
        speech_started = False
        speech_finished = False
        
        start_time = time.time()
        generation_start_time = None
        speech_start_time = None
        speech_end_time = None
        
        def on_generation_started():
            nonlocal generation_started, generation_start_time
            generation_started = True
            generation_start_time = time.time()
            print(f"⚡ TTS generation started at {generation_start_time - start_time:.2f}s")
        
        def on_speech_started():
            nonlocal speech_started, speech_start_time
            speech_started = True
            speech_start_time = time.time()
            print(f"🔊 Speech playback started at {speech_start_time - start_time:.2f}s")
            if generation_start_time:
                generation_time = speech_start_time - generation_start_time
                print(f"   Generation took: {generation_time:.2f}s")
        
        def on_speech_finished():
            nonlocal speech_finished, speech_end_time
            speech_finished = True
            speech_end_time = time.time()
            total_time = speech_end_time - start_time
            speech_duration = speech_end_time - speech_start_time if speech_start_time else 0
            print(f"✅ Speech finished at {total_time:.2f}s")
            print(f"   Speech duration: {speech_duration:.2f}s")
        
        def on_error(error):
            print(f"❌ TTS Error: {error}")
        
        # Connect signals
        tts_plugin.signals.speech_started.connect(on_speech_started)
        tts_plugin.signals.speech_finished.connect(on_speech_finished)
        tts_plugin.signals.speech_error.connect(on_error)
        
        # Start TTS
        print("🚀 Starting TTS...")
        tts_plugin.speak(test_message)
        
        # Wait for completion
        timeout = 30
        while not speech_finished and time.time() - start_time < timeout:
            app.processEvents()
            time.sleep(0.1)
        
        if speech_finished:
            total_time = speech_end_time - start_time
            print(f"\n🎉 TTS test completed successfully!")
            print(f"Total time: {total_time:.2f}s")
            
            if generation_start_time and speech_start_time:
                generation_time = speech_start_time - generation_start_time
                print(f"Generation time: {generation_time:.2f}s")
                
                if generation_time < 3.0:
                    print("✅ TTS generation is fast (< 3s)")
                else:
                    print("⚠️ TTS generation is slow (> 3s)")
            
            return True
        else:
            print("❌ TTS test timed out")
            return False
            
    except Exception as e:
        print(f"❌ TTS test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tts_timing()
    if success:
        print("\n🎉 TTS timing is optimized!")
    else:
        print("\n❌ TTS timing needs improvement!")
    
    sys.exit(0 if success else 1)
