"""
System HUD Plugin for Jarvis V6
Displays system information in a HUD overlay
"""

import psutil
from datetime import datetime
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QProgressBar, QFrame
from PyQt6.QtCore import <PERSON><PERSON>ime<PERSON>, Qt
from PyQt6.QtGui import <PERSON>Font
from typing import Dict, Any
from src.plugins.plugin_manager import HUDPlugin
from src.core.config import Config

class SystemHUDWidget(QWidget):
    """Widget that displays system information"""
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        self.init_ui()
        
    def init_ui(self):
        """Initialize the HUD widget UI"""
        self.setFixedSize(250, 300)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # Main frame
        main_frame = QFrame()
        main_frame.setObjectName("hudFrame")
        frame_layout = QVBoxLayout(main_frame)
        
        # Title
        title_label = QLabel("SYSTEM STATUS")
        title_label.setObjectName("hudTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(title_label)
        
        # Time
        self.time_label = QLabel()
        self.time_label.setObjectName("hudTime")
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(self.time_label)
        
        # CPU Usage
        cpu_label = QLabel("CPU Usage:")
        cpu_label.setObjectName("hudLabel")
        frame_layout.addWidget(cpu_label)
        
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setObjectName("hudProgressBar")
        frame_layout.addWidget(self.cpu_bar)
        
        self.cpu_text = QLabel("0%")
        self.cpu_text.setObjectName("hudValue")
        frame_layout.addWidget(self.cpu_text)
        
        # Memory Usage
        mem_label = QLabel("Memory Usage:")
        mem_label.setObjectName("hudLabel")
        frame_layout.addWidget(mem_label)
        
        self.mem_bar = QProgressBar()
        self.mem_bar.setObjectName("hudProgressBar")
        frame_layout.addWidget(self.mem_bar)
        
        self.mem_text = QLabel("0%")
        self.mem_text.setObjectName("hudValue")
        frame_layout.addWidget(self.mem_text)
        
        # Disk Usage
        disk_label = QLabel("Disk Usage:")
        disk_label.setObjectName("hudLabel")
        frame_layout.addWidget(disk_label)
        
        self.disk_bar = QProgressBar()
        self.disk_bar.setObjectName("hudProgressBar")
        frame_layout.addWidget(self.disk_bar)
        
        self.disk_text = QLabel("0%")
        self.disk_text.setObjectName("hudValue")
        frame_layout.addWidget(self.disk_text)
        
        layout.addWidget(main_frame)
        
        # Apply styling
        self.setStyleSheet(f"""
            #hudFrame {{
                background-color: rgba(10, 10, 10, 0.9);
                border: 2px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 10px;
            }}
            
            #hudTitle {{
                color: {self.config.THEME_PRIMARY_COLOR};
                font-size: 14px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                margin: 5px;
            }}
            
            #hudTime {{
                color: {self.config.THEME_SECONDARY_COLOR};
                font-size: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                margin: 5px;
            }}
            
            #hudLabel {{
                color: {self.config.THEME_TEXT_COLOR};
                font-size: 10px;
                font-weight: bold;
                margin: 2px;
            }}
            
            #hudValue {{
                color: {self.config.THEME_ACCENT_COLOR};
                font-size: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                margin: 2px;
            }}
            
            #hudProgressBar {{
                border: 1px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 3px;
                background-color: rgba(0, 0, 0, 0.5);
                height: 15px;
            }}
            
            #hudProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.config.THEME_PRIMARY_COLOR},
                    stop:1 {self.config.THEME_SECONDARY_COLOR});
                border-radius: 2px;
            }}
        """)
        
    def update_data(self, data: Dict[str, Any]):
        """Update the HUD with new data"""
        # Update time
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(current_time)
        
        # Update CPU
        cpu_percent = data.get('cpu_percent', 0)
        self.cpu_bar.setValue(int(cpu_percent))
        self.cpu_text.setText(f"{cpu_percent:.1f}%")
        
        # Update Memory
        mem_percent = data.get('memory_percent', 0)
        self.mem_bar.setValue(int(mem_percent))
        self.mem_text.setText(f"{mem_percent:.1f}%")
        
        # Update Disk
        disk_percent = data.get('disk_percent', 0)
        self.disk_bar.setValue(int(disk_percent))
        self.disk_text.setText(f"{disk_percent:.1f}%")

class SystemHUDPlugin(HUDPlugin):
    """HUD plugin that displays system information"""
    
    def __init__(self):
        self.config = None
        self.widget = None
        self.update_timer = None
        
    @property
    def name(self) -> str:
        return "System HUD"
        
    @property
    def version(self) -> str:
        return "1.0.0"
        
    @property
    def description(self) -> str:
        return "Displays system information in a HUD overlay"
        
    def initialize(self, config: Config) -> bool:
        """Initialize the plugin"""
        try:
            self.config = config
            return True
        except Exception as e:
            print(f"Failed to initialize System HUD Plugin: {e}")
            return False
            
    def cleanup(self) -> None:
        """Cleanup plugin resources"""
        if self.update_timer:
            self.update_timer.stop()
        if self.widget:
            self.widget.close()
            
    def create_widget(self) -> QWidget:
        """Create the HUD widget"""
        if not self.widget:
            self.widget = SystemHUDWidget(self.config)
            
            # Setup update timer
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_system_data)
            self.update_timer.start(1000)  # Update every second
            
        return self.widget
        
    def update_data(self, data: Dict[str, Any]) -> None:
        """Update HUD data"""
        if self.widget:
            self.widget.update_data(data)
            
    def update_system_data(self):
        """Update system data automatically"""
        try:
            # Get system information
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            data = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk.percent
            }
            
            self.update_data(data)
            
        except Exception as e:
            print(f"Failed to update system data: {e}")
            
    def show_widget(self):
        """Show the HUD widget"""
        if self.widget:
            self.widget.show()
            
    def hide_widget(self):
        """Hide the HUD widget"""
        if self.widget:
            self.widget.hide()
