{"platforms": {"philips_hue": {"enabled": false, "bridge_ip": "*************", "username": "your_hue_username_here", "description": "Philips Hue smart lights. Get username by pressing bridge button and running discovery."}, "tplink_kasa": {"enabled": false, "devices": [{"name": "Living Room Light", "ip": "*************", "type": "light", "room": "Living Room"}, {"name": "Bedroom Light", "ip": "*************", "type": "light", "room": "Bedroom"}, {"name": "Kitchen Switch", "ip": "*************", "type": "switch", "room": "Kitchen"}], "description": "TP-Link Kasa smart devices. Update IP addresses to match your devices."}, "midea_ac": {"enabled": true, "demo_mode": true, "devices": [{"id": "living_room_ac", "name": "Living Room AC", "room": "Living Room", "state": "off", "target_temperature": 24, "current_temperature": 22, "mode": "cool", "fan_speed": "auto"}, {"id": "bedroom_ac", "name": "Bedroom AC", "room": "Bedroom", "state": "off", "target_temperature": 22, "current_temperature": 24, "mode": "cool", "fan_speed": "low"}], "description": "Midea Air Conditioners. Set demo_mode to false for real device control."}, "demo_mode": {"enabled": true, "devices": [{"name": "Demo Living Room Light", "type": "light", "room": "Living Room", "state": "off"}, {"name": "Demo Bedroom Light", "type": "light", "room": "Bedroom", "state": "off"}, {"name": "Demo Kitchen Light", "type": "light", "room": "Kitchen", "state": "on"}, {"name": "Demo Office Light", "type": "light", "room": "Office", "state": "off"}, {"name": "Demo Thermostat", "type": "thermostat", "room": "Living Room", "state": "on", "temperature": 72}], "description": "Demo mode for testing smart home commands without real devices."}}, "rooms": {"Living Room": {"description": "Main living area", "aliases": ["living room", "lounge", "family room", "main room"]}, "Bedroom": {"description": "Master bedroom", "aliases": ["bedroom", "bed room", "master bedroom", "sleeping room"]}, "Kitchen": {"description": "Kitchen and dining area", "aliases": ["kitchen", "cooking area", "dining room"]}, "Office": {"description": "Home office", "aliases": ["office", "study", "work room", "den"]}, "Bathroom": {"description": "Bathroom", "aliases": ["bathroom", "bath", "restroom"]}, "Garage": {"description": "Garage", "aliases": ["garage", "car port"]}}, "voice_commands": {"enabled": true, "examples": ["Turn on the living room light", "Switch off bedroom light", "Set kitchen light to 75%", "Dim the office light to 25%", "Turn on all lights in the living room", "Lights off in bedroom", "Set temperature to 72 degrees", "Check smart home status", "What's the status of the thermostat"]}, "settings": {"auto_discovery": true, "response_timeout": 5, "retry_attempts": 3, "voice_feedback": true, "status_updates": true}}