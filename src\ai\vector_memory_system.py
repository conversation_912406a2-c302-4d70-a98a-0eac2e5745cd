"""
Vector Memory System for JARVIS V6
=================================
Advanced vector-based memory and similarity search system

Features:
- Vector embeddings for semantic similarity
- FAISS-like similarity search
- Memory consolidation and retrieval
- Contextual memory clustering
- Long-term memory formation
"""

import os
import json
import sqlite3
import numpy as np
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import threading
import time

@dataclass
class MemoryVector:
    content: str
    embedding: List[float]
    timestamp: str
    context: Dict[str, Any]
    importance: float
    access_count: int = 0
    last_accessed: Optional[str] = None

@dataclass
class MemoryCluster:
    cluster_id: str
    centroid: List[float]
    memories: List[str]  # Memory IDs
    topic: str
    strength: float
    last_updated: str

class VectorMemorySystem:
    """Advanced vector-based memory system with semantic search"""
    
    def __init__(self, config=None):
        self.config = config
        self.memory_db_path = "data/memory/vector_memory.db"
        self.embeddings_cache = {}
        self.memory_vectors = {}
        self.clusters = {}
        
        # Memory parameters
        self.embedding_dim = 384  # Sentence transformer dimension
        self.max_memories = 10000
        self.cluster_threshold = 0.7
        self.importance_decay = 0.95
        
        # Initialize database
        self._init_memory_database()
        
        # Load existing memories
        self._load_memories()
        
        # Start memory consolidation process
        self.consolidation_thread = threading.Thread(target=self._memory_consolidation_loop, daemon=True)
        self.consolidation_thread.start()
        
        print("🧠 Vector Memory System initialized")
        print(f"📊 Loaded {len(self.memory_vectors)} memories in {len(self.clusters)} clusters")
    
    def _init_memory_database(self):
        """Initialize the vector memory database"""
        os.makedirs(os.path.dirname(self.memory_db_path), exist_ok=True)
        
        with sqlite3.connect(self.memory_db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS memory_vectors (
                    id TEXT PRIMARY KEY,
                    content TEXT NOT NULL,
                    embedding BLOB NOT NULL,
                    timestamp TEXT NOT NULL,
                    context TEXT,
                    importance REAL NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TEXT
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS memory_clusters (
                    cluster_id TEXT PRIMARY KEY,
                    centroid BLOB NOT NULL,
                    memories TEXT NOT NULL,
                    topic TEXT NOT NULL,
                    strength REAL NOT NULL,
                    last_updated TEXT NOT NULL
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS memory_associations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    memory1_id TEXT NOT NULL,
                    memory2_id TEXT NOT NULL,
                    association_strength REAL NOT NULL,
                    association_type TEXT NOT NULL,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            conn.commit()
    
    def _load_memories(self):
        """Load existing memories from database"""
        try:
            with sqlite3.connect(self.memory_db_path) as conn:
                # Load memory vectors
                cursor = conn.execute('SELECT * FROM memory_vectors')
                for row in cursor.fetchall():
                    memory_id, content, embedding_blob, timestamp, context, importance, access_count, last_accessed = row
                    
                    # Deserialize embedding
                    embedding = np.frombuffer(embedding_blob, dtype=np.float32).tolist()
                    
                    memory = MemoryVector(
                        content=content,
                        embedding=embedding,
                        timestamp=timestamp,
                        context=json.loads(context) if context else {},
                        importance=importance,
                        access_count=access_count,
                        last_accessed=last_accessed
                    )
                    
                    self.memory_vectors[memory_id] = memory
                
                # Load clusters
                cursor = conn.execute('SELECT * FROM memory_clusters')
                for row in cursor.fetchall():
                    cluster_id, centroid_blob, memories_json, topic, strength, last_updated = row
                    
                    centroid = np.frombuffer(centroid_blob, dtype=np.float32).tolist()
                    memories = json.loads(memories_json)
                    
                    cluster = MemoryCluster(
                        cluster_id=cluster_id,
                        centroid=centroid,
                        memories=memories,
                        topic=topic,
                        strength=strength,
                        last_updated=last_updated
                    )
                    
                    self.clusters[cluster_id] = cluster
                    
        except Exception as e:
            print(f"❌ Error loading memories: {e}")
    
    def create_embedding(self, text: str) -> List[float]:
        """Create embedding for text (simplified - in production use sentence transformers)"""
        # Simplified embedding creation (in production, use proper sentence transformers)
        # This is a placeholder that creates a basic hash-based embedding
        
        # Check cache first
        text_hash = hashlib.md5(text.encode()).hexdigest()
        if text_hash in self.embeddings_cache:
            return self.embeddings_cache[text_hash]
        
        # Create simple embedding based on text characteristics
        words = text.lower().split()
        embedding = np.zeros(self.embedding_dim)
        
        for i, word in enumerate(words[:50]):  # Limit to first 50 words
            # Simple hash-based embedding
            word_hash = hash(word) % self.embedding_dim
            embedding[word_hash] += 1.0 / (i + 1)  # Weight by position
        
        # Normalize
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
        
        embedding_list = embedding.tolist()
        self.embeddings_cache[text_hash] = embedding_list
        
        return embedding_list
    
    def store_memory(self, content: str, context: Dict[str, Any] = None, importance: float = 0.5) -> str:
        """Store a new memory with vector embedding"""
        try:
            # Create memory ID
            memory_id = hashlib.sha256(f"{content}{datetime.now().isoformat()}".encode()).hexdigest()[:16]
            
            # Create embedding
            embedding = self.create_embedding(content)
            
            # Create memory object
            memory = MemoryVector(
                content=content,
                embedding=embedding,
                timestamp=datetime.now().isoformat(),
                context=context or {},
                importance=importance
            )
            
            # Store in memory
            self.memory_vectors[memory_id] = memory
            
            # Store in database
            with sqlite3.connect(self.memory_db_path) as conn:
                embedding_blob = np.array(embedding, dtype=np.float32).tobytes()
                conn.execute('''
                    INSERT OR REPLACE INTO memory_vectors 
                    (id, content, embedding, timestamp, context, importance)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    memory_id,
                    content,
                    embedding_blob,
                    memory.timestamp,
                    json.dumps(memory.context),
                    memory.importance
                ))
                conn.commit()
            
            # Update clusters
            self._update_clusters(memory_id, memory)
            
            return memory_id
            
        except Exception as e:
            print(f"❌ Error storing memory: {e}")
            return ""
    
    def retrieve_similar_memories(self, query: str, limit: int = 5, threshold: float = 0.5) -> List[Tuple[str, MemoryVector, float]]:
        """Retrieve memories similar to query"""
        try:
            query_embedding = self.create_embedding(query)
            similarities = []
            
            for memory_id, memory in self.memory_vectors.items():
                # Calculate cosine similarity
                similarity = self._cosine_similarity(query_embedding, memory.embedding)
                
                if similarity >= threshold:
                    similarities.append((memory_id, memory, similarity))
            
            # Sort by similarity and importance
            similarities.sort(key=lambda x: x[2] * x[1].importance, reverse=True)
            
            # Update access counts
            for memory_id, memory, _ in similarities[:limit]:
                memory.access_count += 1
                memory.last_accessed = datetime.now().isoformat()
                self._update_memory_access(memory_id, memory)
            
            return similarities[:limit]
            
        except Exception as e:
            print(f"❌ Error retrieving similar memories: {e}")
            return []
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            a = np.array(vec1)
            b = np.array(vec2)
            
            dot_product = np.dot(a, b)
            norm_a = np.linalg.norm(a)
            norm_b = np.linalg.norm(b)
            
            if norm_a == 0 or norm_b == 0:
                return 0.0
            
            return dot_product / (norm_a * norm_b)
            
        except Exception as e:
            print(f"❌ Error calculating similarity: {e}")
            return 0.0
    
    def _update_clusters(self, memory_id: str, memory: MemoryVector):
        """Update memory clusters with new memory"""
        try:
            best_cluster = None
            best_similarity = 0.0
            
            # Find best matching cluster
            for cluster_id, cluster in self.clusters.items():
                similarity = self._cosine_similarity(memory.embedding, cluster.centroid)
                if similarity > best_similarity and similarity >= self.cluster_threshold:
                    best_similarity = similarity
                    best_cluster = cluster_id
            
            if best_cluster:
                # Add to existing cluster
                cluster = self.clusters[best_cluster]
                cluster.memories.append(memory_id)
                cluster.strength = (cluster.strength + memory.importance) / 2
                cluster.last_updated = datetime.now().isoformat()
                
                # Update centroid
                self._update_cluster_centroid(best_cluster)
            else:
                # Create new cluster
                cluster_id = f"cluster_{len(self.clusters):04d}"
                new_cluster = MemoryCluster(
                    cluster_id=cluster_id,
                    centroid=memory.embedding.copy(),
                    memories=[memory_id],
                    topic=self._extract_topic(memory.content),
                    strength=memory.importance,
                    last_updated=datetime.now().isoformat()
                )
                self.clusters[cluster_id] = new_cluster
            
            # Save cluster to database
            self._save_cluster(best_cluster or cluster_id)
            
        except Exception as e:
            print(f"❌ Error updating clusters: {e}")
    
    def _update_cluster_centroid(self, cluster_id: str):
        """Update cluster centroid based on member memories"""
        try:
            cluster = self.clusters[cluster_id]
            if not cluster.memories:
                return
            
            # Calculate new centroid as average of member embeddings
            embeddings = []
            for memory_id in cluster.memories:
                if memory_id in self.memory_vectors:
                    embeddings.append(self.memory_vectors[memory_id].embedding)
            
            if embeddings:
                centroid = np.mean(embeddings, axis=0).tolist()
                cluster.centroid = centroid
                
        except Exception as e:
            print(f"❌ Error updating cluster centroid: {e}")
    
    def _extract_topic(self, content: str) -> str:
        """Extract topic from content (simplified)"""
        words = content.lower().split()
        # Simple topic extraction - in production, use NLP techniques
        common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'}
        meaningful_words = [word for word in words if word not in common_words and len(word) > 2]
        
        if meaningful_words:
            return ' '.join(meaningful_words[:3])  # First 3 meaningful words
        return 'general'
    
    def _save_cluster(self, cluster_id: str):
        """Save cluster to database"""
        try:
            if cluster_id not in self.clusters:
                return
            
            cluster = self.clusters[cluster_id]
            
            with sqlite3.connect(self.memory_db_path) as conn:
                centroid_blob = np.array(cluster.centroid, dtype=np.float32).tobytes()
                conn.execute('''
                    INSERT OR REPLACE INTO memory_clusters 
                    (cluster_id, centroid, memories, topic, strength, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    cluster_id,
                    centroid_blob,
                    json.dumps(cluster.memories),
                    cluster.topic,
                    cluster.strength,
                    cluster.last_updated
                ))
                conn.commit()
                
        except Exception as e:
            print(f"❌ Error saving cluster: {e}")
    
    def _update_memory_access(self, memory_id: str, memory: MemoryVector):
        """Update memory access statistics in database"""
        try:
            with sqlite3.connect(self.memory_db_path) as conn:
                conn.execute('''
                    UPDATE memory_vectors 
                    SET access_count = ?, last_accessed = ?
                    WHERE id = ?
                ''', (memory.access_count, memory.last_accessed, memory_id))
                conn.commit()
                
        except Exception as e:
            print(f"❌ Error updating memory access: {e}")
    
    def _memory_consolidation_loop(self):
        """Background memory consolidation process"""
        while True:
            try:
                # Decay importance of old memories
                cutoff_date = datetime.now() - timedelta(days=7)
                
                for memory_id, memory in self.memory_vectors.items():
                    memory_date = datetime.fromisoformat(memory.timestamp)
                    if memory_date < cutoff_date:
                        memory.importance *= self.importance_decay
                
                # Remove very low importance memories if over limit
                if len(self.memory_vectors) > self.max_memories:
                    sorted_memories = sorted(
                        self.memory_vectors.items(),
                        key=lambda x: x[1].importance * x[1].access_count
                    )
                    
                    # Remove bottom 10%
                    to_remove = len(sorted_memories) // 10
                    for memory_id, _ in sorted_memories[:to_remove]:
                        del self.memory_vectors[memory_id]
                        
                        # Remove from database
                        with sqlite3.connect(self.memory_db_path) as conn:
                            conn.execute('DELETE FROM memory_vectors WHERE id = ?', (memory_id,))
                            conn.commit()
                
                # Sleep for 1 hour
                time.sleep(3600)
                
            except Exception as e:
                print(f"❌ Memory consolidation error: {e}")
                time.sleep(1800)  # Wait 30 minutes on error
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory system statistics"""
        try:
            total_memories = len(self.memory_vectors)
            total_clusters = len(self.clusters)
            
            # Calculate average importance
            avg_importance = sum(m.importance for m in self.memory_vectors.values()) / max(total_memories, 1)
            
            # Recent memories (last 24 hours)
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_memories = sum(
                1 for m in self.memory_vectors.values()
                if datetime.fromisoformat(m.timestamp) > recent_cutoff
            )
            
            # Top clusters by strength
            top_clusters = sorted(
                [(c.topic, c.strength, len(c.memories)) for c in self.clusters.values()],
                key=lambda x: x[1],
                reverse=True
            )[:5]
            
            return {
                'total_memories': total_memories,
                'total_clusters': total_clusters,
                'recent_memories_24h': recent_memories,
                'average_importance': avg_importance,
                'top_clusters': top_clusters,
                'embedding_dimension': self.embedding_dim,
                'cache_size': len(self.embeddings_cache)
            }
            
        except Exception as e:
            print(f"❌ Error getting memory stats: {e}")
            return {'error': str(e)}
    
    def search_memories_by_context(self, context_filter: Dict[str, Any], limit: int = 10) -> List[Tuple[str, MemoryVector]]:
        """Search memories by context attributes"""
        try:
            results = []
            
            for memory_id, memory in self.memory_vectors.items():
                match = True
                for key, value in context_filter.items():
                    if key not in memory.context or memory.context[key] != value:
                        match = False
                        break
                
                if match:
                    results.append((memory_id, memory))
            
            # Sort by importance and recency
            results.sort(key=lambda x: (x[1].importance, x[1].timestamp), reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            print(f"❌ Error searching memories by context: {e}")
            return []
