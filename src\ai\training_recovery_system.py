"""
Training Recovery System for JARVIS V6
=====================================
Handles training session failures and code improvement recovery

Features:
- Training session recovery
- Code improvement retry mechanism
- Backup and restore functionality
- Error analysis and resolution
- Manual training completion
"""

import os
import json
import shutil
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class TrainingSession:
    id: str
    topic: str
    start_time: str
    end_time: Optional[str]
    duration_hours: float
    status: str  # 'completed', 'failed', 'recovered'
    knowledge_gained: List[str]
    improvements_applied: int
    errors: List[str]

class TrainingRecoverySystem:
    """Recovery system for failed training sessions"""
    
    def __init__(self, config=None):
        self.config = config
        self.recovery_dir = "data/training_recovery"
        self.sessions_file = os.path.join(self.recovery_dir, "sessions.json")
        self.failed_sessions = []
        
        # Ensure recovery directory exists
        os.makedirs(self.recovery_dir, exist_ok=True)
        
        # Load previous sessions
        self._load_sessions()
        
        print("🔄 Training Recovery System initialized")
    
    def _load_sessions(self):
        """Load previous training sessions"""
        try:
            if os.path.exists(self.sessions_file):
                with open(self.sessions_file, 'r') as f:
                    data = json.load(f)
                    self.failed_sessions = [
                        TrainingSession(**session) for session in data.get('failed_sessions', [])
                    ]
                print(f"📂 Loaded {len(self.failed_sessions)} failed training sessions")
        except Exception as e:
            print(f"❌ Error loading training sessions: {e}")
            self.failed_sessions = []
    
    def _save_sessions(self):
        """Save training sessions to file"""
        try:
            data = {
                'failed_sessions': [
                    {
                        'id': session.id,
                        'topic': session.topic,
                        'start_time': session.start_time,
                        'end_time': session.end_time,
                        'duration_hours': session.duration_hours,
                        'status': session.status,
                        'knowledge_gained': session.knowledge_gained,
                        'improvements_applied': session.improvements_applied,
                        'errors': session.errors
                    }
                    for session in self.failed_sessions
                ]
            }
            
            with open(self.sessions_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            print(f"❌ Error saving training sessions: {e}")
    
    def record_failed_session(self, topic: str, duration_hours: float, 
                            knowledge_gained: List[str], errors: List[str]) -> str:
        """Record a failed training session"""
        session_id = f"training_{int(datetime.now().timestamp())}"
        
        session = TrainingSession(
            id=session_id,
            topic=topic,
            start_time=(datetime.now() - timedelta(hours=duration_hours)).isoformat(),
            end_time=datetime.now().isoformat(),
            duration_hours=duration_hours,
            status='failed',
            knowledge_gained=knowledge_gained,
            improvements_applied=0,
            errors=errors
        )
        
        self.failed_sessions.append(session)
        self._save_sessions()
        
        print(f"📝 Recorded failed training session: {session_id}")
        print(f"🎯 Topic: {topic}")
        print(f"⏱️ Duration: {duration_hours} hours")
        print(f"📚 Knowledge gained: {len(knowledge_gained)} concepts")
        print(f"❌ Errors: {len(errors)}")
        
        return session_id
    
    def recover_training_session(self, session_id: str, training_system=None, 
                               self_evolution_system=None) -> Dict[str, Any]:
        """Attempt to recover a failed training session"""
        session = None
        for s in self.failed_sessions:
            if s.id == session_id:
                session = s
                break
        
        if not session:
            return {'status': 'error', 'message': 'Session not found'}
        
        print(f"🔄 Attempting to recover training session: {session_id}")
        print(f"🎯 Topic: {session.topic}")
        
        try:
            # Attempt manual code improvements
            improvements_applied = 0
            
            if training_system and self_evolution_system:
                # Try to apply improvements manually
                result = self._apply_manual_improvements(session, training_system, self_evolution_system)
                improvements_applied = result.get('applied', 0)
            
            # Update session status
            session.status = 'recovered'
            session.improvements_applied = improvements_applied
            self._save_sessions()
            
            print(f"✅ Successfully recovered training session: {session_id}")
            print(f"🔧 Applied {improvements_applied} improvements")
            
            return {
                'status': 'success',
                'session_id': session_id,
                'topic': session.topic,
                'improvements_applied': improvements_applied,
                'knowledge_concepts': len(session.knowledge_gained)
            }
            
        except Exception as e:
            print(f"❌ Error recovering training session: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _apply_manual_improvements(self, session: TrainingSession, 
                                 training_system, self_evolution_system) -> Dict[str, Any]:
        """Apply manual improvements for a training session"""
        try:
            print(f"🔧 Applying manual improvements for {session.topic}...")
            
            # Create knowledge integration improvements
            improvements_applied = 0
            
            # 1. Store training knowledge in memory
            if hasattr(training_system, 'memory_system') and training_system.memory_system:
                knowledge_summary = f"Recovered training on {session.topic}. Duration: {session.duration_hours} hours. Key concepts: {', '.join(session.knowledge_gained[:10])}"
                
                try:
                    training_system.memory_system.store_memory(
                        content=knowledge_summary,
                        memory_type="recovered_training",
                        importance=0.9,
                        tags=[session.topic, "training", "recovery"]
                    )
                    improvements_applied += 1
                    print(f"💾 Stored {session.topic} knowledge in memory")
                except Exception as e:
                    print(f"❌ Error storing knowledge: {e}")
            
            # 2. Create specialized methods file
            try:
                self._create_specialized_methods_file(session)
                improvements_applied += 1
                print(f"📝 Created specialized methods for {session.topic}")
            except Exception as e:
                print(f"❌ Error creating specialized methods: {e}")
            
            # 3. Update training statistics
            try:
                if hasattr(training_system, 'training_stats'):
                    training_system.training_stats['topics_trained'].append(session.topic)
                    training_system.training_stats['concepts_learned'].extend(session.knowledge_gained)
                    training_system.training_stats['last_training'] = session.end_time
                    improvements_applied += 1
                    print(f"📊 Updated training statistics")
            except Exception as e:
                print(f"❌ Error updating statistics: {e}")
            
            return {'applied': improvements_applied, 'errors': 0}
            
        except Exception as e:
            print(f"❌ Error in manual improvements: {e}")
            return {'applied': 0, 'errors': 1}
    
    def _create_specialized_methods_file(self, session: TrainingSession):
        """Create a specialized methods file for the training topic"""
        import re
        
        topic_safe = re.sub(r'[^a-zA-Z0-9_]', '_', session.topic.lower())
        class_name = topic_safe.title().replace("_", "")
        
        methods_content = f'''"""
Specialized Methods for {session.topic.title()}
Generated by JARVIS Training Recovery on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Training Session: {session.id}
Duration: {session.duration_hours} hours
Knowledge Concepts: {len(session.knowledge_gained)}
"""

import re
from typing import List, Dict, Any

class {class_name}Methods:
    """Specialized methods for {session.topic}"""

    def __init__(self):
        self.topic = "{session.topic}"
        self.knowledge_base = {session.knowledge_gained[:20]}  # Top 20 concepts
        self.training_session = "{session.id}"

    def analyze_{topic_safe}_query(self, query: str) -> Dict[str, Any]:
        """Analyze query for {session.topic} specific content"""
        analysis = {{
            "is_relevant": False,
            "confidence": 0.0,
            "relevant_knowledge": [],
            "suggested_response": None
        }}

        # Check for topic relevance
        topic_keywords = ["{session.topic.lower()}", "{topic_safe}"]
        query_lower = query.lower()
        
        relevance_score = 0
        for keyword in topic_keywords:
            if keyword in query_lower:
                relevance_score += 0.3
        
        for concept in self.knowledge_base:
            if concept.lower() in query_lower:
                relevance_score += 0.1
                analysis["relevant_knowledge"].append(concept)
        
        analysis["is_relevant"] = relevance_score > 0.2
        analysis["confidence"] = min(relevance_score, 1.0)
        
        if analysis["is_relevant"]:
            analysis["suggested_response"] = f"Based on my {session.topic} training, I can help with: {{', '.join(analysis['relevant_knowledge'][:3])}}"
        
        return analysis

    def get_{topic_safe}_knowledge(self, query: str = None) -> List[str]:
        """Get {session.topic} specific knowledge"""
        if query:
            # Filter knowledge based on query
            query_lower = query.lower()
            relevant = [k for k in self.knowledge_base if any(word in k.lower() for word in query_lower.split())]
            return relevant[:5]
        else:
            return self.knowledge_base[:10]

    def apply_{topic_safe}_insights(self, context: str) -> str:
        """Apply {session.topic} insights to given context"""
        insights = []
        context_lower = context.lower()
        
        for concept in self.knowledge_base:
            if any(word in context_lower for word in concept.lower().split()):
                insights.append(f"From {session.topic} training: {{concept}}")
        
        if insights:
            return "\\n".join(insights[:3])
        else:
            return f"No specific {session.topic} insights found for this context."

# Global instance
{topic_safe}_methods = {class_name}Methods()
'''
        
        # Save the methods file
        methods_file = f"src/ai/specialized_{topic_safe}_methods.py"
        with open(methods_file, 'w') as f:
            f.write(methods_content)
        
        print(f"📝 Created specialized methods file: {methods_file}")
    
    def get_recovery_summary(self) -> Dict[str, Any]:
        """Get summary of training recovery status"""
        total_sessions = len(self.failed_sessions)
        recovered_sessions = len([s for s in self.failed_sessions if s.status == 'recovered'])
        failed_sessions = len([s for s in self.failed_sessions if s.status == 'failed'])
        
        return {
            'total_sessions': total_sessions,
            'recovered_sessions': recovered_sessions,
            'failed_sessions': failed_sessions,
            'recovery_rate': recovered_sessions / total_sessions if total_sessions > 0 else 0,
            'recent_sessions': [
                {
                    'id': s.id,
                    'topic': s.topic,
                    'duration_hours': s.duration_hours,
                    'status': s.status,
                    'improvements_applied': s.improvements_applied
                }
                for s in self.failed_sessions[-5:]  # Last 5 sessions
            ]
        }
    
    def recover_all_failed_sessions(self, training_system=None, self_evolution_system=None) -> Dict[str, Any]:
        """Attempt to recover all failed training sessions"""
        failed_sessions = [s for s in self.failed_sessions if s.status == 'failed']
        
        if not failed_sessions:
            return {'status': 'no_sessions', 'message': 'No failed sessions to recover'}
        
        print(f"🔄 Attempting to recover {len(failed_sessions)} failed training sessions...")
        
        recovered = 0
        errors = 0
        
        for session in failed_sessions:
            try:
                result = self.recover_training_session(session.id, training_system, self_evolution_system)
                if result['status'] == 'success':
                    recovered += 1
                else:
                    errors += 1
            except Exception as e:
                print(f"❌ Error recovering session {session.id}: {e}")
                errors += 1
        
        return {
            'status': 'completed',
            'total_sessions': len(failed_sessions),
            'recovered': recovered,
            'errors': errors,
            'recovery_rate': recovered / len(failed_sessions) if failed_sessions else 0
        }
