"""
Self-Evolution System for JARVIS V6
===================================

Advanced self-improvement system that allows JARVIS to analyze and improve
its own code, performance, and capabilities autonomously.

Based on the self-evolution system from llama server project.
"""

import os
import sys
import ast
import json
import time
import shutil
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import sqlite3
import threading
import hashlib


@dataclass
class EvolutionTask:
    """Individual evolution task"""
    task_id: str
    task_type: str  # code_improvement, feature_addition, optimization, bug_fix
    description: str
    priority: int  # 1-10
    complexity: int  # 1-10
    estimated_time: int  # minutes
    status: str  # pending, in_progress, completed, failed, rolled_back
    created_at: str
    completed_at: Optional[str] = None
    result: Optional[str] = None
    performance_impact: Optional[float] = None


@dataclass
class CodeAnalysis:
    """Code analysis result"""
    file_path: str
    complexity_score: float
    performance_score: float
    maintainability_score: float
    suggestions: List[str]
    potential_improvements: List[str]
    risk_level: str  # low, medium, high


class SelfEvolutionSystem:
    """Advanced self-evolution system for JARVIS"""
    
    def __init__(self, config):
        self.config = config
        self.db_path = "data/evolution.db"
        self.backup_dir = "data/backups"
        self.evolution_enabled = True
        self.safety_mode = True
        
        # Core files that can be safely modified
        self.modifiable_files = [
            'src/ai/ollama_client.py',
            'src/ai/training_system.py',
            'src/ai/self_edit_system.py',
            'src/ai/knowledge_base.py',
            'src/gui/chat_widget.py',
            'src/gui/input_widget.py'
        ]
        
        # Files that should never be modified
        self.protected_files = [
            'src/core/config.py',
            'src/gui/main_window.py',
            'main.py',
            'jarvis_clean.py'
        ]
        
        self._init_database()
        self._ensure_backup_dir()
    
    def _init_database(self):
        """Initialize evolution database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS evolution_tasks (
                    task_id TEXT PRIMARY KEY,
                    task_type TEXT NOT NULL,
                    description TEXT NOT NULL,
                    priority INTEGER NOT NULL,
                    complexity INTEGER NOT NULL,
                    estimated_time INTEGER NOT NULL,
                    status TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    completed_at TEXT,
                    result TEXT,
                    performance_impact REAL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS code_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL,
                    analysis_date TEXT NOT NULL,
                    complexity_score REAL NOT NULL,
                    performance_score REAL NOT NULL,
                    maintainability_score REAL NOT NULL,
                    suggestions TEXT,
                    improvements TEXT,
                    risk_level TEXT NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS evolution_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    action TEXT NOT NULL,
                    file_path TEXT,
                    description TEXT,
                    success BOOLEAN NOT NULL,
                    rollback_available BOOLEAN DEFAULT TRUE
                )
            """)
    
    def _ensure_backup_dir(self):
        """Ensure backup directory exists"""
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def analyze_code_file(self, file_path: str) -> CodeAnalysis:
        """Analyze a code file for improvement opportunities"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST for analysis
            tree = ast.parse(content)
            
            # Calculate metrics
            complexity_score = self._calculate_complexity(tree)
            performance_score = self._analyze_performance(content)
            maintainability_score = self._analyze_maintainability(content)
            
            # Generate suggestions
            suggestions = self._generate_suggestions(content, tree)
            improvements = self._identify_improvements(content, tree)
            
            # Determine risk level
            risk_level = self._assess_risk_level(file_path, complexity_score)
            
            analysis = CodeAnalysis(
                file_path=file_path,
                complexity_score=complexity_score,
                performance_score=performance_score,
                maintainability_score=maintainability_score,
                suggestions=suggestions,
                potential_improvements=improvements,
                risk_level=risk_level
            )
            
            # Store analysis in database
            self._store_analysis(analysis)
            
            return analysis
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return None
    
    def _calculate_complexity(self, tree: ast.AST) -> float:
        """Calculate code complexity score"""
        complexity = 0
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For)):
                complexity += 1
            elif isinstance(node, ast.FunctionDef):
                complexity += 0.5
            elif isinstance(node, ast.ClassDef):
                complexity += 0.3
        
        # Normalize to 0-1 scale
        return min(complexity / 50.0, 1.0)
    
    def _analyze_performance(self, content: str) -> float:
        """Analyze performance characteristics"""
        performance_score = 1.0
        
        # Check for potential performance issues
        performance_issues = [
            'time.sleep(',
            'while True:',
            'for i in range(10000',
            'import *',
            'eval(',
            'exec('
        ]
        
        for issue in performance_issues:
            if issue in content:
                performance_score -= 0.1
        
        return max(performance_score, 0.0)
    
    def _analyze_maintainability(self, content: str) -> float:
        """Analyze code maintainability"""
        maintainability = 1.0
        
        lines = content.split('\n')
        
        # Check for good practices
        has_docstrings = '"""' in content or "'''" in content
        has_type_hints = ': ' in content and '->' in content
        has_comments = any(line.strip().startswith('#') for line in lines)
        
        if has_docstrings:
            maintainability += 0.1
        if has_type_hints:
            maintainability += 0.1
        if has_comments:
            maintainability += 0.05
        
        # Check for bad practices
        long_lines = sum(1 for line in lines if len(line) > 120)
        if long_lines > len(lines) * 0.1:  # More than 10% long lines
            maintainability -= 0.2
        
        return min(max(maintainability, 0.0), 1.0)
    
    def _generate_suggestions(self, content: str, tree: ast.AST) -> List[str]:
        """Generate improvement suggestions"""
        suggestions = []
        
        # Check for missing docstrings
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if not ast.get_docstring(node):
                    suggestions.append(f"Add docstring to function '{node.name}'")
        
        # Check for long functions
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if hasattr(node, 'end_lineno') and hasattr(node, 'lineno'):
                    if node.end_lineno - node.lineno > 50:
                        suggestions.append(f"Consider breaking down function '{node.name}' (too long)")
        
        # Check for imports
        if 'import *' in content:
            suggestions.append("Avoid wildcard imports - use specific imports")
        
        return suggestions
    
    def _identify_improvements(self, content: str, tree: ast.AST) -> List[str]:
        """Identify potential improvements"""
        improvements = []
        
        # Performance improvements
        if 'time.sleep(' in content:
            improvements.append("Consider using async/await instead of time.sleep")
        
        if 'for i in range(len(' in content:
            improvements.append("Use enumerate() instead of range(len())")
        
        # Code quality improvements
        if content.count('try:') > content.count('except'):
            improvements.append("Add proper exception handling")
        
        return improvements
    
    def _assess_risk_level(self, file_path: str, complexity: float) -> str:
        """Assess risk level of modifying this file"""
        if file_path in self.protected_files:
            return "high"
        elif complexity > 0.7:
            return "high"
        elif complexity > 0.4:
            return "medium"
        else:
            return "low"
    
    def _store_analysis(self, analysis: CodeAnalysis):
        """Store code analysis in database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO code_analysis
                (file_path, analysis_date, complexity_score, performance_score,
                 maintainability_score, suggestions, improvements, risk_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                analysis.file_path,
                datetime.now().isoformat(),
                analysis.complexity_score,
                analysis.performance_score,
                analysis.maintainability_score,
                json.dumps(analysis.suggestions),
                json.dumps(analysis.potential_improvements),
                analysis.risk_level
            ))
    
    def create_evolution_task(self, task_type: str, description: str,
                            priority: int = 5, complexity: int = 5) -> str:
        """Create a new evolution task"""
        task_id = f"task_{int(time.time())}_{hashlib.md5(description.encode()).hexdigest()[:8]}"
        
        task = EvolutionTask(
            task_id=task_id,
            task_type=task_type,
            description=description,
            priority=priority,
            complexity=complexity,
            estimated_time=complexity * 10,  # Rough estimate
            status="pending",
            created_at=datetime.now().isoformat()
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO evolution_tasks
                (task_id, task_type, description, priority, complexity,
                 estimated_time, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task.task_id, task.task_type, task.description,
                task.priority, task.complexity, task.estimated_time,
                task.status, task.created_at
            ))
        
        return task_id
    
    def get_evolution_stats(self) -> Dict[str, Any]:
        """Get evolution system statistics"""
        with sqlite3.connect(self.db_path) as conn:
            total_tasks = conn.execute("SELECT COUNT(*) FROM evolution_tasks").fetchone()[0]
            completed_tasks = conn.execute(
                "SELECT COUNT(*) FROM evolution_tasks WHERE status = 'completed'"
            ).fetchone()[0]
            
            avg_complexity = conn.execute(
                "SELECT AVG(complexity_score) FROM code_analysis"
            ).fetchone()[0] or 0
            
            return {
                "evolution_enabled": self.evolution_enabled,
                "safety_mode": self.safety_mode,
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "success_rate": completed_tasks / max(total_tasks, 1),
                "average_complexity": round(avg_complexity, 2),
                "modifiable_files": len(self.modifiable_files),
                "protected_files": len(self.protected_files)
            }
    
    def toggle_evolution(self, enabled: bool):
        """Enable or disable evolution system"""
        self.evolution_enabled = enabled
    
    def toggle_safety_mode(self, enabled: bool):
        """Enable or disable safety mode"""
        self.safety_mode = enabled
