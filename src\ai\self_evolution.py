"""
Self-Evolution System for JARVIS V6
===================================

Advanced self-improvement system that allows JARVIS to analyze and improve
its own code, performance, and capabilities autonomously.

Based on the self-evolution system from llama server project.
"""

import os
import sys
import ast
import json
import time
import shutil
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import sqlite3
import threading
import hashlib


@dataclass
class EvolutionTask:
    """Individual evolution task"""
    task_id: str
    task_type: str  # code_improvement, feature_addition, optimization, bug_fix
    description: str
    priority: int  # 1-10
    complexity: int  # 1-10
    estimated_time: int  # minutes
    status: str  # pending, in_progress, completed, failed, rolled_back
    created_at: str
    completed_at: Optional[str] = None
    result: Optional[str] = None
    performance_impact: Optional[float] = None


@dataclass
class CodeAnalysis:
    """Code analysis result"""
    file_path: str
    complexity_score: float
    performance_score: float
    maintainability_score: float
    suggestions: List[str]
    potential_improvements: List[str]
    risk_level: str  # low, medium, high


class SelfEvolutionSystem:
    """Advanced self-evolution system for JARVIS"""
    
    def __init__(self, config, deepseek_coder=None):
        self.config = config
        self.db_path = "data/evolution.db"
        self.backup_dir = "data/backups"
        self.evolution_enabled = True
        self.safety_mode = True
        self.deepseek_coder = deepseek_coder
        self.pending_improvements = []
        self.training_triggered_improvements = []
        
        # Core files that can be safely modified
        self.modifiable_files = [
            'src/ai/ollama_client.py',
            'src/ai/training_system.py',
            'src/ai/self_edit_system.py',
            'src/ai/knowledge_base.py',
            'src/gui/chat_widget.py',
            'src/gui/input_widget.py'
        ]
        
        # Files that should never be modified
        self.protected_files = [
            'src/core/config.py',
            'src/gui/main_window.py',
            'main.py',
            'jarvis_clean.py'
        ]
        
        self._init_database()
        self._ensure_backup_dir()

        print("🧬 Self-Evolution System initialized")
        print(f"📊 Database: {self.db_path}")
        print(f"💾 Backups: {self.backup_dir}")
        print(f"🔒 Safety mode: {'Enabled' if self.safety_mode else 'Disabled'}")
        if deepseek_coder:
            print("🔧 Integrated with DeepSeek-Coder V2 for intelligent code improvements")
    
    def _init_database(self):
        """Initialize evolution database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS evolution_tasks (
                    task_id TEXT PRIMARY KEY,
                    task_type TEXT NOT NULL,
                    description TEXT NOT NULL,
                    priority INTEGER NOT NULL,
                    complexity INTEGER NOT NULL,
                    estimated_time INTEGER NOT NULL,
                    status TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    completed_at TEXT,
                    result TEXT,
                    performance_impact REAL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS code_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL,
                    analysis_date TEXT NOT NULL,
                    complexity_score REAL NOT NULL,
                    performance_score REAL NOT NULL,
                    maintainability_score REAL NOT NULL,
                    suggestions TEXT,
                    improvements TEXT,
                    risk_level TEXT NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS evolution_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    action TEXT NOT NULL,
                    file_path TEXT,
                    description TEXT,
                    success BOOLEAN NOT NULL,
                    rollback_available BOOLEAN DEFAULT TRUE
                )
            """)
    
    def _ensure_backup_dir(self):
        """Ensure backup directory exists"""
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def analyze_code_file(self, file_path: str) -> CodeAnalysis:
        """Analyze a code file for improvement opportunities"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST for analysis
            tree = ast.parse(content)
            
            # Calculate metrics
            complexity_score = self._calculate_complexity(tree)
            performance_score = self._analyze_performance(content)
            maintainability_score = self._analyze_maintainability(content)
            
            # Generate suggestions
            suggestions = self._generate_suggestions(content, tree)
            improvements = self._identify_improvements(content, tree)
            
            # Determine risk level
            risk_level = self._assess_risk_level(file_path, complexity_score)
            
            analysis = CodeAnalysis(
                file_path=file_path,
                complexity_score=complexity_score,
                performance_score=performance_score,
                maintainability_score=maintainability_score,
                suggestions=suggestions,
                potential_improvements=improvements,
                risk_level=risk_level
            )
            
            # Store analysis in database
            self._store_analysis(analysis)
            
            return analysis
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return None
    
    def _calculate_complexity(self, tree: ast.AST) -> float:
        """Calculate code complexity score"""
        complexity = 0
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For)):
                complexity += 1
            elif isinstance(node, ast.FunctionDef):
                complexity += 0.5
            elif isinstance(node, ast.ClassDef):
                complexity += 0.3
        
        # Normalize to 0-1 scale
        return min(complexity / 50.0, 1.0)
    
    def _analyze_performance(self, content: str) -> float:
        """Analyze performance characteristics"""
        performance_score = 1.0
        
        # Check for potential performance issues
        performance_issues = [
            'time.sleep(',
            'while True:',
            'for i in range(10000',
            'import *',
            'eval(',
            'exec('
        ]
        
        for issue in performance_issues:
            if issue in content:
                performance_score -= 0.1
        
        return max(performance_score, 0.0)
    
    def _analyze_maintainability(self, content: str) -> float:
        """Analyze code maintainability"""
        maintainability = 1.0
        
        lines = content.split('\n')
        
        # Check for good practices
        has_docstrings = '"""' in content or "'''" in content
        has_type_hints = ': ' in content and '->' in content
        has_comments = any(line.strip().startswith('#') for line in lines)
        
        if has_docstrings:
            maintainability += 0.1
        if has_type_hints:
            maintainability += 0.1
        if has_comments:
            maintainability += 0.05
        
        # Check for bad practices
        long_lines = sum(1 for line in lines if len(line) > 120)
        if long_lines > len(lines) * 0.1:  # More than 10% long lines
            maintainability -= 0.2
        
        return min(max(maintainability, 0.0), 1.0)
    
    def _generate_suggestions(self, content: str, tree: ast.AST) -> List[str]:
        """Generate improvement suggestions"""
        suggestions = []
        
        # Check for missing docstrings
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if not ast.get_docstring(node):
                    suggestions.append(f"Add docstring to function '{node.name}'")
        
        # Check for long functions
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if hasattr(node, 'end_lineno') and hasattr(node, 'lineno'):
                    if node.end_lineno - node.lineno > 50:
                        suggestions.append(f"Consider breaking down function '{node.name}' (too long)")
        
        # Check for imports
        if 'import *' in content:
            suggestions.append("Avoid wildcard imports - use specific imports")
        
        return suggestions
    
    def _identify_improvements(self, content: str, tree: ast.AST) -> List[str]:
        """Identify potential improvements"""
        improvements = []
        
        # Performance improvements
        if 'time.sleep(' in content:
            improvements.append("Consider using async/await instead of time.sleep")
        
        if 'for i in range(len(' in content:
            improvements.append("Use enumerate() instead of range(len())")
        
        # Code quality improvements
        if content.count('try:') > content.count('except'):
            improvements.append("Add proper exception handling")
        
        return improvements
    
    def _assess_risk_level(self, file_path: str, complexity: float) -> str:
        """Assess risk level of modifying this file"""
        if file_path in self.protected_files:
            return "high"
        elif complexity > 0.7:
            return "high"
        elif complexity > 0.4:
            return "medium"
        else:
            return "low"
    
    def _store_analysis(self, analysis: CodeAnalysis):
        """Store code analysis in database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO code_analysis
                (file_path, analysis_date, complexity_score, performance_score,
                 maintainability_score, suggestions, improvements, risk_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                analysis.file_path,
                datetime.now().isoformat(),
                analysis.complexity_score,
                analysis.performance_score,
                analysis.maintainability_score,
                json.dumps(analysis.suggestions),
                json.dumps(analysis.potential_improvements),
                analysis.risk_level
            ))
    
    def create_evolution_task(self, task_type: str, description: str,
                            priority: int = 5, complexity: int = 5) -> str:
        """Create a new evolution task"""
        task_id = f"task_{int(time.time())}_{hashlib.md5(description.encode()).hexdigest()[:8]}"
        
        task = EvolutionTask(
            task_id=task_id,
            task_type=task_type,
            description=description,
            priority=priority,
            complexity=complexity,
            estimated_time=complexity * 10,  # Rough estimate
            status="pending",
            created_at=datetime.now().isoformat()
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO evolution_tasks
                (task_id, task_type, description, priority, complexity,
                 estimated_time, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task.task_id, task.task_type, task.description,
                task.priority, task.complexity, task.estimated_time,
                task.status, task.created_at
            ))
        
        return task_id
    
    def get_evolution_stats(self) -> Dict[str, Any]:
        """Get evolution system statistics"""
        with sqlite3.connect(self.db_path) as conn:
            total_tasks = conn.execute("SELECT COUNT(*) FROM evolution_tasks").fetchone()[0]
            completed_tasks = conn.execute(
                "SELECT COUNT(*) FROM evolution_tasks WHERE status = 'completed'"
            ).fetchone()[0]
            
            avg_complexity = conn.execute(
                "SELECT AVG(complexity_score) FROM code_analysis"
            ).fetchone()[0] or 0
            
            return {
                "evolution_enabled": self.evolution_enabled,
                "safety_mode": self.safety_mode,
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "success_rate": completed_tasks / max(total_tasks, 1),
                "average_complexity": round(avg_complexity, 2),
                "modifiable_files": len(self.modifiable_files),
                "protected_files": len(self.protected_files)
            }
    
    def toggle_evolution(self, enabled: bool):
        """Enable or disable evolution system"""
        self.evolution_enabled = enabled
    
    def toggle_safety_mode(self, enabled: bool):
        """Enable or disable safety mode"""
        self.safety_mode = enabled

    def analyze_code_for_improvements(self, topic: str, learned_concepts: List[str]) -> List[Dict]:
        """Analyze JARVIS's code for improvements based on training topic"""
        if not self.deepseek_coder:
            print("⚠️ DeepSeek-Coder not available for code analysis")
            return []

        improvements = []

        try:
            print(f"🔍 Analyzing JARVIS code for {topic} improvements...")

            # Analyze relevant files based on training topic
            relevant_files = self._get_relevant_files_for_topic(topic)

            for file_path in relevant_files:
                if os.path.exists(file_path):
                    print(f"📝 Analyzing {file_path}...")

                    # Read current code
                    with open(file_path, 'r', encoding='utf-8') as f:
                        current_code = f.read()

                    # Use DeepSeek-Coder to analyze and improve
                    result = self.deepseek_coder.improve_code(
                        current_code,
                        requirements=[f"Apply {topic} best practices", "Maintain existing functionality", "Improve based on learned concepts"],
                        language=self.deepseek_coder.detect_language(current_code)
                    )

                    if result.success and result.improved_code:
                        improvement = {
                            'file_path': file_path,
                            'topic': topic,
                            'current_code': current_code,
                            'improved_code': result.improved_code,
                            'explanation': result.explanation,
                            'confidence': result.confidence,
                            'suggestions': result.suggestions,
                            'learned_concepts': learned_concepts
                        }
                        improvements.append(improvement)
                        print(f"✅ Found improvement for {file_path} (confidence: {result.confidence:.1%})")
                    else:
                        print(f"ℹ️ No improvements found for {file_path}")

            self.pending_improvements.extend(improvements)
            print(f"🎯 Found {len(improvements)} potential code improvements")

            return improvements

        except Exception as e:
            print(f"❌ Error analyzing code for improvements: {e}")
            return []

    def _get_relevant_files_for_topic(self, topic: str) -> List[str]:
        """Get files relevant to the training topic"""
        topic_lower = topic.lower()

        # Map topics to relevant files
        topic_file_mapping = {
            'error handling': [
                'src/gui/main_window.py',
                'src/ai/ollama_client.py',
                'src/ai/screen_vision_service.py'
            ],
            'performance': [
                'src/ai/ollama_client.py',
                'src/ai/advanced_memory.py',
                'src/gui/main_window.py'
            ],
            'security': [
                'src/ai/ai_ethics_system.py',
                'src/gui/main_window.py',
                'src/ai/advanced_memory.py'
            ],
            'ui': [
                'src/gui/main_window.py',
                'src/gui/chat_widget.py',
                'src/gui/jarvis_hud.py'
            ],
            'ai': [
                'src/ai/ollama_client.py',
                'src/ai/training_system.py',
                'src/ai/advanced_memory.py'
            ],
            'python': [
                'src/gui/main_window.py',
                'src/ai/ollama_client.py'
            ]
        }

        # Find matching files
        relevant_files = []
        for key, files in topic_file_mapping.items():
            if key in topic_lower:
                relevant_files.extend(files)

        # Default to core files if no specific match
        if not relevant_files:
            relevant_files = [
                'src/gui/main_window.py',
                'src/ai/ollama_client.py'
            ]

        # Only return files that exist and are modifiable
        return [f for f in relevant_files if f in self.modifiable_files and os.path.exists(f)]

    def apply_training_improvements(self, topic: str) -> Dict[str, Any]:
        """Apply pending improvements from training session"""
        if not self.pending_improvements:
            return {'applied': 0, 'skipped': 0, 'errors': 0}

        print(f"🔧 Applying {topic} improvements to JARVIS code...")

        # Create backup before applying changes
        backup_id = self.create_backup(f"Before {topic} training improvements")

        applied = 0
        skipped = 0
        errors = 0

        for improvement in self.pending_improvements:
            try:
                if improvement['confidence'] > 0.7:  # Only apply high-confidence improvements
                    success = self._apply_code_improvement(improvement)
                    if success:
                        applied += 1
                        print(f"✅ Applied improvement to {improvement['file_path']}")
                    else:
                        errors += 1
                        print(f"❌ Failed to apply improvement to {improvement['file_path']}")
                else:
                    skipped += 1
                    print(f"⏭️ Skipped low-confidence improvement for {improvement['file_path']}")

            except Exception as e:
                errors += 1
                print(f"❌ Error applying improvement: {e}")

        # Clear pending improvements
        self.pending_improvements = []

        result = {
            'applied': applied,
            'skipped': skipped,
            'errors': errors,
            'backup_id': backup_id
        }

        if applied > 0:
            print(f"🎉 Successfully applied {applied} code improvements!")
            print(f"💾 Backup created: {backup_id}")

        return result

    def _apply_code_improvement(self, improvement: Dict) -> bool:
        """Apply a single code improvement"""
        try:
            file_path = improvement['file_path']
            improved_code = improvement['improved_code']

            # Validate the improved code
            if not self._validate_code_safety(improved_code, file_path):
                print(f"⚠️ Safety validation failed for {file_path}")
                return False

            # Apply the improvement
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(improved_code)

            # Log the evolution
            self._log_evolution(
                change_type="training_improvement",
                description=f"Applied {improvement['topic']} improvements",
                files_modified=[file_path],
                confidence=improvement['confidence']
            )

            return True

        except Exception as e:
            print(f"❌ Error applying code improvement: {e}")
            return False

    def _validate_code_safety(self, code: str, file_path: str) -> bool:
        """Validate that improved code is safe to apply"""
        try:
            # Basic syntax check
            ast.parse(code)

            # Check for dangerous operations
            dangerous_patterns = [
                'os.system(',
                'subprocess.call(',
                'exec(',
                'eval(',
                '__import__',
            ]

            # Read original file to compare
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    original_code = f.read()
            except:
                original_code = ""

            for pattern in dangerous_patterns:
                if pattern in code and pattern not in original_code:
                    print(f"⚠️ Detected potentially dangerous operation: {pattern}")
                    return False

            return True

        except SyntaxError as e:
            print(f"❌ Syntax error in improved code: {e}")
            return False
        except Exception as e:
            print(f"❌ Error validating code: {e}")
            return False
