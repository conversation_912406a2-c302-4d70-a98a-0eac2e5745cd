"""
AI Training System for JARVIS V6
Implements learning from conversations and response improvement
"""

import json
import os
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from src.core.config import Config


@dataclass
class ConversationEntry:
    """Single conversation entry for training"""
    timestamp: str
    user_input: str
    ai_response: str
    user_feedback: Optional[str] = None
    rating: Optional[int] = None  # 1-5 rating
    context: Optional[Dict[str, Any]] = None


@dataclass
class TrainingPattern:
    """Pattern learned from conversations"""
    pattern_id: str
    input_pattern: str
    response_template: str
    confidence: float
    usage_count: int
    last_used: str


class TrainingDatabase:
    """SQLite database for storing training data"""
    
    def __init__(self, db_path: str = "data/training.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self._ensure_db_exists()
        self._create_tables()
    
    def _ensure_db_exists(self):
        """Ensure database directory exists"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def _create_tables(self):
        """Create database tables"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    user_input TEXT NOT NULL,
                    ai_response TEXT NOT NULL,
                    user_feedback TEXT,
                    rating INTEGER,
                    context TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS patterns (
                    pattern_id TEXT PRIMARY KEY,
                    input_pattern TEXT NOT NULL,
                    response_template TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    usage_count INTEGER DEFAULT 0,
                    last_used TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS knowledge_base (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    topic TEXT NOT NULL,
                    content TEXT NOT NULL,
                    source TEXT,
                    confidence REAL DEFAULT 1.0,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
    
    def store_conversation(self, entry: ConversationEntry):
        """Store a conversation entry"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO conversations 
                    (timestamp, user_input, ai_response, user_feedback, rating, context)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    entry.timestamp,
                    entry.user_input,
                    entry.ai_response,
                    entry.user_feedback,
                    entry.rating,
                    json.dumps(entry.context) if entry.context else None
                ))
    
    def get_recent_conversations(self, limit: int = 100) -> List[ConversationEntry]:
        """Get recent conversations"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT timestamp, user_input, ai_response, user_feedback, rating, context
                FROM conversations
                ORDER BY timestamp DESC
                LIMIT ?
            """, (limit,))
            
            entries = []
            for row in cursor.fetchall():
                context = json.loads(row[5]) if row[5] else None
                entries.append(ConversationEntry(
                    timestamp=row[0],
                    user_input=row[1],
                    ai_response=row[2],
                    user_feedback=row[3],
                    rating=row[4],
                    context=context
                ))
            return entries
    
    def store_pattern(self, pattern: TrainingPattern):
        """Store or update a training pattern"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO patterns
                    (pattern_id, input_pattern, response_template, confidence, usage_count, last_used)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    pattern.pattern_id,
                    pattern.input_pattern,
                    pattern.response_template,
                    pattern.confidence,
                    pattern.usage_count,
                    pattern.last_used
                ))


class TrainingSystem:
    """Enhanced training system for JARVIS with advanced capabilities"""

    def __init__(self, config: Config, self_evolution_system=None):
        self.config = config
        self.db = TrainingDatabase()
        self.learning_enabled = True
        self.min_confidence_threshold = 0.7
        self.self_evolution_system = self_evolution_system

        # Enhanced features from llama server
        self.active_sessions = {}  # session_id -> session_data
        self.session_counter = 0
        self.max_concurrent_sessions = 5
        self.learned_concepts = []  # Track concepts learned during training

        # Training metrics
        self.training_metrics = {
            'total_training_time': 0.0,
            'sessions_completed': 0,
            'average_session_duration': 0.0,
            'improvement_rate': 0.0,
            'user_satisfaction': 0.0,
            'response_quality_trend': []
        }

        # Feedback integration
        self.feedback_weights = {
            'positive': 1.2,
            'negative': 0.8,
            'neutral': 1.0
        }
    
    def record_conversation(self, user_input: str, ai_response: str, 
                          context: Optional[Dict[str, Any]] = None):
        """Record a conversation for training"""
        if not self.learning_enabled:
            return
        
        entry = ConversationEntry(
            timestamp=datetime.now().isoformat(),
            user_input=user_input,
            ai_response=ai_response,
            context=context
        )
        
        self.db.store_conversation(entry)
        
        # Analyze for patterns in background
        threading.Thread(target=self._analyze_conversation, args=(entry,), daemon=True).start()
    
    def record_feedback(self, user_input: str, feedback: str, rating: int):
        """Record user feedback on a response"""
        # Find the most recent conversation with this input
        conversations = self.db.get_recent_conversations(50)
        for conv in conversations:
            if conv.user_input.lower().strip() == user_input.lower().strip():
                conv.user_feedback = feedback
                conv.rating = rating
                self.db.store_conversation(conv)
                break
    
    def _analyze_conversation(self, entry: ConversationEntry):
        """Analyze conversation for learning patterns"""
        try:
            # Simple pattern extraction (can be enhanced with NLP)
            input_words = entry.user_input.lower().split()
            
            # Look for common patterns
            if len(input_words) > 0:
                pattern_id = f"pattern_{hash(entry.user_input.lower()) % 10000}"
                
                # Create a simple pattern
                pattern = TrainingPattern(
                    pattern_id=pattern_id,
                    input_pattern=entry.user_input.lower(),
                    response_template=entry.ai_response,
                    confidence=0.5,  # Start with medium confidence
                    usage_count=1,
                    last_used=entry.timestamp
                )
                
                self.db.store_pattern(pattern)
        
        except Exception as e:
            print(f"Error analyzing conversation: {e}")
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get training system statistics"""
        with sqlite3.connect(self.db.db_path) as conn:
            # Count conversations
            conv_count = conn.execute("SELECT COUNT(*) FROM conversations").fetchone()[0]
            
            # Count patterns
            pattern_count = conn.execute("SELECT COUNT(*) FROM patterns").fetchone()[0]
            
            # Get average rating
            avg_rating = conn.execute("""
                SELECT AVG(rating) FROM conversations WHERE rating IS NOT NULL
            """).fetchone()[0] or 0
            
            return {
                "conversations_recorded": conv_count,
                "patterns_learned": pattern_count,
                "average_rating": round(avg_rating, 2),
                "learning_enabled": self.learning_enabled,
                "user_satisfaction": self.training_metrics.get('user_satisfaction', 0.0),
                "improvement_rate": self.training_metrics.get('improvement_rate', 0.0),
                "sessions_completed": self.training_metrics.get('sessions_completed', 0)
            }
    
    def toggle_learning(self, enabled: bool):
        """Enable or disable learning"""
        self.learning_enabled = enabled
    
    def start_training_session(self, duration_minutes: int = 30,
                              session_name: str = None) -> str:
        """Start a new training session with custom duration"""
        session_id = f"session_{self.session_counter}_{int(time.time())}"
        self.session_counter += 1

        session_data = {
            'id': session_id,
            'name': session_name or f"Training Session {self.session_counter}",
            'start_time': datetime.now().isoformat(),
            'duration_minutes': duration_minutes,
            'end_time': (datetime.now() + timedelta(minutes=duration_minutes)).isoformat(),
            'conversations_processed': 0,
            'improvements_made': 0,
            'status': 'active',
            'quality_scores': []
        }

        self.active_sessions[session_id] = session_data
        return session_id

    def process_feedback(self, user_input: str, ai_response: str,
                        feedback_type: str, rating: int = None):
        """Process user feedback with enhanced weighting"""
        # Apply feedback weights
        weight = self.feedback_weights.get(feedback_type, 1.0)

        # Update training metrics
        if rating:
            self.training_metrics['user_satisfaction'] = (
                (self.training_metrics['user_satisfaction'] * 0.9) +
                (rating * 0.1 * weight)
            )

        # Record enhanced feedback
        entry = ConversationEntry(
            timestamp=datetime.now().isoformat(),
            user_input=user_input,
            ai_response=ai_response,
            user_feedback=feedback_type,
            rating=rating,
            context={'feedback_weight': weight}
        )

        self.db.store_conversation(entry)

        # Update active sessions
        for session in self.active_sessions.values():
            if session['status'] == 'active':
                session['conversations_processed'] += 1
                if rating:
                    session['quality_scores'].append(rating * weight)

    def get_training_recommendations(self) -> List[str]:
        """Get AI-generated training recommendations"""
        recommendations = []

        # Analyze recent performance
        recent_conversations = self.db.get_recent_conversations(50)

        if len(recent_conversations) < 10:
            recommendations.append("Need more conversation data for analysis")
            return recommendations

        # Check response quality trend
        ratings = [c.rating for c in recent_conversations if c.rating]
        if ratings:
            avg_rating = sum(ratings) / len(ratings)
            if avg_rating < 3.5:
                recommendations.append("Focus on improving response quality")
            if len(ratings) > 10:
                recent_avg = sum(ratings[-5:]) / 5
                older_avg = sum(ratings[-10:-5]) / 5
                if recent_avg < older_avg:
                    recommendations.append("Performance declining - increase training frequency")

        # Check conversation patterns
        topics = {}
        for conv in recent_conversations:
            words = conv.user_input.lower().split()
            for word in words:
                if len(word) > 3:
                    topics[word] = topics.get(word, 0) + 1

        if topics:
            common_topics = sorted(topics.items(), key=lambda x: x[1], reverse=True)[:3]
            recommendations.append(f"Focus training on: {', '.join([t[0] for t in common_topics])}")

        return recommendations

    def clear_training_data(self):
        """Clear all training data (use with caution)"""
        with sqlite3.connect(self.db.db_path) as conn:
            conn.execute("DELETE FROM conversations")
            conn.execute("DELETE FROM patterns")
            conn.execute("DELETE FROM knowledge_base")
            conn.commit()

        # Reset metrics
        self.training_metrics = {
            'total_training_time': 0.0,
            'sessions_completed': 0,
            'average_session_duration': 0.0,
            'improvement_rate': 0.0,
            'user_satisfaction': 0.0,
            'response_quality_trend': []
        }

    def extract_learned_concepts(self, topic: str, training_content: str) -> List[str]:
        """Extract key concepts learned during training"""
        concepts = []

        try:
            # Simple keyword extraction based on topic
            topic_lower = topic.lower()
            content_lower = training_content.lower()

            print(f"🔍 Extracting concepts from topic: '{topic}' with content length: {len(training_content)}")

            # Topic-specific concept extraction with more comprehensive matching
            if 'error' in topic_lower or 'handling' in topic_lower:
                concepts.extend([
                    "Exception handling with try-except blocks",
                    "Error logging and debugging techniques",
                    "Input validation and error prevention",
                    "Graceful error recovery strategies",
                    "Error message clarity and user experience"
                ])
                print(f"✅ Added {len(concepts)} error handling concepts")

            elif 'performance' in topic_lower:
                concepts.extend([
                    "Caching for performance optimization",
                    "Asynchronous programming and threading",
                    "Memory management and optimization",
                    "Database query optimization",
                    "Code profiling and bottleneck identification"
                ])
                print(f"✅ Added {len(concepts)} performance concepts")

            elif 'security' in topic_lower:
                concepts.extend([
                    "Authentication and authorization",
                    "Data encryption and security",
                    "Input validation and sanitization",
                    "SQL injection prevention",
                    "Cross-site scripting (XSS) protection"
                ])
                print(f"✅ Added {len(concepts)} security concepts")

            elif 'ui' in topic_lower or 'interface' in topic_lower:
                concepts.extend([
                    "Responsive user interface design",
                    "Accessibility and usability improvements",
                    "UI animations and visual feedback",
                    "User experience optimization",
                    "Cross-platform compatibility"
                ])
                print(f"✅ Added {len(concepts)} UI concepts")

            # General programming concepts - always add some
            concepts.extend([
                "Clean code principles and best practices",
                "Code documentation and comments",
                "Unit testing and quality assurance"
            ])

            # Content-based concept extraction
            if 'python' in content_lower:
                concepts.append("Python programming best practices")
            if 'function' in content_lower:
                concepts.append("Function design and modularity")
            if 'class' in content_lower:
                concepts.append("Object-oriented programming principles")

            # Remove duplicates and store
            concepts = list(set(concepts))
            self.learned_concepts.extend(concepts)

            print(f"🎯 Extracted {len(concepts)} total concepts for '{topic}'")

            return concepts

        except Exception as e:
            print(f"❌ Error extracting learned concepts: {e}")
            # Return default concepts even if extraction fails
            return [
                f"{topic.title()} best practices",
                "Code quality improvements",
                "Professional development techniques"
            ]

    def trigger_code_improvements(self, topic: str, training_content: str) -> Dict[str, Any]:
        """Trigger code improvements based on training session"""
        if not self.self_evolution_system:
            print("⚠️ Self-evolution system not available for code improvements")
            return {'status': 'skipped', 'reason': 'No self-evolution system'}

        try:
            print(f"🔧 Triggering code improvements for {topic}...")

            # Extract learned concepts from training
            learned_concepts = self.extract_learned_concepts(topic, training_content)

            if not learned_concepts:
                print(f"ℹ️ No specific concepts learned for {topic}")
                return {'status': 'skipped', 'reason': 'No concepts learned'}

            print(f"📚 Learned concepts: {', '.join(learned_concepts)}")

            # Analyze code for improvements
            improvements = self.self_evolution_system.analyze_code_for_improvements(topic, learned_concepts)

            if not improvements:
                print(f"ℹ️ No code improvements found for {topic}")
                return {'status': 'no_improvements', 'concepts': learned_concepts}

            # Apply improvements
            result = self.self_evolution_system.apply_training_improvements(topic)

            return {
                'status': 'completed',
                'topic': topic,
                'concepts': learned_concepts,
                'improvements_found': len(improvements),
                'improvements_applied': result['applied'],
                'improvements_skipped': result['skipped'],
                'errors': result['errors'],
                'backup_id': result.get('backup_id')
            }

        except Exception as e:
            print(f"❌ Error triggering code improvements: {e}")
            return {'status': 'error', 'error': str(e)}
