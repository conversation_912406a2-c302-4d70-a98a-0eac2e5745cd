"""
Advanced Memory System for JARVIS V6
====================================

Enhanced memory system with long-term episodic memory, user profiling,
emotional intelligence, and contextual awareness.

Based on the advanced memory system from llama server project.
"""

import sqlite3
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
import re
import hashlib


@dataclass
class MemoryEntry:
    """Individual memory entry with enhanced metadata"""
    id: str
    timestamp: str
    memory_type: str  # episodic, semantic, procedural, emotional
    content: str
    context: Dict[str, Any]
    importance: float  # 0.0 to 1.0
    emotional_valence: float  # -1.0 to 1.0 (negative to positive)
    tags: List[str]
    related_memories: List[str]
    access_count: int = 0
    last_accessed: str = ""


@dataclass
class UserProfile:
    """User profile with preferences and patterns"""
    user_id: str
    name: str
    preferences: Dict[str, Any]
    communication_style: str
    emotional_patterns: Dict[str, float]
    interaction_history: List[str]
    created_at: str
    updated_at: str


class AdvancedMemorySystem:
    """Advanced memory system with emotional intelligence and user profiling"""
    
    def __init__(self, db_path: str = "data/advanced_memory.db"):
        self.db_path = db_path
        self.current_user = "default_user"
        self.conversation_context = []
        self.emotional_state = {"valence": 0.0, "arousal": 0.0}
        
        self._init_database()
        self._load_user_profile()
    
    def _init_database(self):
        """Initialize the advanced memory database"""
        with sqlite3.connect(self.db_path) as conn:
            # Memory entries table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    context TEXT,
                    importance REAL NOT NULL,
                    emotional_valence REAL NOT NULL,
                    tags TEXT,
                    related_memories TEXT,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TEXT
                )
            """)
            
            # User profiles table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    preferences TEXT,
                    communication_style TEXT,
                    emotional_patterns TEXT,
                    interaction_history TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # Conversation threads table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS conversation_threads (
                    thread_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    topic TEXT,
                    emotional_arc TEXT,
                    summary TEXT,
                    importance REAL DEFAULT 0.5
                )
            """)
    
    def _load_user_profile(self):
        """Load or create user profile"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM user_profiles WHERE user_id = ?",
                (self.current_user,)
            )
            row = cursor.fetchone()
            
            if row:
                self.user_profile = UserProfile(
                    user_id=row[0],
                    name=row[1],
                    preferences=json.loads(row[2]) if row[2] else {},
                    communication_style=row[3] or "casual",
                    emotional_patterns=json.loads(row[4]) if row[4] else {},
                    interaction_history=json.loads(row[5]) if row[5] else [],
                    created_at=row[6],
                    updated_at=row[7]
                )
            else:
                # Create new user profile
                self.user_profile = UserProfile(
                    user_id=self.current_user,
                    name="User",
                    preferences={},
                    communication_style="casual",
                    emotional_patterns={},
                    interaction_history=[],
                    created_at=datetime.now().isoformat(),
                    updated_at=datetime.now().isoformat()
                )
                self._save_user_profile()
    
    def _save_user_profile(self):
        """Save user profile to database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO user_profiles
                (user_id, name, preferences, communication_style, emotional_patterns, 
                 interaction_history, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.user_profile.user_id,
                self.user_profile.name,
                json.dumps(self.user_profile.preferences),
                self.user_profile.communication_style,
                json.dumps(self.user_profile.emotional_patterns),
                json.dumps(self.user_profile.interaction_history),
                self.user_profile.created_at,
                datetime.now().isoformat()
            ))
    
    def store_memory(self, content: str, memory_type: str = "episodic",
                    importance: float = 0.5, emotional_valence: float = 0.0,
                    tags: List[str] = None, context: Dict[str, Any] = None) -> str:
        """Store a new memory with enhanced metadata"""
        memory_id = hashlib.md5(f"{content}{time.time()}".encode()).hexdigest()
        
        memory = MemoryEntry(
            id=memory_id,
            timestamp=datetime.now().isoformat(),
            memory_type=memory_type,
            content=content,
            context=context or {},
            importance=importance,
            emotional_valence=emotional_valence,
            tags=tags or [],
            related_memories=[],
            access_count=0,
            last_accessed=""
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO memories
                (id, timestamp, memory_type, content, context, importance,
                 emotional_valence, tags, related_memories, access_count, last_accessed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                memory.id,
                memory.timestamp,
                memory.memory_type,
                memory.content,
                json.dumps(memory.context),
                memory.importance,
                memory.emotional_valence,
                json.dumps(memory.tags),
                json.dumps(memory.related_memories),
                memory.access_count,
                memory.last_accessed
            ))
        
        return memory_id
    
    def recall_memories(self, query: str, memory_type: str = None,
                       limit: int = 10) -> List[MemoryEntry]:
        """Recall memories based on query with relevance scoring"""
        with sqlite3.connect(self.db_path) as conn:
            sql = """
                SELECT * FROM memories
                WHERE content LIKE ? OR tags LIKE ?
            """
            params = [f"%{query}%", f"%{query}%"]
            
            if memory_type:
                sql += " AND memory_type = ?"
                params.append(memory_type)
            
            sql += " ORDER BY importance DESC, access_count DESC LIMIT ?"
            params.append(limit)
            
            cursor = conn.execute(sql, params)
            rows = cursor.fetchall()
            
            memories = []
            for row in rows:
                memory = MemoryEntry(
                    id=row[0],
                    timestamp=row[1],
                    memory_type=row[2],
                    content=row[3],
                    context=json.loads(row[4]) if row[4] else {},
                    importance=row[5],
                    emotional_valence=row[6],
                    tags=json.loads(row[7]) if row[7] else [],
                    related_memories=json.loads(row[8]) if row[8] else [],
                    access_count=row[9],
                    last_accessed=row[10]
                )
                memories.append(memory)
                
                # Update access count
                conn.execute(
                    "UPDATE memories SET access_count = access_count + 1, last_accessed = ? WHERE id = ?",
                    (datetime.now().isoformat(), memory.id)
                )
            
            return memories
    
    def analyze_emotional_context(self, text: str) -> Dict[str, float]:
        """Analyze emotional context of text"""
        # Simple emotion detection (can be enhanced with NLP)
        positive_words = ['good', 'great', 'excellent', 'happy', 'love', 'amazing', 'wonderful']
        negative_words = ['bad', 'terrible', 'hate', 'angry', 'sad', 'awful', 'horrible']
        
        words = text.lower().split()
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        
        total_emotional_words = positive_count + negative_count
        if total_emotional_words == 0:
            valence = 0.0
        else:
            valence = (positive_count - negative_count) / total_emotional_words
        
        return {
            'valence': valence,
            'arousal': min(total_emotional_words / len(words), 1.0),
            'positive_words': positive_count,
            'negative_words': negative_count
        }
    
    def update_user_preferences(self, preferences: Dict[str, Any]):
        """Update user preferences"""
        self.user_profile.preferences.update(preferences)
        self.user_profile.updated_at = datetime.now().isoformat()
        self._save_user_profile()
    
    def get_contextual_summary(self, limit: int = 5) -> str:
        """Get contextual summary of recent interactions"""
        recent_memories = self.recall_memories("", limit=limit)
        
        if not recent_memories:
            return "No recent context available."
        
        summary_parts = []
        for memory in recent_memories:
            summary_parts.append(f"- {memory.content[:100]}...")
        
        return "Recent context:\n" + "\n".join(summary_parts)
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory system statistics"""
        with sqlite3.connect(self.db_path) as conn:
            total_memories = conn.execute("SELECT COUNT(*) FROM memories").fetchone()[0]
            avg_importance = conn.execute("SELECT AVG(importance) FROM memories").fetchone()[0] or 0
            memory_types = conn.execute("""
                SELECT memory_type, COUNT(*) FROM memories GROUP BY memory_type
            """).fetchall()
            
            return {
                "total_memories": total_memories,
                "average_importance": round(avg_importance, 2),
                "memory_types": dict(memory_types),
                "user_profile": asdict(self.user_profile),
                "emotional_state": self.emotional_state
            }
