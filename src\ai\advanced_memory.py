"""
Enhanced Advanced Memory System for JARVIS V6
=============================================

Comprehensive memory system with:
- Persistent conversation history
- User knowledge database
- Emotional intelligence
- Contextual awareness
- Learning from interactions
- Personal preferences tracking

Based on the advanced memory system from llama server project.
"""

import sqlite3
import json
import time
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
import re
import hashlib
import threading


@dataclass
class MemoryEntry:
    """Individual memory entry with enhanced metadata"""
    id: str
    timestamp: str
    memory_type: str  # episodic, semantic, procedural, emotional
    content: str
    context: Dict[str, Any]
    importance: float  # 0.0 to 1.0
    emotional_valence: float  # -1.0 to 1.0 (negative to positive)
    tags: List[str]
    related_memories: List[str]
    access_count: int = 0
    last_accessed: str = ""


@dataclass
class UserProfile:
    """Enhanced user profile with comprehensive tracking"""
    user_id: str
    name: str
    preferences: Dict[str, Any]
    communication_style: str
    emotional_patterns: Dict[str, float]
    interaction_history: List[str]
    created_at: str
    updated_at: str
    # New fields for enhanced tracking
    favorite_topics: List[str]
    learning_style: str
    response_preferences: Dict[str, Any]
    personal_facts: Dict[str, Any]
    goals_and_interests: List[str]
    conversation_patterns: Dict[str, Any]


@dataclass
class ConversationEntry:
    """Individual conversation entry"""
    id: str
    timestamp: str
    user_message: str
    jarvis_response: str
    context: Dict[str, Any]
    emotional_tone: float
    importance: float
    tags: List[str]
    session_id: str


@dataclass
class KnowledgeEntry:
    """Knowledge database entry"""
    id: str
    topic: str
    content: str
    source: str
    confidence: float
    timestamp: str
    tags: List[str]
    related_topics: List[str]
    verified: bool


class AdvancedMemorySystem:
    """Enhanced memory system with comprehensive conversation and knowledge tracking"""

    def __init__(self, db_path: str = "data/advanced_memory.db"):
        self.db_path = db_path
        self.current_user = "austin"  # Set to user's name from memories
        self.conversation_context = []
        self.emotional_state = {"valence": 0.0, "arousal": 0.0}
        self.current_session_id = self._generate_session_id()
        self.conversation_buffer = []
        self.lock = threading.Lock()

        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        self._init_database()
        self._migrate_database()
        self._load_user_profile()
        self._initialize_user_knowledge()

        print(f"🧠 Enhanced Memory System initialized for {self.current_user}")
        print(f"📊 Session ID: {self.current_session_id}")

    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        return f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hashlib.md5(str(time.time()).encode()).hexdigest()[:8]}"
    
    def _init_database(self):
        """Initialize the enhanced memory database"""
        with sqlite3.connect(self.db_path) as conn:
            # Memory entries table (enhanced)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    context TEXT,
                    importance REAL NOT NULL,
                    emotional_valence REAL NOT NULL,
                    tags TEXT,
                    related_memories TEXT,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TEXT,
                    user_id TEXT DEFAULT 'austin'
                )
            """)

            # Enhanced user profiles table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    preferences TEXT,
                    communication_style TEXT,
                    emotional_patterns TEXT,
                    interaction_history TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    favorite_topics TEXT,
                    learning_style TEXT,
                    response_preferences TEXT,
                    personal_facts TEXT,
                    goals_and_interests TEXT,
                    conversation_patterns TEXT
                )
            """)

            # Conversation history table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    user_message TEXT NOT NULL,
                    jarvis_response TEXT NOT NULL,
                    context TEXT,
                    emotional_tone REAL DEFAULT 0.0,
                    importance REAL DEFAULT 0.5,
                    tags TEXT,
                    session_id TEXT NOT NULL,
                    user_id TEXT DEFAULT 'austin'
                )
            """)

            # Knowledge database table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS knowledge_base (
                    id TEXT PRIMARY KEY,
                    topic TEXT NOT NULL,
                    content TEXT NOT NULL,
                    source TEXT NOT NULL,
                    confidence REAL DEFAULT 0.5,
                    timestamp TEXT NOT NULL,
                    tags TEXT,
                    related_topics TEXT,
                    verified BOOLEAN DEFAULT 0,
                    user_id TEXT DEFAULT 'austin'
                )
            """)

            # Session tracking table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    message_count INTEGER DEFAULT 0,
                    topics_discussed TEXT,
                    emotional_summary TEXT,
                    key_insights TEXT
                )
            """)

    def _migrate_database(self):
        """Migrate existing database to new schema"""
        with sqlite3.connect(self.db_path) as conn:
            # Check if new columns exist in user_profiles table
            try:
                cursor = conn.execute("PRAGMA table_info(user_profiles)")
                columns = [row[1] for row in cursor.fetchall()]

                # Add missing columns to user_profiles
                new_columns = [
                    ("favorite_topics", "TEXT"),
                    ("learning_style", "TEXT"),
                    ("response_preferences", "TEXT"),
                    ("personal_facts", "TEXT"),
                    ("goals_and_interests", "TEXT"),
                    ("conversation_patterns", "TEXT")
                ]

                for column_name, column_type in new_columns:
                    if column_name not in columns:
                        try:
                            conn.execute(f"ALTER TABLE user_profiles ADD COLUMN {column_name} {column_type}")
                            print(f"✅ Added column {column_name} to user_profiles")
                        except sqlite3.OperationalError as e:
                            if "duplicate column name" not in str(e):
                                print(f"⚠️ Could not add column {column_name}: {e}")

                # Add user_id column to existing tables if missing
                tables_to_update = ["memories", "conversations", "knowledge_base"]
                for table in tables_to_update:
                    try:
                        cursor = conn.execute(f"PRAGMA table_info({table})")
                        columns = [row[1] for row in cursor.fetchall()]

                        if "user_id" not in columns:
                            conn.execute(f"ALTER TABLE {table} ADD COLUMN user_id TEXT DEFAULT 'austin'")
                            print(f"✅ Added user_id column to {table}")

                    except sqlite3.OperationalError as e:
                        if "no such table" not in str(e) and "duplicate column name" not in str(e):
                            print(f"⚠️ Could not update table {table}: {e}")

            except sqlite3.OperationalError as e:
                print(f"⚠️ Database migration error: {e}")

    def _load_user_profile(self):
        """Load or create enhanced user profile"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM user_profiles WHERE user_id = ?",
                (self.current_user,)
            )
            row = cursor.fetchone()

            if row:
                self.user_profile = UserProfile(
                    user_id=row[0],
                    name=row[1],
                    preferences=json.loads(row[2]) if row[2] else {},
                    communication_style=row[3] or "casual",
                    emotional_patterns=json.loads(row[4]) if row[4] else {},
                    interaction_history=json.loads(row[5]) if row[5] else [],
                    created_at=row[6],
                    updated_at=row[7],
                    favorite_topics=json.loads(row[8]) if len(row) > 8 and row[8] else [],
                    learning_style=row[9] if len(row) > 9 else "adaptive",
                    response_preferences=json.loads(row[10]) if len(row) > 10 and row[10] else {},
                    personal_facts=json.loads(row[11]) if len(row) > 11 and row[11] else {},
                    goals_and_interests=json.loads(row[12]) if len(row) > 12 and row[12] else [],
                    conversation_patterns=json.loads(row[13]) if len(row) > 13 and row[13] else {}
                )
            else:
                # Create new enhanced user profile
                self.user_profile = UserProfile(
                    user_id=self.current_user,
                    name="Austin",  # From memories
                    preferences={
                        "ai_assistant_type": "Mixtral 8x7B via Ollama",
                        "architecture": "local-only",
                        "code_structure": "modular",
                        "gui_style": "PyQt6 JARVIS HUD",
                        "tts_provider": "ElevenLabs",
                        "executable_preference": ".exe files"
                    },
                    communication_style="technical_friendly",
                    emotional_patterns={},
                    interaction_history=[],
                    created_at=datetime.now().isoformat(),
                    updated_at=datetime.now().isoformat(),
                    favorite_topics=["AI development", "Smart home automation", "JARVIS systems"],
                    learning_style="hands-on",
                    response_preferences={"length": "concise", "detail_level": "technical"},
                    personal_facts={
                        "has_midea_ac": True,
                        "ac_ip": "************",
                        "prefers_local_ai": True,
                        "uses_ollama": True
                    },
                    goals_and_interests=["Building AI assistants", "Home automation", "Voice control"],
                    conversation_patterns={}
                )
                self._save_user_profile()

    def _initialize_user_knowledge(self):
        """Initialize user knowledge from memories"""
        # Store key facts about the user
        self.store_knowledge(
            topic="User Preferences",
            content="Austin prefers building AI assistants with Mixtral 8x7B via Ollama API, local-only architecture, modular code structure, PyQt6 GUI with JARVIS-style HUD design, and ElevenLabs TTS.",
            source="conversation_history",
            confidence=0.9,
            tags=["user_preferences", "ai_development", "technical_specs"]
        )

        self.store_knowledge(
            topic="Smart Home Setup",
            content="Austin has a Midea AC unit at IP ************ (MAC: 9c:c9:eb:67:bf:b1) and wants JARVIS smart home integration with real device control capabilities.",
            source="conversation_history",
            confidence=0.9,
            tags=["smart_home", "midea_ac", "device_control"]
        )

        self.store_knowledge(
            topic="Development Environment",
            content="Austin has Ollama installed with Mixtral 8x7b model downloaded and running. Prefers .exe executable files for launching applications.",
            source="conversation_history",
            confidence=0.9,
            tags=["development", "ollama", "mixtral", "executables"]
        )
    
    def _save_user_profile(self):
        """Save enhanced user profile to database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO user_profiles
                (user_id, name, preferences, communication_style, emotional_patterns,
                 interaction_history, created_at, updated_at, favorite_topics, learning_style,
                 response_preferences, personal_facts, goals_and_interests, conversation_patterns)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.user_profile.user_id,
                self.user_profile.name,
                json.dumps(self.user_profile.preferences),
                self.user_profile.communication_style,
                json.dumps(self.user_profile.emotional_patterns),
                json.dumps(self.user_profile.interaction_history),
                self.user_profile.created_at,
                datetime.now().isoformat(),
                json.dumps(self.user_profile.favorite_topics),
                self.user_profile.learning_style,
                json.dumps(self.user_profile.response_preferences),
                json.dumps(self.user_profile.personal_facts),
                json.dumps(self.user_profile.goals_and_interests),
                json.dumps(self.user_profile.conversation_patterns)
            ))

    def store_conversation(self, user_message: str, jarvis_response: str,
                          context: Dict[str, Any] = None, importance: float = 0.5) -> str:
        """Store conversation exchange with enhanced metadata"""
        conversation_id = hashlib.md5(f"{user_message}{jarvis_response}{time.time()}".encode()).hexdigest()

        # Analyze emotional tone
        emotional_analysis = self.analyze_emotional_context(user_message)
        emotional_tone = emotional_analysis['valence']

        # Extract tags from conversation
        tags = self._extract_conversation_tags(user_message, jarvis_response)

        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO conversations
                (id, timestamp, user_message, jarvis_response, context, emotional_tone,
                 importance, tags, session_id, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                conversation_id,
                datetime.now().isoformat(),
                user_message,
                jarvis_response,
                json.dumps(context or {}),
                emotional_tone,
                importance,
                json.dumps(tags),
                self.current_session_id,
                self.current_user
            ))

        # Update session message count
        self._update_session_stats()

        # Store as episodic memory
        self.store_memory(
            content=f"User: {user_message}\nJARVIS: {jarvis_response}",
            memory_type="episodic",
            importance=importance,
            emotional_valence=emotional_tone,
            tags=tags,
            context=context or {}
        )

        return conversation_id

    def store_knowledge(self, topic: str, content: str, source: str = "conversation",
                       confidence: float = 0.5, tags: List[str] = None,
                       related_topics: List[str] = None, verified: bool = False) -> str:
        """Store knowledge in the knowledge database"""
        knowledge_id = hashlib.md5(f"{topic}{content}{time.time()}".encode()).hexdigest()

        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO knowledge_base
                (id, topic, content, source, confidence, timestamp, tags, related_topics, verified, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                knowledge_id,
                topic,
                content,
                source,
                confidence,
                datetime.now().isoformat(),
                json.dumps(tags or []),
                json.dumps(related_topics or []),
                verified,
                self.current_user
            ))

        return knowledge_id

    def _extract_conversation_tags(self, user_message: str, jarvis_response: str) -> List[str]:
        """Extract relevant tags from conversation"""
        tags = []
        text = f"{user_message} {jarvis_response}".lower()

        # Technical tags
        if any(word in text for word in ['ac', 'air conditioning', 'midea']):
            tags.append('smart_home')
        if any(word in text for word in ['jarvis', 'ai', 'assistant']):
            tags.append('ai_development')
        if any(word in text for word in ['code', 'programming', 'python']):
            tags.append('programming')
        if any(word in text for word in ['memory', 'remember', 'knowledge']):
            tags.append('memory_system')
        if any(word in text for word in ['voice', 'tts', 'speech']):
            tags.append('voice_control')

        # Emotional tags
        emotional_analysis = self.analyze_emotional_context(text)
        if emotional_analysis['valence'] > 0.3:
            tags.append('positive')
        elif emotional_analysis['valence'] < -0.3:
            tags.append('negative')

        return tags

    def _update_session_stats(self):
        """Update current session statistics"""
        with sqlite3.connect(self.db_path) as conn:
            # Check if session exists
            cursor = conn.execute(
                "SELECT message_count FROM sessions WHERE session_id = ?",
                (self.current_session_id,)
            )
            row = cursor.fetchone()

            if row:
                # Update existing session
                conn.execute("""
                    UPDATE sessions
                    SET message_count = message_count + 1, end_time = ?
                    WHERE session_id = ?
                """, (datetime.now().isoformat(), self.current_session_id))
            else:
                # Create new session
                conn.execute("""
                    INSERT INTO sessions
                    (session_id, user_id, start_time, message_count)
                    VALUES (?, ?, ?, 1)
                """, (self.current_session_id, self.current_user, datetime.now().isoformat()))

    def get_conversation_history(self, limit: int = 10, session_id: str = None) -> List[ConversationEntry]:
        """Get conversation history"""
        with sqlite3.connect(self.db_path) as conn:
            if session_id:
                cursor = conn.execute("""
                    SELECT * FROM conversations
                    WHERE session_id = ? AND user_id = ?
                    ORDER BY timestamp DESC LIMIT ?
                """, (session_id, self.current_user, limit))
            else:
                cursor = conn.execute("""
                    SELECT * FROM conversations
                    WHERE user_id = ?
                    ORDER BY timestamp DESC LIMIT ?
                """, (self.current_user, limit))

            conversations = []
            for row in cursor.fetchall():
                conversations.append(ConversationEntry(
                    id=row[0],
                    timestamp=row[1],
                    user_message=row[2],
                    jarvis_response=row[3],
                    context=json.loads(row[4]) if row[4] else {},
                    emotional_tone=row[5],
                    importance=row[6],
                    tags=json.loads(row[7]) if row[7] else [],
                    session_id=row[8]
                ))

            return list(reversed(conversations))  # Return in chronological order

    def search_knowledge(self, query: str, limit: int = 5) -> List[KnowledgeEntry]:
        """Search knowledge database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM knowledge_base
                WHERE (topic LIKE ? OR content LIKE ? OR tags LIKE ?) AND user_id = ?
                ORDER BY confidence DESC, timestamp DESC LIMIT ?
            """, (f"%{query}%", f"%{query}%", f"%{query}%", self.current_user, limit))

            knowledge_entries = []
            for row in cursor.fetchall():
                knowledge_entries.append(KnowledgeEntry(
                    id=row[0],
                    topic=row[1],
                    content=row[2],
                    source=row[3],
                    confidence=row[4],
                    timestamp=row[5],
                    tags=json.loads(row[6]) if row[6] else [],
                    related_topics=json.loads(row[7]) if row[7] else [],
                    verified=bool(row[8])
                ))

            return knowledge_entries

    def get_contextual_response_data(self, current_message: str) -> Dict[str, Any]:
        """Get contextual data for generating personalized responses"""
        # Get recent conversation context
        recent_conversations = self.get_conversation_history(limit=5)

        # Get relevant knowledge
        relevant_knowledge = self.search_knowledge(current_message, limit=3)

        # Get relevant memories
        relevant_memories = self.recall_memories(current_message, limit=3)

        return {
            "user_profile": asdict(self.user_profile),
            "recent_conversations": [asdict(conv) for conv in recent_conversations],
            "relevant_knowledge": [asdict(knowledge) for knowledge in relevant_knowledge],
            "relevant_memories": [asdict(memory) for memory in relevant_memories],
            "emotional_state": self.emotional_state,
            "session_id": self.current_session_id,
            "conversation_context": self.conversation_context
        }
    
    def store_memory(self, content: str, memory_type: str = "episodic",
                    importance: float = 0.5, emotional_valence: float = 0.0,
                    tags: List[str] = None, context: Dict[str, Any] = None) -> str:
        """Store a new memory with enhanced metadata"""
        memory_id = hashlib.md5(f"{content}{time.time()}".encode()).hexdigest()
        
        memory = MemoryEntry(
            id=memory_id,
            timestamp=datetime.now().isoformat(),
            memory_type=memory_type,
            content=content,
            context=context or {},
            importance=importance,
            emotional_valence=emotional_valence,
            tags=tags or [],
            related_memories=[],
            access_count=0,
            last_accessed=""
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO memories
                (id, timestamp, memory_type, content, context, importance,
                 emotional_valence, tags, related_memories, access_count, last_accessed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                memory.id,
                memory.timestamp,
                memory.memory_type,
                memory.content,
                json.dumps(memory.context),
                memory.importance,
                memory.emotional_valence,
                json.dumps(memory.tags),
                json.dumps(memory.related_memories),
                memory.access_count,
                memory.last_accessed
            ))
        
        return memory_id
    
    def recall_memories(self, query: str, memory_type: str = None,
                       limit: int = 10) -> List[MemoryEntry]:
        """Recall memories based on query with relevance scoring"""
        with sqlite3.connect(self.db_path) as conn:
            sql = """
                SELECT * FROM memories
                WHERE content LIKE ? OR tags LIKE ?
            """
            params = [f"%{query}%", f"%{query}%"]
            
            if memory_type:
                sql += " AND memory_type = ?"
                params.append(memory_type)
            
            sql += " ORDER BY importance DESC, access_count DESC LIMIT ?"
            params.append(limit)
            
            cursor = conn.execute(sql, params)
            rows = cursor.fetchall()
            
            memories = []
            for row in rows:
                memory = MemoryEntry(
                    id=row[0],
                    timestamp=row[1],
                    memory_type=row[2],
                    content=row[3],
                    context=json.loads(row[4]) if row[4] else {},
                    importance=row[5],
                    emotional_valence=row[6],
                    tags=json.loads(row[7]) if row[7] else [],
                    related_memories=json.loads(row[8]) if row[8] else [],
                    access_count=row[9],
                    last_accessed=row[10]
                )
                memories.append(memory)
                
                # Update access count
                conn.execute(
                    "UPDATE memories SET access_count = access_count + 1, last_accessed = ? WHERE id = ?",
                    (datetime.now().isoformat(), memory.id)
                )
            
            return memories
    
    def analyze_emotional_context(self, text: str) -> Dict[str, float]:
        """Analyze emotional context of text"""
        # Simple emotion detection (can be enhanced with NLP)
        positive_words = ['good', 'great', 'excellent', 'happy', 'love', 'amazing', 'wonderful']
        negative_words = ['bad', 'terrible', 'hate', 'angry', 'sad', 'awful', 'horrible']
        
        words = text.lower().split()
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        
        total_emotional_words = positive_count + negative_count
        if total_emotional_words == 0:
            valence = 0.0
        else:
            valence = (positive_count - negative_count) / total_emotional_words
        
        return {
            'valence': valence,
            'arousal': min(total_emotional_words / len(words), 1.0),
            'positive_words': positive_count,
            'negative_words': negative_count
        }
    
    def update_user_preferences(self, preferences: Dict[str, Any]):
        """Update user preferences"""
        self.user_profile.preferences.update(preferences)
        self.user_profile.updated_at = datetime.now().isoformat()
        self._save_user_profile()
    
    def get_contextual_summary(self, limit: int = 5) -> str:
        """Get contextual summary of recent interactions"""
        recent_memories = self.recall_memories("", limit=limit)
        
        if not recent_memories:
            return "No recent context available."
        
        summary_parts = []
        for memory in recent_memories:
            summary_parts.append(f"- {memory.content[:100]}...")
        
        return "Recent context:\n" + "\n".join(summary_parts)
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get comprehensive memory system statistics"""
        with sqlite3.connect(self.db_path) as conn:
            # Memory statistics
            total_memories = conn.execute("SELECT COUNT(*) FROM memories WHERE user_id = ?", (self.current_user,)).fetchone()[0]
            avg_importance = conn.execute("SELECT AVG(importance) FROM memories WHERE user_id = ?", (self.current_user,)).fetchone()[0] or 0
            memory_types = conn.execute("""
                SELECT memory_type, COUNT(*) FROM memories WHERE user_id = ? GROUP BY memory_type
            """, (self.current_user,)).fetchall()

            # Conversation statistics
            total_conversations = conn.execute("SELECT COUNT(*) FROM conversations WHERE user_id = ?", (self.current_user,)).fetchone()[0]
            current_session_messages = conn.execute("""
                SELECT COUNT(*) FROM conversations WHERE session_id = ? AND user_id = ?
            """, (self.current_session_id, self.current_user)).fetchone()[0]

            # Knowledge statistics
            total_knowledge = conn.execute("SELECT COUNT(*) FROM knowledge_base WHERE user_id = ?", (self.current_user,)).fetchone()[0]
            verified_knowledge = conn.execute("SELECT COUNT(*) FROM knowledge_base WHERE verified = 1 AND user_id = ?", (self.current_user,)).fetchone()[0]

            # Session statistics
            total_sessions = conn.execute("SELECT COUNT(*) FROM sessions WHERE user_id = ?", (self.current_user,)).fetchone()[0]

            return {
                "total_memories": total_memories,
                "average_importance": round(avg_importance, 2),
                "memory_types": dict(memory_types),
                "total_conversations": total_conversations,
                "current_session_messages": current_session_messages,
                "total_knowledge_entries": total_knowledge,
                "verified_knowledge": verified_knowledge,
                "total_sessions": total_sessions,
                "user_profile": asdict(self.user_profile),
                "emotional_state": self.emotional_state,
                "session_id": self.current_session_id
            }

    def learn_from_conversation(self, user_message: str, jarvis_response: str):
        """Learn and adapt from conversation"""
        # Analyze conversation for learning opportunities
        text = f"{user_message} {jarvis_response}".lower()

        # Update user preferences based on conversation
        if "prefer" in user_message.lower() or "like" in user_message.lower():
            self._extract_and_store_preference(user_message)

        # Update emotional patterns
        emotional_analysis = self.analyze_emotional_context(user_message)
        self._update_emotional_patterns(emotional_analysis)

        # Update conversation patterns
        self._update_conversation_patterns(user_message, jarvis_response)

        # Store conversation
        self.store_conversation(user_message, jarvis_response)

    def _extract_and_store_preference(self, message: str):
        """Extract and store user preferences from message"""
        # Simple preference extraction (can be enhanced with NLP)
        if "prefer" in message.lower():
            # Extract preference and update user profile
            preference_text = message.lower()
            if "short" in preference_text or "brief" in preference_text:
                self.user_profile.response_preferences["length"] = "short"
            elif "detailed" in preference_text or "long" in preference_text:
                self.user_profile.response_preferences["length"] = "detailed"

            self._save_user_profile()

    def _update_emotional_patterns(self, emotional_analysis: Dict[str, float]):
        """Update user's emotional patterns"""
        timestamp = datetime.now().strftime("%Y-%m-%d")

        if timestamp not in self.user_profile.emotional_patterns:
            self.user_profile.emotional_patterns[timestamp] = []

        self.user_profile.emotional_patterns[timestamp].append(emotional_analysis['valence'])

        # Keep only last 30 days
        cutoff_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        self.user_profile.emotional_patterns = {
            k: v for k, v in self.user_profile.emotional_patterns.items()
            if k >= cutoff_date
        }

        self._save_user_profile()

    def _update_conversation_patterns(self, user_message: str, jarvis_response: str):
        """Update conversation patterns"""
        # Track conversation length preferences
        user_length = len(user_message.split())
        response_length = len(jarvis_response.split())

        if "conversation_lengths" not in self.user_profile.conversation_patterns:
            self.user_profile.conversation_patterns["conversation_lengths"] = []

        self.user_profile.conversation_patterns["conversation_lengths"].append({
            "user_length": user_length,
            "response_length": response_length,
            "timestamp": datetime.now().isoformat()
        })

        # Keep only last 100 conversations
        if len(self.user_profile.conversation_patterns["conversation_lengths"]) > 100:
            self.user_profile.conversation_patterns["conversation_lengths"] = \
                self.user_profile.conversation_patterns["conversation_lengths"][-100:]

        self._save_user_profile()

    def get_personalized_context(self) -> str:
        """Get personalized context for AI responses"""
        context_parts = []

        # User information
        context_parts.append(f"User: {self.user_profile.name}")
        context_parts.append(f"Communication style: {self.user_profile.communication_style}")

        # Preferences
        if self.user_profile.preferences:
            prefs = ", ".join([f"{k}: {v}" for k, v in self.user_profile.preferences.items()])
            context_parts.append(f"Preferences: {prefs}")

        # Recent context
        recent_conversations = self.get_conversation_history(limit=3)
        if recent_conversations:
            context_parts.append("Recent conversation context:")
            for conv in recent_conversations[-3:]:
                context_parts.append(f"- User: {conv.user_message[:50]}...")
                context_parts.append(f"- JARVIS: {conv.jarvis_response[:50]}...")

        return "\n".join(context_parts)
