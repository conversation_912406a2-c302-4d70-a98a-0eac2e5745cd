"""
Qt Compatibility Layer
Allows switching between PyQt6 and PySide6
"""

import sys

# Try PySide6 first (more reliable on Windows), then fall back to PyQt6
try:
    from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                                   QTextEdit, QLineEdit, QPushButton, QLabel, QFrame, QSplitter,
                                   QStatusBar, QGridLayout, QProgressBar, QSizePolicy, QGraphicsOpacityEffect,
                                   QDialog, QTabWidget, QTreeWidget, QTreeWidgetItem, QHeaderView,
                                   QScrollArea, QTextBrowser, QComboBox, QSpinBox, QCheckBox,
                                   QGroupBox, QFormLayout, QSlider, QListWidget, QListWidgetItem,
                                   QGraphicsView, QGraphicsScene, QGraphicsItem, QGraphicsPixmapItem,
                                   QGraphicsTextItem, QGraphicsEllipseItem, QGraphicsRectItem,
                                   QGraphicsDropShadowEffect)
    from PySide6.QtCore import (Qt, QTimer, QPropertyAnimation, QEasingCurve, QPointF, QRectF,
                                QThread, QObject, Signal, Property)
    from PySide6.QtGui import (QFont, QPalette, QColor, QPixmap, QPainter, QBrush, QLinearGradient,
                               QPen, QRadialGradient, QKeySequence, QShortcut, QTextCursor)
    print("Using PySide6 for GUI")
    QT_BACKEND = "PySide6"

    # PySide6 uses different signal syntax
    def pyqtSignal(*args, **kwargs):
        return Signal(*args, **kwargs)

    def pyqtProperty(*args, **kwargs):
        return Property(*args, **kwargs)

except ImportError as e1:
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                                     QTextEdit, QLineEdit, QPushButton, QLabel, QFrame, QSplitter,
                                     QStatusBar, QGridLayout, QProgressBar, QSizePolicy, QGraphicsOpacityEffect,
                                     QDialog, QTabWidget, QTreeWidget, QTreeWidgetItem, QHeaderView,
                                     QScrollArea, QTextBrowser, QComboBox, QSpinBox, QCheckBox,
                                     QGroupBox, QFormLayout, QSlider, QListWidget, QListWidgetItem,
                                     QGraphicsView, QGraphicsScene, QGraphicsItem, QGraphicsPixmapItem,
                                     QGraphicsTextItem, QGraphicsEllipseItem, QGraphicsRectItem,
                                     QGraphicsDropShadowEffect)
        from PyQt6.QtCore import (Qt, QTimer, QPropertyAnimation, QEasingCurve, QPointF, QRectF,
                                  QThread, QObject, pyqtSignal, pyqtProperty)
        from PyQt6.QtGui import (QFont, QPalette, QColor, QPixmap, QPainter, QBrush, QLinearGradient,
                                 QPen, QRadialGradient, QKeySequence, QShortcut, QTextCursor)
        print("Using PyQt6 for GUI")
        QT_BACKEND = "PyQt6"

    except ImportError as e2:
        print("❌ Neither PySide6 nor PyQt6 could be imported!")
        print(f"PySide6 error: {e1}")
        print(f"PyQt6 error: {e2}")
        print("Please install one of them:")
        print("  pip install PySide6")
        print("  or")
        print("  pip install PyQt6")
        sys.exit(1)

# Export commonly used classes for easy importing
__all__ = [
    'QApplication', 'QMainWindow', 'QWidget', 'QVBoxLayout', 'QHBoxLayout',
    'QTextEdit', 'QLineEdit', 'QPushButton', 'QLabel', 'QFrame', 'QSplitter',
    'QStatusBar', 'QGridLayout', 'QProgressBar', 'QSizePolicy', 'QTimer',
    'QDialog', 'QTabWidget', 'QTreeWidget', 'QTreeWidgetItem', 'QHeaderView',
    'QScrollArea', 'QTextBrowser', 'QComboBox', 'QSpinBox', 'QCheckBox',
    'QGroupBox', 'QFormLayout', 'QSlider', 'QListWidget', 'QListWidgetItem',
    'QGraphicsView', 'QGraphicsScene', 'QGraphicsItem', 'QGraphicsPixmapItem',
    'QGraphicsTextItem', 'QGraphicsEllipseItem', 'QGraphicsRectItem',
    'QGraphicsOpacityEffect', 'QGraphicsDropShadowEffect', 'Qt', 'QPropertyAnimation', 'QEasingCurve',
    'QPointF', 'QRectF', 'QPen', 'QBrush', 'QColor', 'QPainter',
    'QRadialGradient', 'QLinearGradient', 'QFont', 'QPalette', 'QPixmap',
    'QThread', 'QObject', 'QKeySequence', 'QShortcut', 'QTextCursor',
    'pyqtSignal', 'pyqtProperty', 'QT_BACKEND'
]
