"""
Enhanced AI Processing System for JARVIS V6
==========================================

Advanced AI processing with multiple providers, caching, parallel processing,
and intelligent model selection for optimal performance.

Features:
- Multi-provider support (Groq, OpenAI, Gemini, Claude, Ollama)
- Response caching for instant repeated queries
- Parallel processing with automatic failover
- Smart model selection based on query type
- Performance monitoring and optimization
- Enhanced progress tracking
"""

import asyncio
import hashlib
import json
import time
import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import requests
import os
from concurrent.futures import ThreadPoolExecutor, as_completed


class QueryType(Enum):
    """Types of queries for smart model selection"""
    SIMPLE_QUESTION = "simple_question"
    COMPLEX_ANALYSIS = "complex_analysis"
    CREATIVE_TASK = "creative_task"
    TECHNICAL_HELP = "technical_help"
    CONVERSATION = "conversation"
    COMMAND = "command"


class AIProvider(Enum):
    """Available AI providers"""
    GROQ = "groq"
    OPENAI = "openai"
    GEMINI = "gemini"
    CLAUDE = "claude"
    OLLAMA = "ollama"


@dataclass
class AIResponse:
    """AI response with metadata"""
    content: str
    provider: AIProvider
    model: str
    response_time: float
    tokens_used: int
    cached: bool
    confidence: float
    timestamp: datetime


@dataclass
class ProviderConfig:
    """Configuration for AI provider"""
    name: str
    api_endpoint: str
    model: str
    max_tokens: int
    timeout: int
    cost_per_token: float
    speed_rating: int  # 1-10, 10 being fastest
    quality_rating: int  # 1-10, 10 being highest quality
    available: bool


class EnhancedAIProcessor:
    """Enhanced AI processing system with multiple providers and optimizations"""
    
    def __init__(self, progress_callback=None):
        self.progress_callback = progress_callback
        self.response_cache = {}
        self.cache_ttl = 3600  # 1 hour cache TTL
        self.performance_stats = {}
        self.executor = ThreadPoolExecutor(max_workers=5)
        
        # Initialize providers
        self.providers = self._initialize_providers()
        self.provider_health = {provider: True for provider in AIProvider}
        
        # Query classification patterns
        self.query_patterns = self._initialize_query_patterns()
        
        # Performance monitoring
        self.response_times = {provider: [] for provider in AIProvider}
        self.success_rates = {provider: 1.0 for provider in AIProvider}
        
        print("🚀 Enhanced AI Processor initialized with multi-provider support")
    
    def _initialize_providers(self) -> Dict[AIProvider, ProviderConfig]:
        """Initialize AI provider configurations"""
        return {
            AIProvider.GROQ: ProviderConfig(
                name="Groq (Llama 3.1 8B)",
                api_endpoint="https://api.groq.com/openai/v1/chat/completions",
                model="llama-3.1-8b-instant",
                max_tokens=512,
                timeout=10,
                cost_per_token=0.0,
                speed_rating=10,
                quality_rating=8,
                available=bool(os.getenv('GROQ_API_KEY'))
            ),
            AIProvider.OPENAI: ProviderConfig(
                name="OpenAI (GPT-4o Mini)",
                api_endpoint="https://api.openai.com/v1/chat/completions",
                model="gpt-4o-mini",
                max_tokens=512,
                timeout=15,
                cost_per_token=0.00015,
                speed_rating=7,
                quality_rating=10,
                available=bool(os.getenv('OPENAI_API_KEY'))
            ),
            AIProvider.GEMINI: ProviderConfig(
                name="Google Gemini 1.5 Flash",
                api_endpoint="https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent",
                model="gemini-1.5-flash",
                max_tokens=512,
                timeout=15,
                cost_per_token=0.0,
                speed_rating=8,
                quality_rating=9,
                available=bool(os.getenv('GEMINI_API_KEY'))
            ),
            AIProvider.CLAUDE: ProviderConfig(
                name="Anthropic Claude",
                api_endpoint="https://api.anthropic.com/v1/messages",
                model="claude-3-haiku-20240307",
                max_tokens=512,
                timeout=20,
                cost_per_token=0.00025,
                speed_rating=6,
                quality_rating=9,
                available=bool(os.getenv('ANTHROPIC_API_KEY'))
            ),
            AIProvider.OLLAMA: ProviderConfig(
                name="Ollama (Mixtral 8x7B)",
                api_endpoint="http://localhost:11434/api/generate",
                model="mixtral:8x7b",
                max_tokens=512,
                timeout=60,
                cost_per_token=0.0,
                speed_rating=4,
                quality_rating=9,
                available=self._check_ollama_available()
            )
        }
    
    def _initialize_query_patterns(self) -> Dict[QueryType, List[str]]:
        """Initialize query classification patterns"""
        return {
            QueryType.SIMPLE_QUESTION: [
                "what is", "who is", "when is", "where is", "how much",
                "define", "explain briefly", "quick question"
            ],
            QueryType.COMPLEX_ANALYSIS: [
                "analyze", "compare", "evaluate", "assess", "review",
                "pros and cons", "advantages and disadvantages"
            ],
            QueryType.CREATIVE_TASK: [
                "write", "create", "generate", "compose", "design",
                "story", "poem", "script", "creative"
            ],
            QueryType.TECHNICAL_HELP: [
                "code", "programming", "debug", "error", "fix",
                "technical", "algorithm", "function", "class"
            ],
            QueryType.CONVERSATION: [
                "hello", "hi", "how are you", "good morning",
                "thank you", "please", "chat", "talk"
            ],
            QueryType.COMMAND: [
                "turn on", "turn off", "set", "adjust", "control",
                "activate", "deactivate", "start", "stop"
            ]
        }
    
    def _check_ollama_available(self) -> bool:
        """Check if Ollama is available"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            return response.status_code == 200
        except:
            return False
    
    def _classify_query(self, query: str) -> QueryType:
        """Classify query type for smart model selection"""
        query_lower = query.lower()
        
        # Score each query type
        scores = {query_type: 0 for query_type in QueryType}
        
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                if pattern in query_lower:
                    scores[query_type] += 1
        
        # Return the highest scoring type, default to CONVERSATION
        if max(scores.values()) > 0:
            return max(scores.items(), key=lambda x: x[1])[0]
        else:
            return QueryType.CONVERSATION
    
    def _get_cache_key(self, query: str, provider: AIProvider) -> str:
        """Generate cache key for query"""
        content = f"{query}_{provider.value}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cached_response(self, cache_key: str) -> Optional[AIResponse]:
        """Get cached response if available and not expired"""
        if cache_key in self.response_cache:
            cached_data = self.response_cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < timedelta(seconds=self.cache_ttl):
                cached_data['cached'] = True
                return AIResponse(**cached_data)
        return None
    
    def _cache_response(self, cache_key: str, response: AIResponse):
        """Cache AI response"""
        self.response_cache[cache_key] = {
            'content': response.content,
            'provider': response.provider,
            'model': response.model,
            'response_time': response.response_time,
            'tokens_used': response.tokens_used,
            'cached': False,
            'confidence': response.confidence,
            'timestamp': response.timestamp
        }
    
    def _select_optimal_provider(self, query_type: QueryType) -> List[AIProvider]:
        """Select optimal providers based on query type and performance"""
        available_providers = [p for p in AIProvider if self.providers[p].available and self.provider_health[p]]
        
        if not available_providers:
            return [AIProvider.OLLAMA]  # Fallback to local
        
        # Score providers based on query type
        provider_scores = {}
        
        for provider in available_providers:
            config = self.providers[provider]
            score = 0
            
            # Base score from speed and quality
            if query_type in [QueryType.SIMPLE_QUESTION, QueryType.CONVERSATION]:
                score = config.speed_rating * 0.7 + config.quality_rating * 0.3
            elif query_type in [QueryType.COMPLEX_ANALYSIS, QueryType.TECHNICAL_HELP]:
                score = config.quality_rating * 0.7 + config.speed_rating * 0.3
            else:
                score = (config.speed_rating + config.quality_rating) / 2
            
            # Adjust for recent performance
            if provider in self.response_times and self.response_times[provider]:
                avg_time = sum(self.response_times[provider][-10:]) / len(self.response_times[provider][-10:])
                if avg_time < 5:
                    score += 2
                elif avg_time > 15:
                    score -= 2
            
            # Adjust for success rate
            score *= self.success_rates[provider]
            
            provider_scores[provider] = score
        
        # Return providers sorted by score (best first)
        return sorted(provider_scores.keys(), key=lambda p: provider_scores[p], reverse=True)
    
    async def _call_provider(self, provider: AIProvider, query: str, system_prompt: str = "") -> Optional[AIResponse]:
        """Call specific AI provider"""
        config = self.providers[provider]
        start_time = time.time()
        
        try:
            if provider == AIProvider.GROQ:
                response = await self._call_groq(query, system_prompt, config)
            elif provider == AIProvider.OPENAI:
                response = await self._call_openai(query, system_prompt, config)
            elif provider == AIProvider.GEMINI:
                response = await self._call_gemini(query, system_prompt, config)
            elif provider == AIProvider.CLAUDE:
                response = await self._call_claude(query, system_prompt, config)
            elif provider == AIProvider.OLLAMA:
                response = await self._call_ollama(query, system_prompt, config)
            else:
                return None
            
            response_time = time.time() - start_time
            
            # Update performance stats
            self.response_times[provider].append(response_time)
            if len(self.response_times[provider]) > 50:
                self.response_times[provider] = self.response_times[provider][-50:]
            
            self.success_rates[provider] = min(1.0, self.success_rates[provider] * 0.95 + 0.05)
            self.provider_health[provider] = True
            
            return AIResponse(
                content=response,
                provider=provider,
                model=config.model,
                response_time=response_time,
                tokens_used=len(response.split()) * 1.3,  # Rough estimate
                cached=False,
                confidence=0.9,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            print(f"❌ {provider.value} error: {e}")
            self.success_rates[provider] = max(0.1, self.success_rates[provider] * 0.8)
            self.provider_health[provider] = False
            return None
    
    async def _call_groq(self, query: str, system_prompt: str, config: ProviderConfig) -> str:
        """Call Groq API"""
        api_key = os.getenv('GROQ_API_KEY')
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": query})
        
        payload = {
            "model": config.model,
            "messages": messages,
            "max_tokens": config.max_tokens,
            "temperature": 0.7
        }
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(config.api_endpoint, json=payload, headers=headers, timeout=config.timeout)
        
        if response.status_code == 200:
            return response.json()['choices'][0]['message']['content'].strip()
        else:
            raise Exception(f"API Error: {response.status_code}")
    
    async def _call_openai(self, query: str, system_prompt: str, config: ProviderConfig) -> str:
        """Call OpenAI API"""
        api_key = os.getenv('OPENAI_API_KEY')
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": query})
        
        payload = {
            "model": config.model,
            "messages": messages,
            "max_tokens": config.max_tokens,
            "temperature": 0.7
        }
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(config.api_endpoint, json=payload, headers=headers, timeout=config.timeout)
        
        if response.status_code == 200:
            return response.json()['choices'][0]['message']['content'].strip()
        else:
            raise Exception(f"API Error: {response.status_code}")
    
    async def _call_gemini(self, query: str, system_prompt: str, config: ProviderConfig) -> str:
        """Call Gemini API"""
        api_key = os.getenv('GEMINI_API_KEY')
        
        prompt = f"{system_prompt}\n\nUser: {query}\n\nJARVIS:" if system_prompt else query
        
        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "maxOutputTokens": config.max_tokens,
                "temperature": 0.7
            }
        }
        
        url = f"{config.api_endpoint}?key={api_key}"
        headers = {"Content-Type": "application/json"}
        
        response = requests.post(url, json=payload, headers=headers, timeout=config.timeout)
        
        if response.status_code == 200:
            return response.json()['candidates'][0]['content']['parts'][0]['text'].strip()
        else:
            raise Exception(f"API Error: {response.status_code}")

    async def _call_claude(self, query: str, system_prompt: str, config: ProviderConfig) -> str:
        """Call Claude API"""
        api_key = os.getenv('ANTHROPIC_API_KEY')

        messages = []
        if system_prompt:
            messages.append({"role": "user", "content": f"{system_prompt}\n\n{query}"})
        else:
            messages.append({"role": "user", "content": query})

        payload = {
            "model": config.model,
            "max_tokens": config.max_tokens,
            "messages": messages
        }

        headers = {
            "x-api-key": api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }

        response = requests.post(config.api_endpoint, json=payload, headers=headers, timeout=config.timeout)

        if response.status_code == 200:
            return response.json()['content'][0]['text'].strip()
        else:
            raise Exception(f"API Error: {response.status_code}")

    async def _call_ollama(self, query: str, system_prompt: str, config: ProviderConfig) -> str:
        """Call Ollama API"""
        prompt = f"{system_prompt}\n\nUser: {query}\n\nJARVIS:" if system_prompt else query
        
        payload = {
            "model": config.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "num_predict": config.max_tokens,
                "temperature": 0.7
            }
        }
        
        response = requests.post(config.api_endpoint, json=payload, timeout=config.timeout)
        
        if response.status_code == 200:
            return response.json()['response'].strip()
        else:
            raise Exception(f"API Error: {response.status_code}")

    async def process_query(self, query: str, system_prompt: str = "", use_cache: bool = True) -> AIResponse:
        """Process query with enhanced AI system"""
        start_time = time.time()

        # Update progress
        if self.progress_callback:
            self.progress_callback(5, "Analyzing query type...")

        # Classify query type
        query_type = self._classify_query(query)
        print(f"🎯 Query classified as: {query_type.value}")

        # Check cache first
        if use_cache:
            if self.progress_callback:
                self.progress_callback(10, "Checking response cache...")

            for provider in AIProvider:
                cache_key = self._get_cache_key(query, provider)
                cached_response = self._get_cached_response(cache_key)
                if cached_response:
                    print(f"⚡ Cache hit! Returning cached response from {provider.value}")
                    if self.progress_callback:
                        self.progress_callback(100, "Retrieved from cache")
                    return cached_response

        # Select optimal providers
        if self.progress_callback:
            self.progress_callback(15, "Selecting optimal AI provider...")

        optimal_providers = self._select_optimal_provider(query_type)
        print(f"🚀 Selected providers (priority order): {[p.value for p in optimal_providers]}")

        # Try providers in order of preference
        for i, provider in enumerate(optimal_providers):
            try:
                if self.progress_callback:
                    progress = 20 + (i * 60 // len(optimal_providers))
                    self.progress_callback(progress, f"Connecting to {self.providers[provider].name}...")

                print(f"🔄 Trying {provider.value}...")
                response = await self._call_provider(provider, query, system_prompt)

                if response:
                    # Cache successful response
                    if use_cache:
                        cache_key = self._get_cache_key(query, provider)
                        self._cache_response(cache_key, response)

                    total_time = time.time() - start_time
                    print(f"✅ Success with {provider.value} in {total_time:.2f}s")

                    if self.progress_callback:
                        self.progress_callback(100, f"Response from {self.providers[provider].name}")

                    return response

            except Exception as e:
                print(f"❌ {provider.value} failed: {e}")
                continue

        # All providers failed - return fallback
        if self.progress_callback:
            self.progress_callback(100, "Using fallback response")

        return AIResponse(
            content="I apologize, sir. All AI systems are currently experiencing difficulties. Please try again in a moment.",
            provider=AIProvider.OLLAMA,
            model="fallback",
            response_time=time.time() - start_time,
            tokens_used=0,
            cached=False,
            confidence=0.1,
            timestamp=datetime.now()
        )

    async def process_query_parallel(self, query: str, system_prompt: str = "", max_providers: int = 3) -> AIResponse:
        """Process query using multiple providers in parallel (fastest wins)"""
        start_time = time.time()

        if self.progress_callback:
            self.progress_callback(5, "Initializing parallel processing...")

        # Classify query and select providers
        query_type = self._classify_query(query)
        optimal_providers = self._select_optimal_provider(query_type)[:max_providers]

        print(f"🚀 Parallel processing with: {[p.value for p in optimal_providers]}")

        if self.progress_callback:
            self.progress_callback(15, f"Querying {len(optimal_providers)} providers simultaneously...")

        # Create tasks for parallel execution
        tasks = []
        for provider in optimal_providers:
            task = asyncio.create_task(self._call_provider(provider, query, system_prompt))
            tasks.append((provider, task))

        # Wait for first successful response
        completed_tasks = []
        for provider, task in tasks:
            try:
                if self.progress_callback:
                    progress = 20 + len(completed_tasks) * 60 // len(tasks)
                    self.progress_callback(progress, f"Waiting for {provider.value}...")

                response = await task
                if response:
                    # Cancel remaining tasks
                    for _, remaining_task in tasks:
                        if not remaining_task.done():
                            remaining_task.cancel()

                    total_time = time.time() - start_time
                    print(f"🏆 Fastest response from {provider.value} in {total_time:.2f}s")

                    if self.progress_callback:
                        self.progress_callback(100, f"Fastest: {self.providers[provider].name}")

                    return response

                completed_tasks.append(provider)

            except Exception as e:
                print(f"❌ {provider.value} failed in parallel: {e}")
                completed_tasks.append(provider)

        # All failed
        if self.progress_callback:
            self.progress_callback(100, "Parallel processing failed")

        return AIResponse(
            content="I apologize, sir. All AI systems are currently experiencing difficulties.",
            provider=AIProvider.OLLAMA,
            model="fallback",
            response_time=time.time() - start_time,
            tokens_used=0,
            cached=False,
            confidence=0.1,
            timestamp=datetime.now()
        )

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        stats = {
            "providers": {},
            "cache_stats": {
                "total_entries": len(self.response_cache),
                "cache_hit_rate": 0.0  # Would need to track this
            },
            "query_types": {qt.value: 0 for qt in QueryType}  # Would need to track this
        }

        for provider in AIProvider:
            if provider in self.response_times and self.response_times[provider]:
                avg_time = sum(self.response_times[provider]) / len(self.response_times[provider])
                stats["providers"][provider.value] = {
                    "available": self.providers[provider].available,
                    "healthy": self.provider_health[provider],
                    "avg_response_time": round(avg_time, 2),
                    "success_rate": round(self.success_rates[provider], 2),
                    "total_requests": len(self.response_times[provider])
                }
            else:
                stats["providers"][provider.value] = {
                    "available": self.providers[provider].available,
                    "healthy": self.provider_health[provider],
                    "avg_response_time": 0,
                    "success_rate": self.success_rates[provider],
                    "total_requests": 0
                }

        return stats

    def clear_cache(self):
        """Clear response cache"""
        self.response_cache.clear()
        print("🗑️ Response cache cleared")

    def update_provider_availability(self):
        """Update provider availability status"""
        for provider in AIProvider:
            if provider == AIProvider.OLLAMA:
                self.providers[provider].available = self._check_ollama_available()
            else:
                # Check if API key exists
                key_map = {
                    AIProvider.GROQ: 'GROQ_API_KEY',
                    AIProvider.OPENAI: 'OPENAI_API_KEY',
                    AIProvider.GEMINI: 'GEMINI_API_KEY',
                    AIProvider.CLAUDE: 'ANTHROPIC_API_KEY'
                }
                if provider in key_map:
                    self.providers[provider].available = bool(os.getenv(key_map[provider]))

    def get_provider_status(self) -> Dict[str, Any]:
        """Get current provider status"""
        self.update_provider_availability()

        status = {}
        for provider in AIProvider:
            config = self.providers[provider]
            status[provider.value] = {
                "name": config.name,
                "available": config.available,
                "healthy": self.provider_health[provider],
                "speed_rating": config.speed_rating,
                "quality_rating": config.quality_rating,
                "success_rate": round(self.success_rates[provider], 2)
            }

        return status
