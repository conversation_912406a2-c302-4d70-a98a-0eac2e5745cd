"""
Helper utilities for Jarvis V6
Common functions and utilities used across the application
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from src.gui.qt_compat import QColor
try:
    from src.gui.qt_compat import QStandardPaths
except ImportError:
    # Handle different import paths between PyQt6 and PySide6
    try:
        from PyQt6.QtCore import QStandardPaths
    except ImportError:
        from PySide6.QtCore import QStandardPaths

def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup application logging"""
    logger = logging.getLogger("JarvisV6")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    
    # Create file handler
    log_dir = get_app_data_dir()
    log_file = os.path.join(log_dir, "jarvis_v6.log")
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger

def get_app_data_dir() -> str:
    """Get the application data directory"""
    app_data = QStandardPaths.writableLocation(QStandardPaths.StandardLocation.AppDataLocation)
    app_dir = os.path.join(app_data, "JarvisV6")
    
    # Create directory if it doesn't exist
    os.makedirs(app_dir, exist_ok=True)
    
    return app_dir

def save_json_file(data: Dict[str, Any], filename: str, directory: Optional[str] = None) -> bool:
    """Save data to a JSON file"""
    try:
        if directory is None:
            directory = get_app_data_dir()
            
        filepath = os.path.join(directory, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        return True
    except Exception as e:
        print(f"Failed to save JSON file {filename}: {e}")
        return False

def load_json_file(filename: str, directory: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """Load data from a JSON file"""
    try:
        if directory is None:
            directory = get_app_data_dir()
            
        filepath = os.path.join(directory, filename)
        
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"Failed to load JSON file {filename}: {e}")
        
    return None

def format_timestamp(timestamp: Optional[datetime] = None) -> str:
    """Format timestamp for display"""
    if timestamp is None:
        timestamp = datetime.now()
        
    return timestamp.strftime("%Y-%m-%d %H:%M:%S")

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to a maximum length"""
    if len(text) <= max_length:
        return text
        
    return text[:max_length - len(suffix)] + suffix

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations"""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
        
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Limit length
    if len(filename) > 255:
        filename = filename[:255]
        
    return filename

def hex_to_qcolor(hex_color: str) -> QColor:
    """Convert hex color string to QColor"""
    if hex_color.startswith('#'):
        hex_color = hex_color[1:]
        
    if len(hex_color) == 6:
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        return QColor(r, g, b)
    elif len(hex_color) == 8:
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        a = int(hex_color[6:8], 16)
        return QColor(r, g, b, a)
    else:
        return QColor()

def qcolor_to_hex(color: QColor) -> str:
    """Convert QColor to hex string"""
    return f"#{color.red():02x}{color.green():02x}{color.blue():02x}"

def get_resource_path(relative_path: str) -> str:
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
        
    return os.path.join(base_path, relative_path)

def ensure_directory_exists(directory: str) -> bool:
    """Ensure a directory exists, create if it doesn't"""
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        print(f"Failed to create directory {directory}: {e}")
        return False

def get_file_size_human(filepath: str) -> str:
    """Get human-readable file size"""
    try:
        size = os.path.getsize(filepath)
        
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
            
        return f"{size:.1f} PB"
    except Exception:
        return "Unknown"

def validate_url(url: str) -> bool:
    """Validate if a string is a valid URL"""
    import re
    
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None

def parse_command_line_args() -> Dict[str, Any]:
    """Parse command line arguments"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Jarvis V6 AI Assistant')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--log-level', default='INFO', help='Set logging level')
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--personality', default='jarvis', 
                       choices=['jarvis', 'tolen', 'alexa'],
                       help='Set personality mode')
    parser.add_argument('--no-plugins', action='store_true', 
                       help='Disable plugin loading')
    
    args = parser.parse_args()
    
    return {
        'debug': args.debug,
        'log_level': args.log_level,
        'config_file': args.config,
        'personality': args.personality,
        'no_plugins': args.no_plugins
    }

def format_error_message(error: Exception) -> str:
    """Format error message for display"""
    error_type = type(error).__name__
    error_message = str(error)
    
    return f"{error_type}: {error_message}"

def is_ollama_running(url: str = "http://localhost:11434") -> bool:
    """Check if Ollama server is running"""
    try:
        import requests
        response = requests.get(f"{url}/api/tags", timeout=5)
        return response.status_code == 200
    except Exception:
        return False
