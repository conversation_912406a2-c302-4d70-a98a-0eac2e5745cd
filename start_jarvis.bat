@echo off
echo 🤖 JARVIS V6 Launcher
echo ==================

REM Change to the script directory
cd /d "%~dp0"

REM Check if virtual environment exists
if not exist ".venv" (
    echo ❌ Virtual environment not found
    echo Please create one with: python -m venv .venv
    pause
    exit /b 1
)

REM Activate virtual environment
echo ⚡ Activating virtual environment...
call .venv\Scripts\activate.bat

REM Check if activation was successful
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment activated

REM Start JARVIS
echo 🚀 Starting JARVIS V6...
python main.py

REM If we get here, JARVIS has exited
echo.
echo 👋 JARVIS V6 has exited
pause
