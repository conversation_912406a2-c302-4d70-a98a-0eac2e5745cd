@echo off
echo Starting Jarvis V6 AI Assistant...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if Ollama is running
echo Checking Ollama connection...
python -c "import requests; requests.get('http://localhost:11434/api/tags', timeout=5)" >nul 2>&1
if errorlevel 1 (
    echo Warning: Ollama server is not running
    echo Please start Ollama and ensure Mixtral model is available
    echo.
    echo To install Ollama and Mixtral:
    echo 1. Download Ollama from https://ollama.ai
    echo 2. Run: ollama pull mixtral:8x7b
    echo 3. Start Ollama service
    echo.
    set /p choice="Continue anyway? (y/n): "
    if /i not "%choice%"=="y" exit /b 1
)

REM Start the application
echo Starting Jarvis V6...
python main.py

pause
