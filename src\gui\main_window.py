"""
Main Window for Jarvis V6 AI Assistant
PyQt6-based GUI with futuristic JARVIS-style interface
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTextEdit, QLineEdit, QPushButton, QLabel, QFrame,
                            QSplitter, QStatusBar)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPalette, QColor, QPixmap, QPainter, QBrush, QLinearGradient
from src.core.config import Config
from src.ai.ollama_client import <PERSON>llamaWorker
from src.gui.animated_background import AnimatedBackground
from src.gui.chat_widget import ChatWidget
from src.gui.input_widget import InputWidget

class JarvisMainWindow(QMainWindow):
    """Main window class for Jarvis V6 AI Assistant"""
    
    def __init__(self):
        super().__init__()
        self.config = Config.load_from_env()
        self.ollama_worker = None
        self.chat_history = []
        
        self.init_ui()
        self.setup_styling()
        self.setup_connections()
        
    def init_ui(self):
        """Initialize the user interface"""
        # Set window properties
        self.setWindowTitle(self.config.WINDOW_TITLE)
        self.setGeometry(100, 100, self.config.WINDOW_WIDTH, self.config.WINDOW_HEIGHT)
        self.setMinimumSize(self.config.WINDOW_MIN_WIDTH, self.config.WINDOW_MIN_HEIGHT)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Create header
        self.create_header(main_layout)
        
        # Create main content area
        self.create_main_content(main_layout)
        
        # Create input area
        self.create_input_area(main_layout)
        
        # Create status bar
        self.create_status_bar()
        
    def create_header(self, parent_layout):
        """Create the header section with title and status"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # Title label
        self.title_label = QLabel("J.A.R.V.I.S. V6")
        self.title_label.setObjectName("titleLabel")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        
        # Status indicator
        self.status_indicator = QLabel("●")
        self.status_indicator.setObjectName("statusIndicator")
        self.status_indicator.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        
        # Personality mode label
        self.personality_label = QLabel(f"Mode: {self.config.PERSONALITY_MODE.title()}")
        self.personality_label.setObjectName("personalityLabel")
        self.personality_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.personality_label)
        header_layout.addWidget(self.status_indicator)
        
        parent_layout.addWidget(header_frame)
        
    def create_main_content(self, parent_layout):
        """Create the main content area with chat display"""
        # Create animated background
        self.animated_background = AnimatedBackground(self.config)
        
        # Create chat widget
        self.chat_widget = ChatWidget(self.config)
        
        # Create splitter for future expandability
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(self.chat_widget)
        splitter.setSizes([800, 400])  # Default sizes
        
        parent_layout.addWidget(splitter, 1)  # Give it stretch factor
        
    def create_input_area(self, parent_layout):
        """Create the input area for user messages"""
        self.input_widget = InputWidget(self.config)
        parent_layout.addWidget(self.input_widget)
        
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Connection status
        self.connection_status = QLabel("Disconnected")
        self.connection_status.setObjectName("connectionStatus")
        self.status_bar.addPermanentWidget(self.connection_status)
        
        # Model info
        self.model_info = QLabel(f"Model: {self.config.OLLAMA_MODEL}")
        self.model_info.setObjectName("modelInfo")
        self.status_bar.addPermanentWidget(self.model_info)
        
    def setup_styling(self):
        """Setup the dark theme styling"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {self.config.THEME_BACKGROUND_COLOR};
                color: {self.config.THEME_TEXT_COLOR};
            }}
            
            #headerFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.config.THEME_BACKGROUND_COLOR},
                    stop:1 rgba(0, 255, 255, 0.1));
                border: 1px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 10px;
                margin: 5px;
            }}
            
            #titleLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {self.config.THEME_PRIMARY_COLOR};
                font-family: 'Consolas', 'Monaco', monospace;
            }}
            
            #statusIndicator {{
                font-size: 20px;
                color: #FF0000;
                margin-left: 10px;
            }}
            
            #personalityLabel {{
                font-size: 12px;
                color: {self.config.THEME_SECONDARY_COLOR};
                margin-right: 15px;
            }}
            
            #connectionStatus, #modelInfo {{
                color: {self.config.THEME_TEXT_COLOR};
                padding: 2px 8px;
                border-radius: 3px;
                background-color: rgba(255, 255, 255, 0.1);
                margin: 2px;
            }}
            
            QStatusBar {{
                background-color: {self.config.THEME_INPUT_BACKGROUND};
                border-top: 1px solid {self.config.THEME_PRIMARY_COLOR};
                color: {self.config.THEME_TEXT_COLOR};
            }}
        """)
        
    def setup_connections(self):
        """Setup signal-slot connections"""
        # Connect input widget signals
        self.input_widget.message_sent.connect(self.handle_user_message)
        
        # Setup status indicator animation
        self.setup_status_animation()
        
    def setup_status_animation(self):
        """Setup pulsing animation for status indicator"""
        self.status_animation = QPropertyAnimation(self.status_indicator, b"styleSheet")
        self.status_animation.setDuration(1000)
        self.status_animation.setLoopCount(-1)  # Infinite loop
        
        # Animation will be started when connected
        
    def handle_user_message(self, message: str):
        """Handle user input message"""
        if not message.strip():
            return
            
        # Add user message to chat
        self.chat_widget.add_user_message(message)
        
        # Clear input
        self.input_widget.clear_input()
        
        # Show typing indicator
        self.chat_widget.show_typing_indicator()
        
        # Start AI processing
        self.process_ai_request(message)
        
    def process_ai_request(self, message: str):
        """Process AI request using Ollama"""
        if self.ollama_worker and self.ollama_worker.isRunning():
            return  # Already processing
            
        # Get system prompt based on personality
        system_prompt = self.config.get_personality_prompt()
        
        # Create and start worker thread
        self.ollama_worker = OllamaWorker(self.config, message, system_prompt)
        self.ollama_worker.response_ready.connect(self.handle_ai_response)
        self.ollama_worker.error_occurred.connect(self.handle_ai_error)
        self.ollama_worker.finished.connect(self.cleanup_worker)
        
        self.ollama_worker.start()
        
    def handle_ai_response(self, response: str):
        """Handle AI response"""
        self.chat_widget.hide_typing_indicator()
        self.chat_widget.add_ai_message(response)
        
        # Update connection status
        self.update_connection_status(True)
        
    def handle_ai_error(self, error: str):
        """Handle AI error"""
        self.chat_widget.hide_typing_indicator()
        self.chat_widget.add_system_message(f"Error: {error}")
        
        # Update connection status
        self.update_connection_status(False)
        
    def cleanup_worker(self):
        """Clean up worker thread"""
        if self.ollama_worker:
            self.ollama_worker.deleteLater()
            self.ollama_worker = None
            
    def update_connection_status(self, connected: bool):
        """Update connection status indicator"""
        if connected:
            self.status_indicator.setStyleSheet("color: #00FF00;")  # Green
            self.connection_status.setText("Connected")
            self.connection_status.setStyleSheet("background-color: rgba(0, 255, 0, 0.2);")
        else:
            self.status_indicator.setStyleSheet("color: #FF0000;")  # Red
            self.connection_status.setText("Disconnected")
            self.connection_status.setStyleSheet("background-color: rgba(255, 0, 0, 0.2);")
            
    def closeEvent(self, event):
        """Handle window close event"""
        # Clean up worker thread
        if self.ollama_worker and self.ollama_worker.isRunning():
            self.ollama_worker.terminate()
            self.ollama_worker.wait()
            
        event.accept()
