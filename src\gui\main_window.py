"""
Main Window for Jarvis V6 AI Assistant
PyQt6-based GUI with futuristic JARVIS-style HUD interface
"""

from src.gui.qt_compat import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTextEdit, QLineEdit, QPushButton, QLabel, QFrame,
                               QSplitter, QStatusBar, QGridLayout, Qt, QTimer,
                               pyqtSignal, QPropertyAnimation, QEasingCurve,
                               QFont, QPalette, QColor, QPixmap, QPainter, QBrush, QLinearGradient)
from src.core.config import Config
from src.ai.ollama_client import OllamaWorker
import os
import threading
from datetime import datetime
from src.ai.training_system import TrainingSystem
from src.ai.self_edit_system import SelfEditSystem
from src.ai.knowledge_base import KnowledgeBase
from src.ai.function_registry import FunctionManager
from src.ai.advanced_memory import AdvancedMemorySystem
from src.ai.self_evolution import SelfEvolutionSystem
from src.gui.animated_background import AnimatedBackground
from src.gui.chat_widget import ChatWidget
from src.gui.input_widget import InputWidget
from src.gui.jarvis_hud import JarvisCore
from src.gui.hud_panels import SystemInfoPanel, TimePanel, StatusPanel
from src.plugins.plugin_manager import PluginManager, VoicePlugin
from src.plugins.smart_home_plugin import SmartHomeManager
from src.ai.smart_home_commands import SmartHomeCommandProcessor

class JarvisMainWindow(QMainWindow):
    """Main window class for Jarvis V6 AI Assistant"""
    
    def __init__(self):
        super().__init__()
        self.config = Config.load_from_env()
        self.ollama_worker = None
        self.chat_history = []

        # Initialize AI systems
        self.training_system = TrainingSystem(self.config)
        self.self_edit_system = SelfEditSystem(self.config)
        self.knowledge_base = KnowledgeBase()
        self.function_manager = FunctionManager()
        self.advanced_memory = AdvancedMemorySystem()
        self.self_evolution = SelfEvolutionSystem(self.config)

        # Initialize Smart Home system
        self.smart_home_manager = SmartHomeManager()
        self.smart_home_processor = SmartHomeCommandProcessor()

        # Initialize plugin manager
        self.plugin_manager = PluginManager(self.config)
        self.tts_plugin = None

        self.init_ui()
        self.setup_styling()
        self.setup_connections()
        self.load_plugins()
        
    def init_ui(self):
        """Initialize the JARVIS HUD interface"""
        # Set window properties
        self.setWindowTitle("J.A.R.V.I.S. - AI INTERFACE")
        self.setGeometry(50, 50, 1400, 900)
        self.setMinimumSize(1200, 800)

        # Create central widget with dark background
        central_widget = QWidget()
        central_widget.setStyleSheet("background-color: #000000;")
        self.setCentralWidget(central_widget)

        # Create main HUD layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Create left panel (HUD panels)
        self.create_left_panel(main_layout)

        # Create center area (JARVIS core + chat)
        self.create_center_area(main_layout)

        # Create right panel (additional HUD elements)
        self.create_right_panel(main_layout)
        
    def create_left_panel(self, parent_layout):
        """Create left HUD panel with system info"""
        left_panel = QWidget()
        left_panel.setFixedWidth(300)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(20)

        # JARVIS title
        title_label = QLabel("J.A.R.V.I.S.")
        title_label.setObjectName("jarvisTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(title_label)

        # System info panel
        self.system_panel = SystemInfoPanel(self.config)
        left_layout.addWidget(self.system_panel)

        # Time panel
        self.time_panel = TimePanel(self.config)
        left_layout.addWidget(self.time_panel)

        # Status panel
        self.status_panel = StatusPanel(self.config)
        left_layout.addWidget(self.status_panel)

        left_layout.addStretch()
        parent_layout.addWidget(left_panel)
        
    def create_center_area(self, parent_layout):
        """Create center area with JARVIS core and chat"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setContentsMargins(0, 0, 0, 0)
        center_layout.setSpacing(20)

        # JARVIS Core visualization
        core_container = QWidget()
        core_container.setFixedHeight(420)
        core_container_layout = QHBoxLayout(core_container)
        core_container_layout.setContentsMargins(0, 0, 0, 0)

        self.jarvis_core = JarvisCore(self.config)
        core_container_layout.addStretch()
        core_container_layout.addWidget(self.jarvis_core)
        core_container_layout.addStretch()

        center_layout.addWidget(core_container)

        # Chat area
        self.chat_widget = ChatWidget(self.config)
        center_layout.addWidget(self.chat_widget, 1)

        # Input area
        self.input_widget = InputWidget(self.config)
        center_layout.addWidget(self.input_widget)

        parent_layout.addWidget(center_widget, 1)
        
    def create_right_panel(self, parent_layout):
        """Create right HUD panel for additional info"""
        right_panel = QWidget()
        right_panel.setFixedWidth(300)
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(20)

        # Personality mode display
        personality_frame = QFrame()
        personality_frame.setObjectName("personalityFrame")
        personality_frame.setFixedHeight(60)
        personality_layout = QVBoxLayout(personality_frame)

        personality_title = QLabel("PERSONALITY MODE")
        personality_title.setObjectName("hudPanelTitle")
        personality_layout.addWidget(personality_title)

        self.personality_label = QLabel(self.config.PERSONALITY_MODE.upper())
        self.personality_label.setObjectName("personalityModeLabel")
        self.personality_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        personality_layout.addWidget(self.personality_label)

        right_layout.addWidget(personality_frame)

        # Add some spacing
        right_layout.addSpacing(20)

        # Add TTS Control Panel to right side
        self.create_tts_control_panel(right_layout)

        # Add more spacing before AI Systems
        right_layout.addSpacing(20)

        # Add AI Systems Panel to right side
        self.create_ai_systems_panel(right_layout)

        right_layout.addStretch()

        parent_layout.addWidget(right_panel)

    def create_tts_control_panel(self, parent_layout):
        """Create TTS control panel"""
        tts_frame = QFrame()
        tts_frame.setObjectName("ttsControlFrame")
        tts_frame.setFixedHeight(80)
        tts_layout = QVBoxLayout(tts_frame)

        tts_title = QLabel("VOICE CONTROL")
        tts_title.setObjectName("hudPanelTitle")
        tts_layout.addWidget(tts_title)

        tts_button_layout = QHBoxLayout()
        self.tts_button = QPushButton("🔊 ENABLED")
        self.tts_button.setObjectName("ttsControlButton")
        self.tts_button.setCheckable(True)
        self.tts_button.setChecked(True)
        tts_button_layout.addWidget(self.tts_button)
        tts_layout.addLayout(tts_button_layout)

        parent_layout.addWidget(tts_frame)

    def create_ai_systems_panel(self, parent_layout):
        """Create AI Systems control panel"""
        ai_frame = QFrame()
        ai_frame.setObjectName("ttsControlFrame")
        ai_frame.setFixedHeight(320)  # Much larger height for labeled buttons
        ai_frame.setMinimumWidth(280)  # Much wider panel
        ai_layout = QVBoxLayout(ai_frame)

        # AI Systems Controls
        ai_systems_title = QLabel("AI SYSTEMS")
        ai_systems_title.setObjectName("hudPanelTitle")
        ai_layout.addWidget(ai_systems_title)

        # Learning System Button
        self.learning_button = QPushButton("🧠 LEARNING SYSTEM")
        self.learning_button.setObjectName("aiSystemButton")
        self.learning_button.setCheckable(True)
        self.learning_button.setChecked(True)
        self.learning_button.setMinimumWidth(260)
        self.learning_button.setMinimumHeight(55)
        self.learning_button.setMaximumHeight(55)
        from src.gui.qt_compat import QSizePolicy
        self.learning_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        ai_layout.addWidget(self.learning_button)

        # Add spacing between buttons
        ai_layout.addSpacing(5)

        # Self-Edit System Button
        self.edit_button = QPushButton("✏️ SELF-EDIT SYSTEM")
        self.edit_button.setObjectName("aiSystemButton")
        self.edit_button.setCheckable(True)
        self.edit_button.setChecked(True)
        self.edit_button.setMinimumWidth(260)
        self.edit_button.setMinimumHeight(55)
        self.edit_button.setMaximumHeight(55)
        self.edit_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        ai_layout.addWidget(self.edit_button)

        # Add spacing between buttons
        ai_layout.addSpacing(5)

        # Advanced Memory Button
        self.memory_button = QPushButton("🧠 MEMORY SYSTEM")
        self.memory_button.setObjectName("aiSystemButton")
        self.memory_button.setCheckable(True)
        self.memory_button.setChecked(True)
        self.memory_button.setMinimumWidth(260)
        self.memory_button.setMinimumHeight(55)
        self.memory_button.setMaximumHeight(55)
        self.memory_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        ai_layout.addWidget(self.memory_button)

        # Self-Evolution Button
        self.evolution_button = QPushButton("🔬 EVOLUTION SYSTEM")
        self.evolution_button.setObjectName("aiSystemButton")
        self.evolution_button.setCheckable(True)
        self.evolution_button.setChecked(False)  # Start disabled for safety
        self.evolution_button.setMinimumWidth(260)
        self.evolution_button.setMinimumHeight(55)
        self.evolution_button.setMaximumHeight(55)
        self.evolution_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        ai_layout.addWidget(self.evolution_button)

        # Add spacing between buttons
        ai_layout.addSpacing(5)

        # Smart Home System Button
        self.smart_home_button = QPushButton("🏠 SMART HOME")
        self.smart_home_button.setObjectName("aiSystemButton")
        self.smart_home_button.setCheckable(True)
        self.smart_home_button.setChecked(False)
        self.smart_home_button.setMinimumWidth(260)
        self.smart_home_button.setMinimumHeight(55)
        self.smart_home_button.setMaximumHeight(55)
        self.smart_home_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.smart_home_button.clicked.connect(self.toggle_smart_home_system)
        ai_layout.addWidget(self.smart_home_button)

        parent_layout.addWidget(ai_frame)
        
    def setup_styling(self):
        """Setup the JARVIS HUD styling"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #000000;
                color: {self.config.THEME_TEXT_COLOR};
            }}

            #jarvisTitle {{
                font-size: 32px;
                font-weight: bold;
                color: {self.config.THEME_PRIMARY_COLOR};
                font-family: 'Consolas', 'Monaco', monospace;
                text-align: center;
                letter-spacing: 3px;
                margin: 20px 0;
            }}

            #personalityFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 50, 100, 0.8),
                    stop:1 rgba(0, 30, 60, 0.6));
                border: 2px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 10px;
            }}

            #personalityModeLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {self.config.THEME_ACCENT_COLOR};
                font-family: 'Consolas', 'Monaco', monospace;
            }}

            #ttsControlFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 50, 100, 0.8),
                    stop:1 rgba(0, 30, 60, 0.6));
                border: 2px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 10px;
            }}

            #ttsControlButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.config.THEME_PRIMARY_COLOR},
                    stop:1 {self.config.THEME_SECONDARY_COLOR});
                border: none;
                border-radius: 8px;
                color: #000000;
                font-weight: bold;
                font-size: 12px;
                padding: 8px 16px;
                font-family: 'Consolas', 'Monaco', monospace;
            }}

            #ttsControlButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FF00,
                    stop:1 #00AA00);
            }}

            #ttsControlButton:!checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF6600,
                    stop:1 #CC4400);
            }}

            #ttsControlButton:hover {{
                border: 2px solid rgba(0, 255, 255, 0.8);
            }}

            #aiSystemButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.config.THEME_PRIMARY_COLOR},
                    stop:1 {self.config.THEME_SECONDARY_COLOR});
                border: none;
                border-radius: 8px;
                color: #FFFFFF;
                font-weight: bold;
                font-size: 14px;
                padding: 15px 20px;
                font-family: 'Consolas', 'Monaco', monospace;
                text-align: center;
                min-width: 260px;
                min-height: 55px;
                max-width: 260px;
                max-height: 55px;
            }}

            #aiSystemButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FF00,
                    stop:1 #00AA00);
                color: #000000;
            }}

            #aiSystemButton:!checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF6600,
                    stop:1 #CC4400);
                color: #FFFFFF;
            }}

            #aiSystemButton:hover {{
                border: 2px solid rgba(0, 255, 255, 0.8);
            }}

            #hudPanelTitle {{
                color: {self.config.THEME_PRIMARY_COLOR};
                font-size: 12px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                text-transform: uppercase;
                letter-spacing: 1px;
                text-align: center;
                margin: 5px 0;
            }}
        """)
        
    def setup_connections(self):
        """Setup signal-slot connections"""
        # Connect input widget signals
        self.input_widget.message_sent.connect(self.handle_user_message)
        self.input_widget.special_command.connect(self.handle_special_command)

        # Connect TTS button
        self.tts_button.clicked.connect(self.toggle_tts)

        # Connect AI system buttons
        self.learning_button.clicked.connect(self.toggle_learning)
        self.edit_button.clicked.connect(self.toggle_self_edit)
        self.memory_button.clicked.connect(self.toggle_advanced_memory)
        self.evolution_button.clicked.connect(self.toggle_self_evolution)

        # Setup status indicator animation
        self.setup_status_animation()
        
    def setup_status_animation(self):
        """Setup pulsing animation for status indicator"""
        # Status animation is now handled by the HUD panels
        pass
        
    def handle_user_message(self, message: str):
        """Handle user input message"""
        if not message.strip():
            return

        # Add user message to chat
        self.chat_widget.add_user_message(message)

        # Clear input
        self.input_widget.clear_input()

        # Check for smart home commands first
        smart_home_command = self.smart_home_processor.parse_command(message)
        if smart_home_command and smart_home_command.confidence > 0.7:
            self.handle_smart_home_command(smart_home_command)
            return

        # Show typing indicator with loading message
        self.chat_widget.show_typing_indicator()
        self.chat_widget.add_system_message("🧠 Mixtral 8x7B is processing your request... (This may take up to 2 minutes for the first request)")

        # Start AI processing
        self.process_ai_request(message)

    def handle_smart_home_command(self, command):
        """Handle smart home commands"""
        import asyncio

        def run_smart_home_command():
            """Run smart home command in async context"""
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                if command.command_type.value == "turn_on":
                    result = loop.run_until_complete(self.smart_home_manager.turn_on_device(command.target))
                    if result:
                        response = f"✅ Turned on {command.target}"
                    else:
                        response = f"❌ Could not turn on {command.target}. Device may not be found or connected."

                elif command.command_type.value == "turn_off":
                    result = loop.run_until_complete(self.smart_home_manager.turn_off_device(command.target))
                    if result:
                        response = f"✅ Turned off {command.target}"
                    else:
                        response = f"❌ Could not turn off {command.target}. Device may not be found or connected."

                elif command.command_type.value == "room_control":
                    controlled = loop.run_until_complete(self.smart_home_manager.control_room(command.target, command.value))
                    if controlled > 0:
                        response = f"✅ Controlled {controlled} device(s) in {command.target}"
                    else:
                        response = f"❌ No devices found in {command.target} or connection failed"

                elif command.command_type.value == "set_temperature":
                    device = self.smart_home_manager.find_device_by_name(command.target)
                    if device and hasattr(device, 'target_temperature'):
                        platform_key = device.platform.lower().replace(" ", "_").replace("-", "_")
                        platform = self.smart_home_manager.platforms.get(platform_key)
                        if platform and hasattr(platform, 'set_temperature'):
                            result = loop.run_until_complete(platform.set_temperature(device.id, command.value))
                            if result:
                                response = f"🌡️ Set {device.name} to {command.value}°C"
                            else:
                                response = f"❌ Could not set temperature for {device.name}"
                        else:
                            response = f"❌ Temperature control not supported for {device.name}"
                    else:
                        response = f"❌ Device '{command.target}' not found or doesn't support temperature control"

                elif command.command_type.value == "set_ac_mode":
                    device = self.smart_home_manager.find_device_by_name(command.target)
                    if device and device.device_type.value == "ac":
                        platform_key = device.platform.lower().replace(" ", "_").replace("-", "_")
                        platform = self.smart_home_manager.platforms.get(platform_key)
                        if platform and hasattr(platform, 'set_mode'):
                            result = loop.run_until_complete(platform.set_mode(device.id, command.value))
                            if result:
                                response = f"🌡️ Set {device.name} to {command.value} mode"
                            else:
                                response = f"❌ Could not set mode for {device.name}"
                        else:
                            response = f"❌ Mode control not supported for {device.name}"
                    else:
                        response = f"❌ AC '{command.target}' not found"

                elif command.command_type.value == "status":
                    if command.target == "all":
                        status = self.smart_home_manager.get_device_status()
                        response = f"🏠 Smart Home Status:\n"
                        response += f"📱 Total Devices: {status['total_devices']}\n"
                        response += f"🔌 Platforms: {status['platforms']}\n"
                        response += f"🏠 Rooms: {status['rooms']}\n"
                        for room, devices in status['devices_by_room'].items():
                            if devices:
                                response += f"\n{room}:\n"
                                for device in devices:
                                    state_icon = "🟢" if device['state'] == "on" else "🔴"
                                    response += f"  {state_icon} {device['name']} ({device['type']})\n"
                    else:
                        device = self.smart_home_manager.find_device_by_name(command.target)
                        if device:
                            state_icon = "🟢" if device.state.value == "on" else "🔴"
                            response = f"{state_icon} {device.name}: {device.state.value.upper()}"
                        else:
                            response = f"❌ Device '{command.target}' not found"

                else:
                    response = f"🔧 Smart home command recognized but not yet implemented: {command.command_type.value}"

                # Add response to chat
                self.chat_widget.add_ai_message(response)

                # Speak response if TTS is enabled
                if self.tts_button.isChecked() and hasattr(self, 'tts_plugin') and self.tts_plugin:
                    self.tts_plugin.speak(response)

                loop.close()

            except Exception as e:
                error_msg = f"❌ Smart home error: {str(e)}"
                self.chat_widget.add_system_message(error_msg)

        # Run in separate thread to avoid blocking UI
        threading.Thread(target=run_smart_home_command, daemon=True).start()

    def toggle_smart_home_system(self):
        """Toggle smart home system on/off"""
        if self.smart_home_button.isChecked():
            # Initialize smart home system
            def init_smart_home():
                import asyncio
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    success = loop.run_until_complete(self.smart_home_manager.initialize())
                    loop.close()

                    if success:
                        self.chat_widget.add_system_message("🏠 Smart Home system initialized successfully")
                        self.smart_home_button.setText("🏠 SMART HOME ✓")
                    else:
                        self.chat_widget.add_system_message("⚠️ Smart Home system initialization failed. Check configuration.")
                        self.smart_home_button.setChecked(False)

                except Exception as e:
                    self.chat_widget.add_system_message(f"❌ Smart Home error: {str(e)}")
                    self.smart_home_button.setChecked(False)

            threading.Thread(target=init_smart_home, daemon=True).start()
        else:
            self.smart_home_button.setText("🏠 SMART HOME")
            self.chat_widget.add_system_message("🏠 Smart Home system disabled")

    def process_ai_request(self, message: str):
        """Process AI request using Ollama with enhanced AI systems"""
        if self.ollama_worker and self.ollama_worker.isRunning():
            return  # Already processing

        # Store message for training
        self.current_user_message = message

        # Get relevant knowledge context
        knowledge_context = self.knowledge_base.get_relevant_context(message)

        # Get system prompt with knowledge context
        system_prompt = self.config.get_personality_prompt()
        if knowledge_context:
            system_prompt += f"\n\nRelevant context: {knowledge_context}"

        # Create and start worker thread
        self.ollama_worker = OllamaWorker(self.config, message, system_prompt)
        self.ollama_worker.response_ready.connect(self.handle_ai_response_enhanced)
        self.ollama_worker.error_occurred.connect(self.handle_ai_error)
        self.ollama_worker.finished.connect(self.cleanup_worker)

        self.ollama_worker.start()
        
    def handle_ai_response_enhanced(self, response: str):
        """Handle AI response with enhanced processing"""
        import asyncio

        # Process response through self-edit system
        try:
            # Run async edit in a thread
            def run_edit():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    edited_response, suggestions = loop.run_until_complete(
                        self.self_edit_system.edit_response(response)
                    )
                    return edited_response
                finally:
                    loop.close()

            # Use edited response if available
            import threading
            edit_thread = threading.Thread(target=run_edit)
            edit_thread.start()
            edit_thread.join(timeout=2)  # Quick edit timeout

            final_response = response  # Default to original

        except Exception as e:
            print(f"Self-edit error: {e}")
            final_response = response

        # Hide typing indicator and show response
        self.chat_widget.hide_typing_indicator()
        self.chat_widget.add_ai_message(final_response)

        # Record conversation for training
        if hasattr(self, 'current_user_message'):
            self.training_system.record_conversation(
                self.current_user_message,
                final_response
            )

            # Learn from conversation
            self.knowledge_base.learn_from_conversation(
                self.current_user_message,
                final_response
            )

            # Store in advanced memory with emotional analysis
            if self.memory_button.isChecked():
                emotional_context = self.advanced_memory.analyze_emotional_context(
                    self.current_user_message + " " + final_response
                )

                self.advanced_memory.store_memory(
                    content=f"User: {self.current_user_message}\nJARVIS: {final_response}",
                    memory_type="episodic",
                    importance=0.7,
                    emotional_valence=emotional_context['valence'],
                    tags=["conversation", "user_interaction"],
                    context={
                        "emotional_analysis": emotional_context,
                        "timestamp": datetime.now().isoformat()
                    }
                )

        # Speak the response if TTS is enabled
        if self.tts_button.isChecked() and self.tts_plugin:
            # Show immediate visual feedback for TTS preparation
            self.status_panel.update_tts_status("GENERATING", "#FFAA00")
            self.tts_plugin.speak(final_response)

        # Update status
        self.status_panel.update_ai_status("ONLINE", "#00FF00")
        self.update_connection_status(True)

    def handle_ai_response(self, response: str):
        """Legacy handler - redirect to enhanced version"""
        self.handle_ai_response_enhanced(response)
        
    def handle_ai_error(self, error: str):
        """Handle AI error"""
        self.chat_widget.hide_typing_indicator()
        self.chat_widget.add_system_message(f"System Error: {error}")

        # Update connection status
        self.update_connection_status(False)

        # Reset status after error
        QTimer.singleShot(3000, lambda: self.status_panel.update_ai_status("ONLINE", "#00FF00"))
        
    def cleanup_worker(self):
        """Clean up worker thread"""
        if self.ollama_worker:
            self.ollama_worker.deleteLater()
            self.ollama_worker = None
            
    def update_connection_status(self, connected: bool):
        """Update connection status"""
        if connected:
            self.status_panel.update_ai_status("ONLINE", "#00FF00")
        else:
            self.status_panel.update_ai_status("OFFLINE", "#FF0000")
            
    def load_plugins(self):
        """Load and initialize plugins"""
        try:
            # Load ElevenLabs TTS plugin
            from src.plugins.elevenlabs_tts_plugin import ElevenLabsTTSPlugin

            tts_plugin = ElevenLabsTTSPlugin()
            if tts_plugin.initialize(self.config):
                self.tts_plugin = tts_plugin

                # Connect TTS signals
                self.tts_plugin.signals.speech_started.connect(self.on_speech_started)
                self.tts_plugin.signals.speech_finished.connect(self.on_speech_finished)
                self.tts_plugin.signals.speech_error.connect(self.on_speech_error)

                print("ElevenLabs TTS plugin loaded successfully")
            else:
                print("Failed to initialize ElevenLabs TTS plugin")

        except Exception as e:
            print(f"Failed to load TTS plugin: {e}")
            self.tts_button.setEnabled(False)
            self.tts_button.setToolTip("TTS plugin not available")

    def toggle_tts(self):
        """Toggle TTS on/off"""
        if self.tts_plugin and self.tts_plugin.is_currently_speaking():
            self.tts_plugin.stop_speaking()

        # Update button text
        if self.tts_button.isChecked():
            self.tts_button.setText("🔊 ENABLED")
            self.status_panel.update_tts_status("READY", self.config.THEME_PRIMARY_COLOR)
        else:
            self.tts_button.setText("🔇 DISABLED")
            self.status_panel.update_tts_status("DISABLED", "#888888")

    def on_speech_started(self):
        """Handle speech started - when audio actually begins playing"""
        self.tts_button.setText("🔇 SPEAKING")
        self.status_panel.update_tts_status("SPEAKING", "#00FF00")

        # Activate JARVIS core speaking mode with immediate response
        self.jarvis_core.set_speaking_mode(True)

    def on_speech_finished(self):
        """Handle speech finished"""
        self.tts_button.setText("🔊 ENABLED")
        self.status_panel.update_tts_status("READY", self.config.THEME_PRIMARY_COLOR)

        # Deactivate JARVIS core speaking mode
        self.jarvis_core.set_speaking_mode(False)

    def on_speech_error(self, error: str):
        """Handle speech error"""
        self.chat_widget.add_system_message(f"TTS Error: {error}")
        self.status_panel.update_tts_status("ERROR", "#FF0000")
        self.on_speech_finished()

    def toggle_learning(self):
        """Toggle learning system on/off"""
        enabled = self.learning_button.isChecked()
        self.training_system.toggle_learning(enabled)
        self.knowledge_base.toggle_learning(enabled)

        if enabled:
            self.learning_button.setText("🧠 LEARNING")
            self.chat_widget.add_system_message("Learning system enabled")
        else:
            self.learning_button.setText("🧠 DISABLED")
            self.chat_widget.add_system_message("Learning system disabled")

    def toggle_self_edit(self):
        """Toggle self-edit system on/off"""
        enabled = self.edit_button.isChecked()
        self.self_edit_system.toggle_editing(enabled)

        if enabled:
            self.edit_button.setText("✏️ SELF-EDIT")
            self.chat_widget.add_system_message("Self-edit system enabled")
        else:
            self.edit_button.setText("✏️ DISABLED")
            self.chat_widget.add_system_message("Self-edit system disabled")

    def get_ai_systems_status(self) -> str:
        """Get status of all AI systems"""
        training_stats = self.training_system.get_training_stats()
        knowledge_stats = self.knowledge_base.get_stats()
        edit_stats = self.self_edit_system.get_system_stats()
        function_stats = self.function_manager.registry.get_stats()

        status = f"""AI Systems Status:

Training System:
- Conversations: {training_stats['conversations_recorded']}
- Patterns: {training_stats['patterns_learned']}
- Average Rating: {training_stats['average_rating']}
- Learning: {'ON' if training_stats['learning_enabled'] else 'OFF'}

Knowledge Base:
- Entries: {knowledge_stats['total_entries']}
- Avg Confidence: {knowledge_stats['average_confidence']}
- Total Accesses: {knowledge_stats['total_accesses']}
- Learning: {'ON' if knowledge_stats['learning_enabled'] else 'OFF'}

Self-Edit System:
- Editing: {'ON' if edit_stats['edit_enabled'] else 'OFF'}
- Auto-Apply: {'ON' if edit_stats['auto_apply_edits'] else 'OFF'}
- Quality Threshold: {edit_stats['quality_threshold']}
- Active Rules: {edit_stats['active_rules']}/{edit_stats['total_rules']}

Function Registry:
- Total Functions: {function_stats['total_functions']}
- Enabled: {function_stats['enabled_functions']}
- Total Usage: {function_stats['total_usage']}
- Categories: {', '.join(function_stats['categories'])}"""

        return status

    def toggle_advanced_memory(self):
        """Toggle advanced memory system on/off"""
        enabled = self.memory_button.isChecked()

        if enabled:
            self.memory_button.setText("🧠 MEMORY")
            self.chat_widget.add_system_message("Advanced memory system enabled")

            # Get memory summary
            memory_stats = self.advanced_memory.get_memory_stats()
            self.chat_widget.add_system_message(
                f"Memory: {memory_stats['total_memories']} memories stored"
            )
        else:
            self.memory_button.setText("🧠 DISABLED")
            self.chat_widget.add_system_message("Advanced memory system disabled")

    def toggle_self_evolution(self):
        """Toggle self-evolution system on/off"""
        enabled = self.evolution_button.isChecked()
        self.self_evolution.toggle_evolution(enabled)

        if enabled:
            self.evolution_button.setText("🔬 EVOLUTION")
            self.chat_widget.add_system_message("⚠️ Self-evolution system enabled")
            self.chat_widget.add_system_message("JARVIS can now analyze and improve its own code")

            # Start code analysis in background
            threading.Thread(target=self._analyze_system_code, daemon=True).start()
        else:
            self.evolution_button.setText("🔬 DISABLED")
            self.chat_widget.add_system_message("Self-evolution system disabled")

    def _analyze_system_code(self):
        """Analyze system code for improvements (background task)"""
        try:
            # Analyze key files
            files_to_analyze = [
                'src/ai/ollama_client.py',
                'src/ai/training_system.py',
                'src/gui/chat_widget.py'
            ]

            for file_path in files_to_analyze:
                if os.path.exists(file_path):
                    analysis = self.self_evolution.analyze_code_file(file_path)
                    if analysis and analysis.suggestions:
                        # Create evolution tasks for improvements
                        for suggestion in analysis.suggestions[:2]:  # Limit to 2 per file
                            self.self_evolution.create_evolution_task(
                                task_type="code_improvement",
                                description=f"{file_path}: {suggestion}",
                                priority=5,
                                complexity=3
                            )

            # Notify user of analysis completion
            QTimer.singleShot(1000, lambda: self.chat_widget.add_system_message(
                "Code analysis complete. Evolution tasks created."
            ))

        except Exception as e:
            print(f"Code analysis error: {e}")

    def get_advanced_systems_status(self) -> str:
        """Get comprehensive status of all advanced AI systems"""
        training_stats = self.training_system.get_training_stats()
        memory_stats = self.advanced_memory.get_memory_stats()
        evolution_stats = self.self_evolution.get_evolution_stats()

        status = f"""🤖 JARVIS V6 Advanced AI Systems Status:

📊 Training System:
- Conversations: {training_stats['conversations_recorded']}
- Patterns Learned: {training_stats['patterns_learned']}
- Average Rating: {training_stats['average_rating']}/5
- Learning: {'🟢 ACTIVE' if training_stats['learning_enabled'] else '🔴 DISABLED'}

🧠 Advanced Memory:
- Total Memories: {memory_stats['total_memories']}
- Memory Types: {', '.join(memory_stats['memory_types'].keys())}
- User Profile: {memory_stats['user_profile']['name']}
- Emotional State: {memory_stats['emotional_state']['valence']:.2f}

🔬 Self-Evolution:
- Evolution: {'🟢 ACTIVE' if evolution_stats['evolution_enabled'] else '🔴 DISABLED'}
- Safety Mode: {'🟢 ON' if evolution_stats['safety_mode'] else '🔴 OFF'}
- Tasks Completed: {evolution_stats['completed_tasks']}/{evolution_stats['total_tasks']}
- Success Rate: {evolution_stats['success_rate']:.1%}
- Code Complexity: {evolution_stats['average_complexity']:.2f}

⚙️ Function Registry:
- Available Functions: {len(self.function_manager.get_available_functions())}
- Categories: {', '.join(self.function_manager.registry.get_categories())}

🎯 Overall Status: {'🟢 FULLY OPERATIONAL' if all([
    training_stats['learning_enabled'],
    memory_stats['total_memories'] > 0,
    len(self.function_manager.get_available_functions()) > 0
]) else '🟡 PARTIALLY ACTIVE'}"""

        return status

    def handle_special_command(self, command: str):
        """Handle special commands from input widget"""
        if command == 'show_ai_status':
            status = self.get_advanced_systems_status()
            self.chat_widget.add_system_message(status)

        elif command == 'show_memory_status':
            if hasattr(self, 'advanced_memory'):
                stats = self.advanced_memory.get_memory_stats()
                memory_status = f"""🧠 Advanced Memory System Status:

Total Memories: {stats['total_memories']}
Memory Types: {', '.join(stats['memory_types'].keys()) if stats['memory_types'] else 'None'}
User Profile: {stats['user_profile']['name']}
Communication Style: {stats['user_profile']['communication_style']}
Emotional State: Valence {stats['emotional_state']['valence']:.2f}

Recent Context:
{self.advanced_memory.get_contextual_summary(3)}"""
                self.chat_widget.add_system_message(memory_status)
            else:
                self.chat_widget.add_system_message("Advanced memory system not available")

        elif command == 'show_evolution_status':
            if hasattr(self, 'self_evolution'):
                stats = self.self_evolution.get_evolution_stats()
                evolution_status = f"""🔬 Self-Evolution System Status:

Evolution Enabled: {'🟢 YES' if stats['evolution_enabled'] else '🔴 NO'}
Safety Mode: {'🟢 ON' if stats['safety_mode'] else '🔴 OFF'}
Total Tasks: {stats['total_tasks']}
Completed Tasks: {stats['completed_tasks']}
Success Rate: {stats['success_rate']:.1%}
Average Code Complexity: {stats['average_complexity']:.2f}
Modifiable Files: {stats['modifiable_files']}
Protected Files: {stats['protected_files']}

⚠️ Self-evolution allows JARVIS to analyze and improve its own code.
Use with caution and keep safety mode enabled."""
                self.chat_widget.add_system_message(evolution_status)
            else:
                self.chat_widget.add_system_message("Self-evolution system not available")

        elif command == 'show_training_status':
            stats = self.training_system.get_training_stats()
            recommendations = self.training_system.get_training_recommendations()

            training_status = f"""🧠 Enhanced Training System Status:

Conversations Recorded: {stats['conversations_recorded']}
Patterns Learned: {stats['patterns_learned']}
Average Rating: {stats['average_rating']:.2f}/5
User Satisfaction: {stats.get('user_satisfaction', 0):.2f}
Learning Enabled: {'🟢 YES' if stats['learning_enabled'] else '🔴 NO'}

Training Recommendations:
{chr(10).join(f'• {rec}' for rec in recommendations[:3]) if recommendations else '• No recommendations at this time'}

Active Sessions: {len(getattr(self.training_system, 'active_sessions', {}))}"""
            self.chat_widget.add_system_message(training_status)

        elif command == 'show_help':
            help_text = """🤖 JARVIS V6 Special Commands:

/status, /systems, /ai-status - Show all AI systems status
/memory, /memories - Show advanced memory system status
/evolution, /evolve - Show self-evolution system status
/training, /learn - Show enhanced training system status
/help, /commands - Show this help message

🎛️ Control Panel:
• 🔊 Voice Output - Toggle TTS on/off
• 🧠 Learning - Toggle learning systems
• ✏️ Self-Edit - Toggle response improvement
• 🧠 Memory - Toggle advanced memory
• 🔬 Evolution - Toggle self-evolution (⚠️ Advanced)

💡 Tips:
- Use concise messages for faster responses
- JARVIS learns from your conversations
- Toggle systems based on your needs
- Evolution mode allows JARVIS to improve itself

🎯 Current Status: All systems operational"""
            self.chat_widget.add_system_message(help_text)

        else:
            self.chat_widget.add_system_message(f"Unknown command: {command}")

    def closeEvent(self, event):
        """Handle window close event"""
        # Clean up worker thread
        if self.ollama_worker and self.ollama_worker.isRunning():
            self.ollama_worker.terminate()
            self.ollama_worker.wait()

        # Clean up plugins
        if self.tts_plugin:
            self.tts_plugin.cleanup()

        if self.plugin_manager:
            self.plugin_manager.cleanup_all_plugins()

        event.accept()
