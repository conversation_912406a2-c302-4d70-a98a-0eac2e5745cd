"""
DeepSeek-Coder V2 Service for JARVIS V6
=======================================

Specialized coding service using DeepSeek-Coder V2 model for:
- Code analysis and review
- Code improvements and optimization
- Bug detection and fixing
- Code generation and completion
- Refactoring suggestions
- Documentation generation
- Code explanation and teaching

This service provides expert-level programming assistance across multiple languages.
"""

import requests
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import re
import os

class CodeTaskType(Enum):
    """Types of coding tasks"""
    ANALYZE = "analyze"
    IMPROVE = "improve"
    FIX_BUG = "fix_bug"
    GENERATE = "generate"
    REFACTOR = "refactor"
    DOCUMENT = "document"
    EXPLAIN = "explain"
    REVIEW = "review"
    OPTIMIZE = "optimize"
    TEST = "test"

class ProgrammingLanguage(Enum):
    """Supported programming languages"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CPP = "cpp"
    CSHARP = "csharp"
    GO = "go"
    RUST = "rust"
    PHP = "php"
    RUBY = "ruby"
    SWIFT = "swift"
    KOTLIN = "kotlin"
    HTML = "html"
    CSS = "css"
    SQL = "sql"
    BASH = "bash"
    POWERSHELL = "powershell"

@dataclass
class CodeTask:
    """Represents a coding task"""
    task_type: CodeTaskType
    language: ProgrammingLanguage
    code: str
    description: str
    context: Optional[str] = None
    requirements: Optional[List[str]] = None

@dataclass
class CodeResult:
    """Result from DeepSeek-Coder V2"""
    success: bool
    improved_code: Optional[str]
    explanation: str
    suggestions: List[str]
    confidence: float
    processing_time: float
    error_message: Optional[str] = None

class DeepSeekCoderService:
    """
    DeepSeek-Coder V2 Service for advanced code editing and improvements
    
    This service provides specialized programming assistance using the
    DeepSeek-Coder V2 model, which is specifically trained for coding tasks.
    """
    
    def __init__(self, api_endpoint: str = "http://localhost:11434", model_name: str = "deepseek-coder:6.7b"):
        self.api_endpoint = api_endpoint
        self.model_name = model_name
        self.session_history = []
        self.total_requests = 0
        self.successful_requests = 0
        
        print(f"🔧 DeepSeek-Coder V2 Service initialized")
        print(f"📡 API Endpoint: {api_endpoint}")
        print(f"🤖 Model: {model_name}")
    
    def detect_language(self, code: str) -> ProgrammingLanguage:
        """Detect programming language from code"""
        try:
            # Simple language detection based on patterns
            code_lower = code.lower()
            
            # Python indicators
            if any(keyword in code for keyword in ['def ', 'import ', 'from ', 'class ', '__init__', 'self.']):
                return ProgrammingLanguage.PYTHON
            
            # JavaScript/TypeScript indicators
            if any(keyword in code for keyword in ['function ', 'const ', 'let ', 'var ', '=>', 'console.log']):
                if 'interface ' in code or 'type ' in code or ': string' in code:
                    return ProgrammingLanguage.TYPESCRIPT
                return ProgrammingLanguage.JAVASCRIPT
            
            # Java indicators
            if any(keyword in code for keyword in ['public class', 'private ', 'public static void main', 'System.out']):
                return ProgrammingLanguage.JAVA
            
            # C++ indicators
            if any(keyword in code for keyword in ['#include', 'std::', 'cout', 'cin', 'namespace']):
                return ProgrammingLanguage.CPP
            
            # C# indicators
            if any(keyword in code for keyword in ['using System', 'namespace ', 'Console.WriteLine', 'public class']):
                return ProgrammingLanguage.CSHARP
            
            # Go indicators
            if any(keyword in code for keyword in ['package ', 'func ', 'import (', 'fmt.Print']):
                return ProgrammingLanguage.GO
            
            # Rust indicators
            if any(keyword in code for keyword in ['fn ', 'let mut', 'println!', 'use std::']):
                return ProgrammingLanguage.RUST
            
            # HTML indicators
            if any(keyword in code for keyword in ['<html', '<div', '<body', '<!DOCTYPE']):
                return ProgrammingLanguage.HTML
            
            # CSS indicators
            if any(keyword in code for keyword in ['{', '}', ':', ';']) and any(prop in code for prop in ['color:', 'background:', 'margin:', 'padding:']):
                return ProgrammingLanguage.CSS
            
            # SQL indicators
            if any(keyword in code_lower for keyword in ['select ', 'from ', 'where ', 'insert into', 'update ', 'delete from']):
                return ProgrammingLanguage.SQL
            
            # Default to Python if uncertain
            return ProgrammingLanguage.PYTHON
            
        except Exception as e:
            print(f"Error detecting language: {e}")
            return ProgrammingLanguage.PYTHON
    
    def analyze_code(self, code: str, language: Optional[ProgrammingLanguage] = None) -> CodeResult:
        """Analyze code for issues, improvements, and suggestions"""
        if not language:
            language = self.detect_language(code)
        
        task = CodeTask(
            task_type=CodeTaskType.ANALYZE,
            language=language,
            code=code,
            description="Analyze this code for issues, improvements, and best practices"
        )
        
        return self._process_code_task(task)
    
    def improve_code(self, code: str, requirements: List[str] = None, language: Optional[ProgrammingLanguage] = None) -> CodeResult:
        """Improve code quality, performance, and readability"""
        if not language:
            language = self.detect_language(code)
        
        task = CodeTask(
            task_type=CodeTaskType.IMPROVE,
            language=language,
            code=code,
            description="Improve this code for better quality, performance, and readability",
            requirements=requirements or []
        )
        
        return self._process_code_task(task)
    
    def fix_bug(self, code: str, error_description: str, language: Optional[ProgrammingLanguage] = None) -> CodeResult:
        """Fix bugs in code based on error description"""
        if not language:
            language = self.detect_language(code)
        
        task = CodeTask(
            task_type=CodeTaskType.FIX_BUG,
            language=language,
            code=code,
            description=f"Fix the bug in this code: {error_description}"
        )
        
        return self._process_code_task(task)
    
    def generate_code(self, description: str, language: ProgrammingLanguage, requirements: List[str] = None) -> CodeResult:
        """Generate code based on description and requirements"""
        task = CodeTask(
            task_type=CodeTaskType.GENERATE,
            language=language,
            code="",
            description=description,
            requirements=requirements or []
        )
        
        return self._process_code_task(task)
    
    def refactor_code(self, code: str, refactor_goals: List[str], language: Optional[ProgrammingLanguage] = None) -> CodeResult:
        """Refactor code according to specified goals"""
        if not language:
            language = self.detect_language(code)
        
        task = CodeTask(
            task_type=CodeTaskType.REFACTOR,
            language=language,
            code=code,
            description="Refactor this code according to the specified goals",
            requirements=refactor_goals
        )
        
        return self._process_code_task(task)
    
    def document_code(self, code: str, language: Optional[ProgrammingLanguage] = None) -> CodeResult:
        """Generate documentation for code"""
        if not language:
            language = self.detect_language(code)
        
        task = CodeTask(
            task_type=CodeTaskType.DOCUMENT,
            language=language,
            code=code,
            description="Generate comprehensive documentation for this code"
        )
        
        return self._process_code_task(task)
    
    def explain_code(self, code: str, language: Optional[ProgrammingLanguage] = None) -> CodeResult:
        """Explain how code works"""
        if not language:
            language = self.detect_language(code)
        
        task = CodeTask(
            task_type=CodeTaskType.EXPLAIN,
            language=language,
            code=code,
            description="Explain how this code works in detail"
        )
        
        return self._process_code_task(task)
    
    def _process_code_task(self, task: CodeTask) -> CodeResult:
        """Process a coding task using DeepSeek-Coder V2"""
        start_time = time.time()
        self.total_requests += 1
        
        try:
            # Build prompt for DeepSeek-Coder V2
            prompt = self._build_prompt(task)
            
            # Make API request
            response = self._make_api_request(prompt)
            
            if response:
                # Parse response
                result = self._parse_response(response, task)
                result.processing_time = time.time() - start_time
                
                if result.success:
                    self.successful_requests += 1
                
                # Store in session history
                self.session_history.append({
                    "task": task,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
                
                return result
            else:
                return CodeResult(
                    success=False,
                    improved_code=None,
                    explanation="Failed to get response from DeepSeek-Coder V2",
                    suggestions=[],
                    confidence=0.0,
                    processing_time=time.time() - start_time,
                    error_message="API request failed"
                )
                
        except Exception as e:
            return CodeResult(
                success=False,
                improved_code=None,
                explanation=f"Error processing code task: {str(e)}",
                suggestions=[],
                confidence=0.0,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def _build_prompt(self, task: CodeTask) -> str:
        """Build prompt for DeepSeek-Coder V2"""
        prompt_parts = [
            f"You are DeepSeek-Coder V2, an expert programming assistant.",
            f"Task: {task.task_type.value.upper()}",
            f"Language: {task.language.value}",
            f"Description: {task.description}",
            ""
        ]
        
        if task.code:
            prompt_parts.extend([
                "Code to analyze/improve:",
                "```" + task.language.value,
                task.code,
                "```",
                ""
            ])
        
        if task.requirements:
            prompt_parts.extend([
                "Requirements:",
                *[f"- {req}" for req in task.requirements],
                ""
            ])
        
        if task.context:
            prompt_parts.extend([
                "Context:",
                task.context,
                ""
            ])
        
        # Add task-specific instructions
        if task.task_type == CodeTaskType.IMPROVE:
            prompt_parts.append("Please provide improved code with explanations of changes made.")
        elif task.task_type == CodeTaskType.FIX_BUG:
            prompt_parts.append("Please identify and fix the bug, explaining what was wrong and how you fixed it.")
        elif task.task_type == CodeTaskType.ANALYZE:
            prompt_parts.append("Please analyze the code for issues, improvements, and best practices.")
        elif task.task_type == CodeTaskType.GENERATE:
            prompt_parts.append("Please generate clean, well-documented code that meets the requirements.")
        elif task.task_type == CodeTaskType.REFACTOR:
            prompt_parts.append("Please refactor the code according to the goals while maintaining functionality.")
        elif task.task_type == CodeTaskType.DOCUMENT:
            prompt_parts.append("Please add comprehensive documentation including docstrings and comments.")
        elif task.task_type == CodeTaskType.EXPLAIN:
            prompt_parts.append("Please explain the code step by step in clear, educational language.")
        
        prompt_parts.extend([
            "",
            "Please provide:",
            "1. Improved/generated code (if applicable)",
            "2. Detailed explanation of changes/analysis",
            "3. Suggestions for further improvements",
            "4. Confidence level (0-100%)"
        ])
        
        return "\n".join(prompt_parts)
    
    def _make_api_request(self, prompt: str) -> Optional[str]:
        """Make API request to DeepSeek-Coder V2"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Low temperature for consistent code generation
                    "top_p": 0.9,
                    "max_tokens": 4000
                }
            }
            
            response = requests.post(
                f"{self.api_endpoint}/api/generate",
                json=payload,
                timeout=300  # 5 minute timeout for complex code tasks (increased for training)
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "")
            else:
                print(f"❌ DeepSeek-Coder API error: {response.status_code}")
                print(f"📄 Response text: {response.text}")
                print(f"🔗 Request URL: {response.url}")
                return None
                
        except requests.exceptions.ConnectionError as e:
            print(f"❌ DeepSeek-Coder connection error: Cannot connect to {self.api_endpoint}")
            print(f"💡 Make sure Ollama is running and DeepSeek-Coder model is available")
            return None
        except requests.exceptions.Timeout as e:
            print(f"⏰ DeepSeek-Coder timeout error: Request took too long")
            return None
        except Exception as e:
            print(f"❌ DeepSeek-Coder API error: {e}")
            print(f"🔧 Error type: {type(e).__name__}")
            return None
    
    def _parse_response(self, response: str, task: CodeTask) -> CodeResult:
        """Parse response from DeepSeek-Coder V2"""
        try:
            # Extract code blocks
            code_blocks = re.findall(r'```(?:\w+)?\n(.*?)\n```', response, re.DOTALL)
            improved_code = code_blocks[0] if code_blocks else None
            
            # Extract confidence if mentioned
            confidence_match = re.search(r'confidence[:\s]*(\d+)%?', response.lower())
            confidence = float(confidence_match.group(1)) / 100 if confidence_match else 0.8
            
            # Extract suggestions (lines starting with bullet points or numbers)
            suggestion_lines = re.findall(r'(?:^|\n)(?:[•\-\*]|\d+\.)\s*(.+)', response)
            suggestions = [s.strip() for s in suggestion_lines if len(s.strip()) > 10][:5]  # Limit to 5 suggestions
            
            return CodeResult(
                success=True,
                improved_code=improved_code,
                explanation=response,
                suggestions=suggestions,
                confidence=confidence,
                processing_time=0.0  # Will be set by caller
            )
            
        except Exception as e:
            return CodeResult(
                success=False,
                improved_code=None,
                explanation=response,
                suggestions=[],
                confidence=0.5,
                processing_time=0.0,  # Will be set by caller
                error_message=f"Error parsing response: {str(e)}"
            )
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get service status and statistics"""
        success_rate = (self.successful_requests / self.total_requests * 100) if self.total_requests > 0 else 0
        
        return {
            "service_name": "DeepSeek-Coder V2",
            "model": self.model_name,
            "endpoint": self.api_endpoint,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "success_rate": f"{success_rate:.1f}%",
            "session_history_count": len(self.session_history),
            "supported_languages": [lang.value for lang in ProgrammingLanguage],
            "supported_tasks": [task.value for task in CodeTaskType]
        }
    
    def get_recent_history(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent coding task history"""
        recent = self.session_history[-limit:] if self.session_history else []
        
        return [{
            "task_type": item["task"].task_type.value,
            "language": item["task"].language.value,
            "success": item["result"].success,
            "confidence": item["result"].confidence,
            "processing_time": f"{item['result'].processing_time:.2f}s",
            "timestamp": item["timestamp"]
        } for item in recent]
