"""
Plugin Manager for Jarvis V6
Handles loading and managing plugins for extensibility
"""

import os
import importlib
import inspect
from typing import Dict, List, Any, Optional, Type
from abc import ABC, abstractmethod
from PyQt6.QtCore import QObject, pyqtSignal
from src.core.config import Config

class PluginInterface(ABC):
    """Base interface for all plugins"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Plugin name"""
        pass
        
    @property
    @abstractmethod
    def version(self) -> str:
        """Plugin version"""
        pass
        
    @property
    @abstractmethod
    def description(self) -> str:
        """Plugin description"""
        pass
        
    @abstractmethod
    def initialize(self, config: Config) -> bool:
        """Initialize the plugin"""
        pass
        
    @abstractmethod
    def cleanup(self) -> None:
        """Cleanup plugin resources"""
        pass

class VoicePlugin(PluginInterface):
    """Interface for voice input/output plugins"""
    
    @abstractmethod
    def start_listening(self) -> None:
        """Start voice input"""
        pass
        
    @abstractmethod
    def stop_listening(self) -> None:
        """Stop voice input"""
        pass
        
    @abstractmethod
    def speak(self, text: str) -> None:
        """Speak text output"""
        pass

class MemoryPlugin(PluginInterface):
    """Interface for memory/persistence plugins"""
    
    @abstractmethod
    def save_conversation(self, conversation: List[Dict[str, Any]]) -> bool:
        """Save conversation history"""
        pass
        
    @abstractmethod
    def load_conversation(self) -> List[Dict[str, Any]]:
        """Load conversation history"""
        pass
        
    @abstractmethod
    def save_user_preferences(self, preferences: Dict[str, Any]) -> bool:
        """Save user preferences"""
        pass
        
    @abstractmethod
    def load_user_preferences(self) -> Dict[str, Any]:
        """Load user preferences"""
        pass

class HUDPlugin(PluginInterface):
    """Interface for HUD overlay plugins"""
    
    @abstractmethod
    def create_widget(self) -> QObject:
        """Create the HUD widget"""
        pass
        
    @abstractmethod
    def update_data(self, data: Dict[str, Any]) -> None:
        """Update HUD data"""
        pass

class PersonalityPlugin(PluginInterface):
    """Interface for personality mode plugins"""
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """Get system prompt for this personality"""
        pass
        
    @abstractmethod
    def process_response(self, response: str) -> str:
        """Process AI response for personality"""
        pass

class PluginManager(QObject):
    """Manages plugin loading and lifecycle"""
    
    plugin_loaded = pyqtSignal(str)  # plugin name
    plugin_unloaded = pyqtSignal(str)  # plugin name
    plugin_error = pyqtSignal(str, str)  # plugin name, error message
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        self.plugins: Dict[str, PluginInterface] = {}
        self.plugin_types: Dict[Type, List[PluginInterface]] = {
            VoicePlugin: [],
            MemoryPlugin: [],
            HUDPlugin: [],
            PersonalityPlugin: []
        }
        
    def load_plugins_from_directory(self, directory: str) -> None:
        """Load all plugins from a directory"""
        if not os.path.exists(directory):
            return
            
        for filename in os.listdir(directory):
            if filename.endswith('.py') and not filename.startswith('__'):
                plugin_name = filename[:-3]  # Remove .py extension
                self.load_plugin(directory, plugin_name)
                
    def load_plugin(self, directory: str, plugin_name: str) -> bool:
        """Load a specific plugin"""
        try:
            # Import the plugin module
            spec = importlib.util.spec_from_file_location(
                plugin_name, 
                os.path.join(directory, f"{plugin_name}.py")
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Find plugin classes
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (issubclass(obj, PluginInterface) and 
                    obj != PluginInterface and
                    not inspect.isabstract(obj)):
                    
                    # Instantiate plugin
                    plugin_instance = obj()
                    
                    # Initialize plugin
                    if plugin_instance.initialize(self.config):
                        self.plugins[plugin_instance.name] = plugin_instance
                        
                        # Categorize plugin
                        for plugin_type in self.plugin_types:
                            if isinstance(plugin_instance, plugin_type):
                                self.plugin_types[plugin_type].append(plugin_instance)
                                
                        self.plugin_loaded.emit(plugin_instance.name)
                        return True
                    else:
                        self.plugin_error.emit(plugin_name, "Failed to initialize")
                        return False
                        
        except Exception as e:
            self.plugin_error.emit(plugin_name, str(e))
            return False
            
        return False
        
    def unload_plugin(self, plugin_name: str) -> bool:
        """Unload a specific plugin"""
        if plugin_name not in self.plugins:
            return False
            
        try:
            plugin = self.plugins[plugin_name]
            
            # Remove from type categories
            for plugin_type in self.plugin_types:
                if plugin in self.plugin_types[plugin_type]:
                    self.plugin_types[plugin_type].remove(plugin)
                    
            # Cleanup plugin
            plugin.cleanup()
            
            # Remove from plugins dict
            del self.plugins[plugin_name]
            
            self.plugin_unloaded.emit(plugin_name)
            return True
            
        except Exception as e:
            self.plugin_error.emit(plugin_name, str(e))
            return False
            
    def get_plugins_by_type(self, plugin_type: Type) -> List[PluginInterface]:
        """Get all plugins of a specific type"""
        return self.plugin_types.get(plugin_type, [])
        
    def get_plugin(self, plugin_name: str) -> Optional[PluginInterface]:
        """Get a specific plugin by name"""
        return self.plugins.get(plugin_name)
        
    def list_plugins(self) -> List[str]:
        """List all loaded plugin names"""
        return list(self.plugins.keys())
        
    def cleanup_all_plugins(self) -> None:
        """Cleanup all loaded plugins"""
        for plugin_name in list(self.plugins.keys()):
            self.unload_plugin(plugin_name)
            
    def reload_plugin(self, plugin_name: str, directory: str) -> bool:
        """Reload a specific plugin"""
        if plugin_name in self.plugins:
            self.unload_plugin(plugin_name)
            
        return self.load_plugin(directory, plugin_name)
        
    def get_plugin_info(self, plugin_name: str) -> Optional[Dict[str, str]]:
        """Get information about a plugin"""
        plugin = self.get_plugin(plugin_name)
        if plugin:
            return {
                'name': plugin.name,
                'version': plugin.version,
                'description': plugin.description,
                'type': type(plugin).__name__
            }
        return None
