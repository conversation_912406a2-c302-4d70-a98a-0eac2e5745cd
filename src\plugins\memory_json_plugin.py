"""
JSON Memory Plugin for Jarvis V6
Provides persistent memory using JSON files
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any
from src.plugins.plugin_manager import MemoryPlugin
from src.core.config import Config

class JSONMemoryPlugin(MemoryPlugin):
    """Memory plugin that uses JSON files for persistence"""
    
    def __init__(self):
        self.config = None
        self.data_dir = "data"
        self.conversations_file = "conversations.json"
        self.preferences_file = "preferences.json"
        
    @property
    def name(self) -> str:
        return "JSON Memory"
        
    @property
    def version(self) -> str:
        return "1.0.0"
        
    @property
    def description(self) -> str:
        return "Provides persistent memory using JSON files"
        
    def initialize(self, config: Config) -> bool:
        """Initialize the plugin"""
        try:
            self.config = config
            
            # Create data directory if it doesn't exist
            if not os.path.exists(self.data_dir):
                os.makedirs(self.data_dir)
                
            return True
        except Exception as e:
            print(f"Failed to initialize JSON Memory Plugin: {e}")
            return False
            
    def cleanup(self) -> None:
        """Cleanup plugin resources"""
        # Nothing to cleanup for JSON files
        pass
        
    def save_conversation(self, conversation: List[Dict[str, Any]]) -> bool:
        """Save conversation history"""
        try:
            filepath = os.path.join(self.data_dir, self.conversations_file)
            
            # Load existing conversations
            conversations = []
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    conversations = json.load(f)
                    
            # Add new conversation with timestamp
            conversation_entry = {
                'timestamp': datetime.now().isoformat(),
                'messages': conversation
            }
            conversations.append(conversation_entry)
            
            # Keep only last 100 conversations
            if len(conversations) > 100:
                conversations = conversations[-100:]
                
            # Save back to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, indent=2, ensure_ascii=False)
                
            return True
        except Exception as e:
            print(f"Failed to save conversation: {e}")
            return False
            
    def load_conversation(self) -> List[Dict[str, Any]]:
        """Load conversation history"""
        try:
            filepath = os.path.join(self.data_dir, self.conversations_file)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    conversations = json.load(f)
                    
                # Return the most recent conversation
                if conversations:
                    return conversations[-1].get('messages', [])
                    
        except Exception as e:
            print(f"Failed to load conversation: {e}")
            
        return []
        
    def save_user_preferences(self, preferences: Dict[str, Any]) -> bool:
        """Save user preferences"""
        try:
            filepath = os.path.join(self.data_dir, self.preferences_file)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(preferences, f, indent=2, ensure_ascii=False)
                
            return True
        except Exception as e:
            print(f"Failed to save preferences: {e}")
            return False
            
    def load_user_preferences(self) -> Dict[str, Any]:
        """Load user preferences"""
        try:
            filepath = os.path.join(self.data_dir, self.preferences_file)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
                    
        except Exception as e:
            print(f"Failed to load preferences: {e}")
            
        return {}
        
    def get_conversation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history"""
        try:
            filepath = os.path.join(self.data_dir, self.conversations_file)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    conversations = json.load(f)
                    
                # Return the most recent conversations
                return conversations[-limit:] if conversations else []
                
        except Exception as e:
            print(f"Failed to get conversation history: {e}")
            
        return []
        
    def search_conversations(self, query: str) -> List[Dict[str, Any]]:
        """Search through conversation history"""
        try:
            filepath = os.path.join(self.data_dir, self.conversations_file)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    conversations = json.load(f)
                    
                # Search for query in messages
                matching_conversations = []
                for conv in conversations:
                    for message in conv.get('messages', []):
                        if query.lower() in message.get('content', '').lower():
                            matching_conversations.append(conv)
                            break
                            
                return matching_conversations
                
        except Exception as e:
            print(f"Failed to search conversations: {e}")
            
        return []
