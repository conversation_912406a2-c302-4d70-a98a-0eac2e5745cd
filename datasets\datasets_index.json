{"notes.txt": {"path": "datasets\\notes.txt", "format": ".txt", "hash": "691ea38621b37d764f61050c849e09b7", "processed_at": "2025-07-17T13:45:24.131420", "size": 1346, "records_count": 1, "data_preview": "{'type': 'text_lines', 'content': ['JARVIS AI Assistant - Development Notes', '=====================================', '', 'Project Goals:', '- Create an advanced AI assistant with autonomous capabili..."}, "products.json": {"path": "datasets\\products.json", "format": ".json", "hash": "1102806ab4603d91ae042e623c41cb1a", "processed_at": "2025-07-17T13:45:24.144986", "size": 1190, "records_count": 1, "data_preview": "{'products': [{'id': 1, 'name': 'Wireless Headphones', 'category': 'Electronics', 'price': 199.99, 'stock': 45, 'description': 'High-quality wireless headphones with noise cancellation'}, {'id': 2, 'n..."}, "sample_customers.csv": {"path": "datasets\\sample_customers.csv", "format": ".csv", "hash": "5071b585ce9f26002d11f9b5e8b9e660", "processed_at": "2025-07-17T13:45:24.170577", "size": 599, "records_count": 10, "data_preview": "[{'id': '1', 'name': '<PERSON>', 'email': '<EMAIL>', 'age': '28', 'city': 'New York', 'purchase_amount': '1250.50'}, {'id': '2', 'name': '<PERSON>', 'email': '<EMAIL>', '..."}, "datasets_index.json": {"path": "datasets\\datasets_index.json", "format": ".json", "hash": "0d6c3ea5f24a049032cbf01a2f847007", "processed_at": "2025-07-17T13:45:50.294368", "size": 1873, "records_count": 1, "data_preview": "{'notes.txt': {'path': 'datasets\\\\notes.txt', 'format': '.txt', 'hash': '691ea38621b37d764f61050c849e09b7', 'processed_at': '2025-07-17T13:45:24.131420', 'size': 1346, 'records_count': 1, 'data_previe..."}, "DailyDialog.csv": {"path": "datasets\\DailyDialog.csv", "format": ".csv", "hash": "1d0f0ffda054903b3687e9271d1f08ee", "processed_at": "2025-07-17T13:56:16.122262", "size": 561656, "records_count": 1000, "data_preview": "[{'dialog': \"['Hey man , you wanna buy some weed ? ' ' Some what ? '\\n ' Weed ! You know ? <PERSON><PERSON> , <PERSON><PERSON><PERSON> , <PERSON> some chronic ! '\\n ' Oh , umm , no thanks . '\\n ' I also have blow if you prefer to d..."}}