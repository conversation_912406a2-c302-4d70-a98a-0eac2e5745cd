"""
Panel Customization System for JARVIS V6
========================================
Allows users to move and resize panels with hotkeys

Features:
- Ctrl+M: Enable/disable panel customization mode
- Ctrl+S: Save current panel positions
- Drag panels to move them
- Resize panels by dragging corners
- Auto-load saved positions on startup
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from src.gui.qt_compat import (QWidget, QFrame, QLabel, QVBoxLayout, QHBoxLayout,
                               QShortcut, QKeySequence, Qt, QCursor, QPoint, QSize,
                               QApplication, QMessageBox, QTimer, QObject, QEvent)

class PanelCustomizer(QObject):
    """System for customizing panel positions and sizes"""
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.customization_mode = False
        self.settings_file = "data/panel_settings.json"
        self.dragging_panel = None
        self.drag_start_pos = None
        self.resizing_panel = None
        self.resize_start_pos = None
        self.resize_start_size = None
        
        # Customizable panels
        self.customizable_panels = {}
        
        # Create data directory
        os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
        
        # Setup hotkeys
        self.setup_hotkeys()
        
        # Load saved settings
        self.load_panel_settings()
        
        print("🎯 Panel Customization System initialized")
        print("📋 Hotkeys: Ctrl+M (customize mode), Ctrl+S (save positions)")
    
    def setup_hotkeys(self):
        """Setup keyboard shortcuts for panel customization"""
        # Ctrl+M: Toggle customization mode
        self.customize_shortcut = QShortcut(QKeySequence("Ctrl+M"), self.main_window)
        self.customize_shortcut.activated.connect(self.toggle_customization_mode)
        
        # Ctrl+S: Save panel positions
        self.save_shortcut = QShortcut(QKeySequence("Ctrl+S"), self.main_window)
        self.save_shortcut.activated.connect(self.save_panel_settings)
        
        # Ctrl+R: Reset to default positions
        self.reset_shortcut = QShortcut(QKeySequence("Ctrl+R"), self.main_window)
        self.reset_shortcut.activated.connect(self.reset_panel_positions)
    
    def register_panel(self, panel: QWidget, name: str, movable: bool = True, resizable: bool = True):
        """Register a panel for customization"""
        try:
            self.customizable_panels[name] = {
                'widget': panel,
                'movable': movable,
                'resizable': resizable,
                'original_pos': panel.pos(),
                'original_size': panel.size(),
                'original_cursor': panel.cursor()
            }

            # Install event filter for mouse events
            panel.installEventFilter(self)

            print(f"📋 Registered panel '{name}' for customization")

        except Exception as e:
            print(f"❌ Error registering panel '{name}': {e}")
    
    def toggle_customization_mode(self):
        """Toggle panel customization mode"""
        self.customization_mode = not self.customization_mode
        
        if self.customization_mode:
            self.enable_customization_mode()
        else:
            self.disable_customization_mode()
    
    def enable_customization_mode(self):
        """Enable panel customization mode"""
        print("🎯 Panel customization mode ENABLED")
        
        # Show customization overlay
        self.show_customization_overlay()
        
        # Change cursor for customizable panels
        for name, panel_info in self.customizable_panels.items():
            panel = panel_info['widget']
            if panel_info['movable']:
                panel.setCursor(Qt.CursorShape.SizeAllCursor)
        
        # Show notification
        self.show_notification("Panel Customization Mode ENABLED", 
                             "• Drag panels to move them\n• Drag corners to resize\n• Ctrl+S to save positions\n• Ctrl+M to exit mode")
    
    def disable_customization_mode(self):
        """Disable panel customization mode"""
        print("🎯 Panel customization mode DISABLED")
        
        # Hide customization overlay
        self.hide_customization_overlay()
        
        # Restore original cursors
        for name, panel_info in self.customizable_panels.items():
            panel = panel_info['widget']
            panel.setCursor(panel_info['original_cursor'])
        
        # Show notification
        self.show_notification("Panel Customization Mode DISABLED", "Panel positions locked")
    
    def show_customization_overlay(self):
        """Show visual overlay indicating customization mode"""
        # Create overlay widget
        self.overlay = QFrame(self.main_window)
        self.overlay.setObjectName("customizationOverlay")
        self.overlay.setGeometry(self.main_window.rect())
        self.overlay.setStyleSheet("""
            QFrame#customizationOverlay {
                background-color: rgba(0, 255, 255, 0.1);
                border: 2px dashed #00FFFF;
            }
        """)
        
        # Add instruction label
        overlay_layout = QVBoxLayout(self.overlay)
        instruction_label = QLabel("🎯 PANEL CUSTOMIZATION MODE")
        instruction_label.setStyleSheet("""
            QLabel {
                color: #00FFFF;
                font-size: 24px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                background-color: rgba(0, 0, 0, 0.8);
                padding: 20px;
                border-radius: 10px;
                border: 2px solid #00FFFF;
            }
        """)
        instruction_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        overlay_layout.addWidget(instruction_label)
        
        self.overlay.show()
        self.overlay.raise_()
        
        # Auto-hide overlay after 3 seconds
        QTimer.singleShot(3000, self.hide_overlay_label)
    
    def hide_overlay_label(self):
        """Hide the instruction label but keep the overlay"""
        if hasattr(self, 'overlay'):
            layout = self.overlay.layout()
            if layout and layout.count() > 0:
                item = layout.takeAt(0)
                if item and item.widget():
                    item.widget().deleteLater()
    
    def hide_customization_overlay(self):
        """Hide customization overlay"""
        if hasattr(self, 'overlay'):
            self.overlay.hide()
            self.overlay.deleteLater()
            delattr(self, 'overlay')
    
    def show_notification(self, title: str, message: str):
        """Show notification message"""
        # Create notification widget
        notification = QFrame(self.main_window)
        notification.setObjectName("notification")
        notification.setFixedSize(400, 120)
        notification.move(self.main_window.width() - 420, 20)
        
        notification.setStyleSheet("""
            QFrame#notification {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 50, 100, 0.95),
                    stop:1 rgba(0, 30, 60, 0.9));
                border: 2px solid #00FFFF;
                border-radius: 10px;
            }
            QLabel {
                color: #FFFFFF;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        
        layout = QVBoxLayout(notification)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #00FFFF;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        message_label = QLabel(message)
        message_label.setStyleSheet("font-size: 10px;")
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(message_label)
        
        notification.show()
        notification.raise_()
        
        # Auto-hide after 4 seconds
        QTimer.singleShot(4000, notification.deleteLater)
    
    def save_panel_settings(self):
        """Save current panel positions and sizes"""
        settings = {
            'timestamp': datetime.now().isoformat(),
            'panels': {}
        }
        
        for name, panel_info in self.customizable_panels.items():
            panel = panel_info['widget']
            settings['panels'][name] = {
                'x': panel.x(),
                'y': panel.y(),
                'width': panel.width(),
                'height': panel.height()
            }
        
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(settings, f, indent=2)
            
            print(f"💾 Panel settings saved to {self.settings_file}")
            self.show_notification("Panel Settings Saved!", 
                                 f"Positions saved for {len(settings['panels'])} panels")
            
        except Exception as e:
            print(f"❌ Error saving panel settings: {e}")
            self.show_notification("Save Error", f"Failed to save: {str(e)}")
    
    def load_panel_settings(self):
        """Load saved panel positions and sizes"""
        if not os.path.exists(self.settings_file):
            print("📋 No saved panel settings found")
            return
        
        try:
            with open(self.settings_file, 'r') as f:
                settings = json.load(f)
            
            panels_loaded = 0
            for name, panel_data in settings.get('panels', {}).items():
                if name in self.customizable_panels:
                    panel = self.customizable_panels[name]['widget']
                    panel.move(panel_data['x'], panel_data['y'])
                    panel.resize(panel_data['width'], panel_data['height'])
                    panels_loaded += 1
            
            print(f"📋 Loaded panel settings for {panels_loaded} panels")
            
        except Exception as e:
            print(f"❌ Error loading panel settings: {e}")
    
    def reset_panel_positions(self):
        """Reset all panels to their original positions"""
        for name, panel_info in self.customizable_panels.items():
            panel = panel_info['widget']
            panel.move(panel_info['original_pos'])
            panel.resize(panel_info['original_size'])
        
        print("🔄 Panel positions reset to defaults")
        self.show_notification("Panels Reset", "All panels restored to default positions")
    
    def eventFilter(self, obj, event):
        """Handle mouse events for panel customization"""
        try:
            if not self.customization_mode:
                return False

            # Find which panel this event belongs to
            panel_name = None
            panel_info = None
            for name, info in self.customizable_panels.items():
                if info['widget'] == obj:
                    panel_name = name
                    panel_info = info
                    break

            if not panel_info:
                return False

            if hasattr(event, 'type'):
                event_type = event.type()

                # Check for mouse button press
                if event_type == QEvent.Type.MouseButtonPress:
                    if hasattr(event, 'button') and event.button() == Qt.MouseButton.LeftButton:
                        self.start_drag_or_resize(obj, event.pos(), panel_info)
                        return True

                # Check for mouse move
                elif event_type == QEvent.Type.MouseMove:
                    if self.dragging_panel == obj:
                        self.handle_panel_drag(obj, event.globalPos())
                        return True
                    elif self.resizing_panel == obj:
                        self.handle_panel_resize(obj, event.globalPos())
                        return True

                # Check for mouse button release
                elif event_type == QEvent.Type.MouseButtonRelease:
                    if hasattr(event, 'button') and event.button() == Qt.MouseButton.LeftButton:
                        self.end_drag_or_resize()
                        return True

            return False

        except Exception as e:
            print(f"❌ Event filter error: {e}")
            return False
    
    def start_drag_or_resize(self, panel, local_pos, panel_info):
        """Start dragging or resizing a panel"""
        # Check if clicking near edges for resize
        panel_rect = panel.rect()
        edge_margin = 10
        
        near_right = local_pos.x() > panel_rect.width() - edge_margin
        near_bottom = local_pos.y() > panel_rect.height() - edge_margin
        
        if (near_right or near_bottom) and panel_info['resizable']:
            # Start resize
            self.resizing_panel = panel
            self.resize_start_pos = QCursor.pos()
            self.resize_start_size = panel.size()
            panel.setCursor(Qt.CursorShape.SizeFDiagCursor)
        elif panel_info['movable']:
            # Start drag
            self.dragging_panel = panel
            self.drag_start_pos = QCursor.pos() - panel.pos()
            panel.setCursor(Qt.CursorShape.ClosedHandCursor)
    
    def handle_panel_drag(self, panel, global_pos):
        """Handle panel dragging"""
        if self.dragging_panel and self.drag_start_pos:
            new_pos = global_pos - self.drag_start_pos
            panel.move(new_pos)
    
    def handle_panel_resize(self, panel, global_pos):
        """Handle panel resizing"""
        if self.resizing_panel and self.resize_start_pos and self.resize_start_size:
            delta = global_pos - self.resize_start_pos
            new_width = max(100, self.resize_start_size.width() + delta.x())
            new_height = max(50, self.resize_start_size.height() + delta.y())
            panel.resize(new_width, new_height)
    
    def end_drag_or_resize(self):
        """End dragging or resizing"""
        if self.dragging_panel:
            self.dragging_panel.setCursor(Qt.CursorShape.SizeAllCursor)
            self.dragging_panel = None
            self.drag_start_pos = None
        
        if self.resizing_panel:
            self.resizing_panel.setCursor(Qt.CursorShape.SizeAllCursor)
            self.resizing_panel = None
            self.resize_start_pos = None
            self.resize_start_size = None
