"""
Voice Input System for JARVIS V6
Provides speech-to-text capabilities for natural voice interaction

Features:
- Real-time speech recognition
- Wake word detection ("Hey JARVIS")
- Noise filtering and audio processing
- Multi-language support
- Voice command parsing
- Integration with input enhancement system
"""

import threading
import time
import queue
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

# Try to import speech recognition libraries
try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False
    print("⚠️ Speech recognition not available. Install with: pip install SpeechRecognition pyaudio")

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("⚠️ PyAudio not available. Install with: pip install pyaudio")

class VoiceInputState(Enum):
    """Voice input system states"""
    INACTIVE = "inactive"
    LISTENING = "listening"
    PROCESSING = "processing"
    ERROR = "error"

@dataclass
class VoiceCommand:
    """Represents a voice command"""
    text: str
    confidence: float
    timestamp: datetime
    language: str
    wake_word_detected: bool

class VoiceInputSystem:
    """Advanced voice input system for JARVIS"""
    
    def __init__(self, config=None):
        self.config = config
        self.state = VoiceInputState.INACTIVE
        self.is_listening = False
        self.voice_queue = queue.Queue()
        
        # Voice recognition settings
        self.wake_words = ["hey jarvis", "jarvis", "hey j.a.r.v.i.s"]
        self.language = "en-US"
        self.energy_threshold = 4000
        self.pause_threshold = 0.8
        self.timeout = 5
        
        # Callbacks
        self.on_voice_command = None
        self.on_wake_word = None
        self.on_state_change = None
        
        # Initialize if libraries are available
        if SPEECH_RECOGNITION_AVAILABLE and PYAUDIO_AVAILABLE:
            self._initialize_speech_recognition()
        else:
            print("❌ Voice input system disabled - missing dependencies")
    
    def _initialize_speech_recognition(self):
        """Initialize speech recognition components"""
        try:
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            print("🎤 Calibrating microphone for ambient noise...")
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
            
            # Set recognition parameters
            self.recognizer.energy_threshold = self.energy_threshold
            self.recognizer.pause_threshold = self.pause_threshold
            self.recognizer.timeout = self.timeout
            
            print("✅ Voice input system initialized")
            print(f"🎤 Energy threshold: {self.recognizer.energy_threshold}")
            print(f"⏱️ Pause threshold: {self.recognizer.pause_threshold}s")
            
        except Exception as e:
            print(f"❌ Error initializing voice input: {e}")
            self.state = VoiceInputState.ERROR
    
    def start_listening(self):
        """Start continuous voice listening"""
        if not SPEECH_RECOGNITION_AVAILABLE or not PYAUDIO_AVAILABLE:
            print("❌ Cannot start voice input - missing dependencies")
            return False
        
        if self.is_listening:
            print("⚠️ Voice input already active")
            return True
        
        try:
            self.is_listening = True
            self.state = VoiceInputState.LISTENING
            self._notify_state_change()
            
            # Start listening thread
            self.listen_thread = threading.Thread(target=self._listen_continuously, daemon=True)
            self.listen_thread.start()
            
            print("🎤 Voice input started - listening for wake words and commands")
            return True
            
        except Exception as e:
            print(f"❌ Error starting voice input: {e}")
            self.state = VoiceInputState.ERROR
            self._notify_state_change()
            return False
    
    def stop_listening(self):
        """Stop voice listening"""
        if not self.is_listening:
            return
        
        self.is_listening = False
        self.state = VoiceInputState.INACTIVE
        self._notify_state_change()
        
        print("🔇 Voice input stopped")
    
    def _listen_continuously(self):
        """Continuous listening loop"""
        while self.is_listening:
            try:
                # Listen for audio
                with self.microphone as source:
                    print("🎤 Listening...")
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=5)
                
                # Process audio in background
                threading.Thread(target=self._process_audio, args=(audio,), daemon=True).start()
                
            except sr.WaitTimeoutError:
                # Normal timeout, continue listening
                pass
            except Exception as e:
                print(f"❌ Error in voice listening: {e}")
                time.sleep(1)  # Brief pause before retrying
    
    def _process_audio(self, audio):
        """Process captured audio"""
        try:
            self.state = VoiceInputState.PROCESSING
            self._notify_state_change()
            
            # Recognize speech
            text = self.recognizer.recognize_google(audio, language=self.language)
            confidence = 0.8  # Google doesn't provide confidence, estimate
            
            print(f"🎤 Recognized: '{text}' (confidence: {confidence:.1%})")
            
            # Check for wake words
            text_lower = text.lower()
            wake_word_detected = any(wake_word in text_lower for wake_word in self.wake_words)
            
            # Create voice command
            command = VoiceCommand(
                text=text,
                confidence=confidence,
                timestamp=datetime.now(),
                language=self.language,
                wake_word_detected=wake_word_detected
            )
            
            # Handle command
            if wake_word_detected:
                self._handle_wake_word(command)
            else:
                self._handle_voice_command(command)
            
            self.state = VoiceInputState.LISTENING
            self._notify_state_change()
            
        except sr.UnknownValueError:
            # Speech not understood
            print("🎤 Could not understand audio")
            self.state = VoiceInputState.LISTENING
            self._notify_state_change()
            
        except sr.RequestError as e:
            print(f"❌ Speech recognition error: {e}")
            self.state = VoiceInputState.ERROR
            self._notify_state_change()
            
        except Exception as e:
            print(f"❌ Error processing audio: {e}")
            self.state = VoiceInputState.LISTENING
            self._notify_state_change()
    
    def _handle_wake_word(self, command: VoiceCommand):
        """Handle wake word detection"""
        print(f"👋 Wake word detected: '{command.text}'")
        
        # Extract command after wake word
        text_lower = command.text.lower()
        actual_command = text_lower
        
        for wake_word in self.wake_words:
            if wake_word in text_lower:
                # Remove wake word and get the actual command
                actual_command = text_lower.replace(wake_word, "").strip()
                break
        
        if actual_command:
            # Create new command without wake word
            clean_command = VoiceCommand(
                text=actual_command,
                confidence=command.confidence,
                timestamp=command.timestamp,
                language=command.language,
                wake_word_detected=True
            )
            
            # Notify callbacks
            if self.on_wake_word:
                self.on_wake_word(clean_command)
            
            if self.on_voice_command:
                self.on_voice_command(clean_command)
        else:
            print("🎤 Wake word detected but no command found")
    
    def _handle_voice_command(self, command: VoiceCommand):
        """Handle regular voice command"""
        print(f"🎤 Voice command: '{command.text}'")
        
        if self.on_voice_command:
            self.on_voice_command(command)
    
    def _notify_state_change(self):
        """Notify state change to callbacks"""
        if self.on_state_change:
            self.on_state_change(self.state)
    
    def set_callbacks(self, on_voice_command: Callable = None, 
                     on_wake_word: Callable = None, 
                     on_state_change: Callable = None):
        """Set callback functions"""
        self.on_voice_command = on_voice_command
        self.on_wake_word = on_wake_word
        self.on_state_change = on_state_change
    
    def configure_recognition(self, energy_threshold: int = None, 
                            pause_threshold: float = None,
                            timeout: int = None,
                            language: str = None):
        """Configure recognition parameters"""
        if energy_threshold:
            self.energy_threshold = energy_threshold
            if hasattr(self, 'recognizer'):
                self.recognizer.energy_threshold = energy_threshold
        
        if pause_threshold:
            self.pause_threshold = pause_threshold
            if hasattr(self, 'recognizer'):
                self.recognizer.pause_threshold = pause_threshold
        
        if timeout:
            self.timeout = timeout
            if hasattr(self, 'recognizer'):
                self.recognizer.timeout = timeout
        
        if language:
            self.language = language
        
        print(f"🔧 Voice recognition configured: threshold={self.energy_threshold}, pause={self.pause_threshold}s, timeout={self.timeout}s, lang={self.language}")
    
    def add_wake_word(self, wake_word: str):
        """Add a new wake word"""
        if wake_word.lower() not in self.wake_words:
            self.wake_words.append(wake_word.lower())
            print(f"✅ Added wake word: '{wake_word}'")
    
    def remove_wake_word(self, wake_word: str):
        """Remove a wake word"""
        if wake_word.lower() in self.wake_words:
            self.wake_words.remove(wake_word.lower())
            print(f"❌ Removed wake word: '{wake_word}'")
    
    def get_status(self) -> Dict[str, Any]:
        """Get voice input system status"""
        return {
            'available': SPEECH_RECOGNITION_AVAILABLE and PYAUDIO_AVAILABLE,
            'state': self.state.value,
            'listening': self.is_listening,
            'wake_words': self.wake_words,
            'language': self.language,
            'energy_threshold': self.energy_threshold,
            'pause_threshold': self.pause_threshold,
            'timeout': self.timeout
        }
    
    def test_microphone(self) -> bool:
        """Test microphone functionality"""
        if not SPEECH_RECOGNITION_AVAILABLE or not PYAUDIO_AVAILABLE:
            return False
        
        try:
            print("🎤 Testing microphone - say something...")
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=3, phrase_time_limit=3)
            
            text = self.recognizer.recognize_google(audio, language=self.language)
            print(f"✅ Microphone test successful: '{text}'")
            return True
            
        except Exception as e:
            print(f"❌ Microphone test failed: {e}")
            return False
