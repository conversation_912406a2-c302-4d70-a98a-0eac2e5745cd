#!/usr/bin/env python3
"""
Test script for ElevenLabs TTS functionality
"""

import sys
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from src.core.config import Config
from src.plugins.elevenlabs_tts_plugin import ElevenLabsTTSPlugin

def test_tts():
    """Test TTS functionality"""
    print("Testing ElevenLabs TTS Plugin...")
    
    # Create QApplication (required for Qt signals)
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # Load configuration
    config = Config.load_from_env()
    
    # Create TTS plugin
    tts_plugin = ElevenLabsTTSPlugin()
    
    # Test initialization
    print("Initializing TTS plugin...")
    if not tts_plugin.initialize(config):
        print("❌ Failed to initialize TTS plugin")
        return False
    
    print("✅ TTS plugin initialized successfully")
    
    # Test voice settings
    print(f"Voice settings: {tts_plugin.get_voice_settings()}")
    
    # Test speech generation
    test_messages = [
        "Hello! I am <PERSON><PERSON><PERSON><PERSON>, your AI assistant.",
        "Testing text-to-speech functionality with ElevenLabs.",
        "This is a short test message."
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\nTest {i}: Speaking: '{message}'")
        
        # Set up signal handlers
        speech_finished = False
        error_occurred = None
        
        def on_speech_finished():
            nonlocal speech_finished
            speech_finished = True
            print("✅ Speech completed")
        
        def on_speech_error(error):
            nonlocal error_occurred
            error_occurred = error
            print(f"❌ Speech error: {error}")
        
        # Connect signals
        tts_plugin.signals.speech_finished.connect(on_speech_finished)
        tts_plugin.signals.speech_error.connect(on_speech_error)
        
        # Start speech
        tts_plugin.speak(message)
        
        # Wait for completion (with timeout)
        timeout = 30  # 30 seconds timeout
        start_time = time.time()
        
        while not speech_finished and error_occurred is None:
            app.processEvents()  # Process Qt events
            time.sleep(0.1)
            
            if time.time() - start_time > timeout:
                print("⚠️ Speech timeout")
                break
        
        if error_occurred:
            print(f"❌ Test {i} failed: {error_occurred}")
            return False
        elif speech_finished:
            print(f"✅ Test {i} completed successfully")
        else:
            print(f"⚠️ Test {i} timed out")
        
        # Disconnect signals
        tts_plugin.signals.speech_finished.disconnect()
        tts_plugin.signals.speech_error.disconnect()
        
        # Wait a bit between tests
        if i < len(test_messages):
            print("Waiting 2 seconds before next test...")
            time.sleep(2)
    
    # Cleanup
    print("\nCleaning up...")
    tts_plugin.cleanup()
    
    print("✅ All TTS tests completed successfully!")
    return True

def test_api_only():
    """Test just the API connection without audio playback"""
    print("Testing ElevenLabs API connection only...")
    
    try:
        import requests
        
        api_key = "***************************************************"
        voice_id = "vJCZeLn6Scm2fXBSToLH"
        
        # Test API connection
        headers = {"xi-api-key": api_key}
        response = requests.get("https://api.elevenlabs.io/v1/voices", headers=headers, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ API connection failed: {response.status_code}")
            return False
        
        print("✅ API connection successful")
        
        # Test TTS generation (without playing audio)
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
        
        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": api_key
        }
        
        data = {
            "text": "This is a test message for API verification.",
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.75,
                "similarity_boost": 0.75,
                "style": 0.0,
                "use_speaker_boost": True
            }
        }
        
        print("Generating test audio...")
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            audio_size = len(response.content)
            print(f"✅ TTS generation successful (audio size: {audio_size} bytes)")
            return True
        else:
            print(f"❌ TTS generation failed: {response.status_code}")
            if response.text:
                print(f"Error details: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 50)
    print("ELEVENLABS TTS TEST")
    print("=" * 50)
    
    # Test API connection first
    if not test_api_only():
        print("\n❌ API test failed. Cannot proceed with full TTS test.")
        return False
    
    print("\n" + "=" * 50)
    print("FULL TTS TEST (with audio playback)")
    print("=" * 50)
    
    try:
        # Test full TTS functionality
        return test_tts()
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 All TTS tests passed!")
    else:
        print("\n❌ TTS tests failed!")
    
    sys.exit(0 if success else 1)
