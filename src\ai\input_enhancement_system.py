"""
Advanced Input Enhancement System for JARVIS V6
Provides intelligent input processing, auto-completion, context awareness, and natural language understanding

Features:
- Smart auto-completion and suggestions
- Context-aware input processing
- Natural language intent recognition
- Input validation and error correction
- Multi-modal input support (text, voice commands)
- Intelligent command parsing
- User preference learning
"""

import re
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import difflib

class InputType(Enum):
    """Types of user input"""
    QUESTION = "question"
    COMMAND = "command"
    CONVERSATION = "conversation"
    CODE_REQUEST = "code_request"
    SYSTEM_QUERY = "system_query"
    TRAINING_REQUEST = "training_request"
    SMART_HOME = "smart_home"
    MEMORY_QUERY = "memory_query"
    AGENT_TASK = "agent_task"

class IntentConfidence(Enum):
    """Confidence levels for intent recognition"""
    HIGH = "high"      # >80%
    MEDIUM = "medium"  # 50-80%
    LOW = "low"        # <50%

@dataclass
class InputSuggestion:
    """Represents an input suggestion"""
    text: str
    type: str
    confidence: float
    description: str
    category: str

@dataclass
class InputAnalysis:
    """Analysis result of user input"""
    original_text: str
    cleaned_text: str
    input_type: InputType
    intent: str
    confidence: IntentConfidence
    entities: Dict[str, Any]
    suggestions: List[str]
    corrections: List[str]
    context_score: float
    processing_time: float

class InputEnhancementSystem:
    """Advanced input enhancement and processing system"""
    
    def __init__(self, config=None):
        self.config = config
        self.user_preferences = {}
        self.command_history = []
        self.context_memory = []
        self.suggestion_cache = {}
        
        # Initialize patterns and keywords
        self._initialize_patterns()
        self._initialize_suggestions()
        
        print("🎯 Advanced Input Enhancement System initialized")
        print("✅ Smart auto-completion, intent recognition, and context awareness active")
    
    def _initialize_patterns(self):
        """Initialize regex patterns for input analysis"""
        self.patterns = {
            'question': [
                r'\b(what|how|why|when|where|who|which|can you|could you|would you|do you)\b',
                r'\?$',
                r'\b(explain|tell me|show me|help me)\b'
            ],
            'command': [
                r'\b(start|stop|run|execute|launch|open|close|turn on|turn off)\b',
                r'\b(train|learn|evolve|improve|analyze|scan|check|monitor)\b',
                r'\b(agent|delegate|coordinate|multi.?agent)\b'
            ],
            'code_request': [
                r'\b(code|program|script|function|class|method|algorithm)\b',
                r'\b(write|create|generate|build|develop|implement)\b',
                r'\b(python|javascript|java|cpp|html|css|sql)\b',
                r'\b(debug|fix|refactor|optimize|review)\b'
            ],
            'system_query': [
                r'\b(status|health|performance|metrics|stats|info)\b',
                r'\b(system|jarvis|ai|agent|memory|evolution)\b',
                r'\b(show|display|report|summary)\b'
            ],
            'smart_home': [
                r'\b(lights?|temperature|ac|air.?conditioning|thermostat)\b',
                r'\b(turn on|turn off|set|adjust|control)\b',
                r'\b(home|house|room|bedroom|living room)\b'
            ],
            'training': [
                r'\b(train|learn|study|practice|improve|enhance)\b',
                r'\b(knowledge|skill|ability|capability|understanding)\b',
                r'\b(background|autonomous|self.?improvement)\b'
            ]
        }
    
    def _initialize_suggestions(self):
        """Initialize suggestion database"""
        self.suggestions = {
            'commands': [
                InputSuggestion("health check", "system", 0.9, "Check JARVIS system health", "system"),
                InputSuggestion("agent status", "multi-agent", 0.9, "View multi-agent system status", "agents"),
                InputSuggestion("force health check", "self-healing", 0.9, "Force immediate health scan", "system"),
                InputSuggestion("train on conversations", "training", 0.9, "Start conversation training", "learning"),
                InputSuggestion("delegate to agents", "agents", 0.9, "Delegate task to agent team", "agents"),
                InputSuggestion("show memory", "memory", 0.9, "Display memory system status", "memory"),
                InputSuggestion("turn on AC", "smart-home", 0.9, "Control air conditioning", "home"),
                InputSuggestion("what can you do", "help", 0.9, "Show JARVIS capabilities", "help"),
                InputSuggestion("analyze this code", "coding", 0.9, "Request code analysis", "development"),
                InputSuggestion("self evolution status", "evolution", 0.9, "Check self-evolution system", "system")
            ],
            'questions': [
                InputSuggestion("What are your capabilities?", "help", 0.9, "Learn about JARVIS features", "help"),
                InputSuggestion("How do I train you?", "training", 0.9, "Learn about training system", "learning"),
                InputSuggestion("What agents are available?", "agents", 0.9, "Learn about multi-agent system", "agents"),
                InputSuggestion("How does self-healing work?", "system", 0.9, "Learn about self-healing", "system"),
                InputSuggestion("Can you control smart home devices?", "smart-home", 0.9, "Learn about home automation", "home"),
                InputSuggestion("What programming languages do you support?", "coding", 0.9, "Learn about coding support", "development")
            ],
            'code': [
                InputSuggestion("Write a Python function", "code-gen", 0.9, "Generate Python code", "development"),
                InputSuggestion("Review this code", "code-review", 0.9, "Request code review", "development"),
                InputSuggestion("Fix this bug", "debugging", 0.9, "Request bug fixing", "development"),
                InputSuggestion("Optimize performance", "optimization", 0.9, "Request performance optimization", "development"),
                InputSuggestion("Add error handling", "enhancement", 0.9, "Request error handling", "development")
            ]
        }
    
    def analyze_input(self, text: str, context: Dict[str, Any] = None) -> InputAnalysis:
        """Comprehensive analysis of user input"""
        start_time = time.time()
        
        # Clean and normalize input
        cleaned_text = self._clean_input(text)
        
        # Determine input type and intent
        input_type, confidence = self._classify_input_type(cleaned_text)
        intent = self._extract_intent(cleaned_text, input_type)
        
        # Extract entities and context
        entities = self._extract_entities(cleaned_text)
        context_score = self._calculate_context_score(cleaned_text, context)
        
        # Generate suggestions and corrections
        suggestions = self._generate_suggestions(cleaned_text, input_type)
        corrections = self._suggest_corrections(cleaned_text)
        
        processing_time = time.time() - start_time
        
        return InputAnalysis(
            original_text=text,
            cleaned_text=cleaned_text,
            input_type=input_type,
            intent=intent,
            confidence=confidence,
            entities=entities,
            suggestions=suggestions,
            corrections=corrections,
            context_score=context_score,
            processing_time=processing_time
        )
    
    def _clean_input(self, text: str) -> str:
        """Clean and normalize input text"""
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Fix common typos
        typo_fixes = {
            r'\bjarvis\b': 'JARVIS',
            r'\bai\b': 'AI',
            r'\btts\b': 'TTS',
            r'\bgui\b': 'GUI',
            r'\bapi\b': 'API',
            r'\bhelth\b': 'health',
            r'\bstatus\b': 'status',
            r'\bsytem\b': 'system',
            r'\bmemory\b': 'memory'
        }
        
        for pattern, replacement in typo_fixes.items():
            cleaned = re.sub(pattern, replacement, cleaned, flags=re.IGNORECASE)
        
        return cleaned
    
    def _classify_input_type(self, text: str) -> Tuple[InputType, IntentConfidence]:
        """Classify the type of input and confidence level"""
        text_lower = text.lower()
        scores = {}
        
        # Calculate scores for each input type
        for input_type, patterns in self.patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text_lower))
                score += matches
            scores[input_type] = score
        
        # Find the highest scoring type
        if not scores or max(scores.values()) == 0:
            return InputType.CONVERSATION, IntentConfidence.LOW
        
        best_type = max(scores, key=scores.get)
        best_score = scores[best_type]
        
        # Map to InputType enum
        type_mapping = {
            'question': InputType.QUESTION,
            'command': InputType.COMMAND,
            'code_request': InputType.CODE_REQUEST,
            'system_query': InputType.SYSTEM_QUERY,
            'smart_home': InputType.SMART_HOME,
            'training': InputType.TRAINING_REQUEST
        }
        
        input_type = type_mapping.get(best_type, InputType.CONVERSATION)
        
        # Determine confidence
        if best_score >= 3:
            confidence = IntentConfidence.HIGH
        elif best_score >= 2:
            confidence = IntentConfidence.MEDIUM
        else:
            confidence = IntentConfidence.LOW
        
        return input_type, confidence
    
    def _extract_intent(self, text: str, input_type: InputType) -> str:
        """Extract specific intent from the input"""
        text_lower = text.lower()
        
        # Intent patterns for different input types
        intent_patterns = {
            InputType.SYSTEM_QUERY: {
                'health_check': r'\b(health|status|check|diagnostic)\b',
                'agent_status': r'\b(agent|multi.?agent|team)\b',
                'memory_status': r'\b(memory|memories|remember)\b',
                'evolution_status': r'\b(evolution|evolve|self.?improvement)\b'
            },
            InputType.COMMAND: {
                'start_training': r'\b(train|learn|study)\b',
                'force_scan': r'\b(force|immediate|scan)\b',
                'delegate_task': r'\b(delegate|assign|coordinate)\b',
                'control_device': r'\b(turn|set|adjust|control)\b'
            },
            InputType.CODE_REQUEST: {
                'generate_code': r'\b(write|create|generate|build)\b',
                'review_code': r'\b(review|analyze|check|examine)\b',
                'fix_bug': r'\b(fix|debug|repair|solve)\b',
                'optimize_code': r'\b(optimize|improve|enhance|refactor)\b'
            }
        }
        
        if input_type in intent_patterns:
            for intent, pattern in intent_patterns[input_type].items():
                if re.search(pattern, text_lower):
                    return intent
        
        return 'general'
    
    def _extract_entities(self, text: str) -> Dict[str, Any]:
        """Extract entities from the input text"""
        entities = {}
        text_lower = text.lower()
        
        # Extract programming languages
        languages = ['python', 'javascript', 'java', 'cpp', 'c++', 'html', 'css', 'sql', 'rust', 'go']
        found_languages = [lang for lang in languages if lang in text_lower]
        if found_languages:
            entities['programming_languages'] = found_languages
        
        # Extract device types
        devices = ['ac', 'air conditioning', 'lights', 'thermostat', 'temperature']
        found_devices = [device for device in devices if device in text_lower]
        if found_devices:
            entities['devices'] = found_devices
        
        # Extract agent types
        agents = ['planner', 'coder', 'memory', 'researcher', 'speaker', 'tester', 'coordinator', 'monitor']
        found_agents = [agent for agent in agents if agent in text_lower]
        if found_agents:
            entities['agents'] = found_agents
        
        # Extract system components
        systems = ['health', 'status', 'memory', 'evolution', 'training', 'self-healing', 'multi-agent']
        found_systems = [system for system in systems if system in text_lower]
        if found_systems:
            entities['systems'] = found_systems
        
        return entities
    
    def _calculate_context_score(self, text: str, context: Dict[str, Any] = None) -> float:
        """Calculate how well the input fits the current context"""
        if not context:
            return 0.5
        
        score = 0.5  # Base score
        
        # Check if input relates to recent conversation
        if 'recent_topics' in context:
            for topic in context['recent_topics']:
                if topic.lower() in text.lower():
                    score += 0.2
        
        # Check if input relates to current system state
        if 'active_systems' in context:
            for system in context['active_systems']:
                if system.lower() in text.lower():
                    score += 0.1
        
        return min(1.0, score)
    
    def _generate_suggestions(self, text: str, input_type: InputType) -> List[str]:
        """Generate intelligent suggestions based on input"""
        suggestions = []
        text_lower = text.lower()
        
        # Get relevant suggestions based on input type
        if input_type == InputType.SYSTEM_QUERY:
            suggestions.extend([s.text for s in self.suggestions['commands'] if s.category == 'system'])
        elif input_type == InputType.CODE_REQUEST:
            suggestions.extend([s.text for s in self.suggestions['code']])
        elif input_type == InputType.QUESTION:
            suggestions.extend([s.text for s in self.suggestions['questions']])
        
        # Add contextual suggestions based on partial matches
        for category in self.suggestions:
            for suggestion in self.suggestions[category]:
                if any(word in suggestion.text.lower() for word in text_lower.split()):
                    if suggestion.text not in suggestions:
                        suggestions.append(suggestion.text)
        
        return suggestions[:5]  # Limit to top 5 suggestions
    
    def _suggest_corrections(self, text: str) -> List[str]:
        """Suggest corrections for potential typos or unclear input"""
        corrections = []
        
        # Common command corrections
        common_commands = [
            'health check', 'agent status', 'force health check', 'train on conversations',
            'delegate to agents', 'show memory', 'turn on AC', 'what can you do',
            'analyze this code', 'self evolution status'
        ]
        
        # Find close matches for potential typos
        close_matches = difflib.get_close_matches(text.lower(), 
                                                [cmd.lower() for cmd in common_commands], 
                                                n=3, cutoff=0.6)
        
        for match in close_matches:
            # Find the original command
            for cmd in common_commands:
                if cmd.lower() == match:
                    corrections.append(cmd)
                    break
        
        return corrections
    
    def get_auto_completions(self, partial_text: str, limit: int = 5) -> List[InputSuggestion]:
        """Get auto-completion suggestions for partial input"""
        if len(partial_text) < 2:
            return []
        
        completions = []
        partial_lower = partial_text.lower()
        
        # Search through all suggestions
        for category in self.suggestions:
            for suggestion in self.suggestions[category]:
                if suggestion.text.lower().startswith(partial_lower):
                    completions.append(suggestion)
                elif partial_lower in suggestion.text.lower():
                    # Lower priority for contains matches
                    suggestion_copy = InputSuggestion(
                        suggestion.text, suggestion.type, 
                        suggestion.confidence * 0.8, 
                        suggestion.description, suggestion.category
                    )
                    completions.append(suggestion_copy)
        
        # Sort by confidence and return top results
        completions.sort(key=lambda x: x.confidence, reverse=True)
        return completions[:limit]
    
    def learn_from_input(self, text: str, user_action: str):
        """Learn from user input patterns to improve suggestions"""
        # Store successful patterns
        if user_action == 'accepted':
            if text not in [s.text for category in self.suggestions.values() for s in category]:
                # Add new successful pattern
                new_suggestion = InputSuggestion(
                    text, 'learned', 0.7, 'User-learned pattern', 'custom'
                )
                if 'custom' not in self.suggestions:
                    self.suggestions['custom'] = []
                self.suggestions['custom'].append(new_suggestion)
        
        # Update command history
        self.command_history.append({
            'text': text,
            'timestamp': datetime.now().isoformat(),
            'action': user_action
        })
        
        # Limit history size
        if len(self.command_history) > 100:
            self.command_history = self.command_history[-100:]
    
    def get_input_statistics(self) -> Dict[str, Any]:
        """Get statistics about input patterns and usage"""
        if not self.command_history:
            return {'total_inputs': 0}
        
        total_inputs = len(self.command_history)
        
        # Analyze input types
        type_counts = {}
        for entry in self.command_history:
            analysis = self.analyze_input(entry['text'])
            input_type = analysis.input_type.value
            type_counts[input_type] = type_counts.get(input_type, 0) + 1
        
        # Calculate success rate
        successful_inputs = len([e for e in self.command_history if e.get('action') == 'accepted'])
        success_rate = successful_inputs / total_inputs if total_inputs > 0 else 0
        
        return {
            'total_inputs': total_inputs,
            'input_types': type_counts,
            'success_rate': success_rate,
            'most_common_type': max(type_counts, key=type_counts.get) if type_counts else None,
            'custom_suggestions': len(self.suggestions.get('custom', []))
        }
