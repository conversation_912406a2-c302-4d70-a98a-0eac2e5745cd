"""
Complete Iron Man JARVIS Window
==============================
Full-screen Iron Man style interface that integrates with JARVIS functionality
while maintaining the futuristic HUD appearance

Features:
- Full Iron Man HUD interface
- Integrated chat functionality
- Real-time system monitoring
- Voice control integration
- Animated elements and effects
- Transparent overlays
"""

from src.gui.qt_compat import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                               Qt, QTimer, QLabel, QPushButton, QFrame)
from src.gui.iron_man_hud import IronManHUD
from src.gui.chat_widget import ChatWidget
from src.gui.input_widget import InputWidget

class IronManJarvisWindow(QMainWindow):
    """Complete Iron Man JARVIS interface window"""
    
    def __init__(self, config, main_window, parent=None):
        super().__init__(parent)
        self.config = config
        self.main_window = main_window  # Reference to main JARVIS window
        
        # Set up the Iron Man window
        self.init_iron_man_ui()
        self.setup_iron_man_styling()
        self.setup_connections()
        
        print("🚀 Iron Man JARVIS Window initialized")
    
    def init_iron_man_ui(self):
        """Initialize the Iron Man UI"""
        # Set window properties for full Iron Man experience
        self.setWindowTitle("J.A.R.V.I.S. - IRON MAN HUD INTERFACE")
        self.setGeometry(0, 0, 1920, 1080)  # Full HD by default
        self.setMinimumSize(1200, 800)
        
        # Make window frameless for true HUD experience
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout - overlay style
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create the Iron Man HUD as background
        self.iron_man_hud = IronManHUD(self.config)
        main_layout.addWidget(self.iron_man_hud)
        
        # Create overlay panels
        self.create_overlay_panels()
        
        # Create control panel
        self.create_hud_controls()
    
    def create_overlay_panels(self):
        """Create transparent overlay panels for chat and input"""
        # Chat overlay (top-right)
        self.chat_overlay = QFrame()
        self.chat_overlay.setObjectName("chatOverlay")
        self.chat_overlay.setFixedSize(400, 300)
        self.chat_overlay.move(self.width() - 420, 20)
        
        chat_layout = QVBoxLayout(self.chat_overlay)
        chat_layout.setContentsMargins(10, 10, 10, 10)
        
        # Mini chat widget
        self.mini_chat = ChatWidget(self.config)
        self.mini_chat.setMaximumHeight(250)
        chat_layout.addWidget(self.mini_chat)
        
        # Input overlay (bottom-center)
        self.input_overlay = QFrame()
        self.input_overlay.setObjectName("inputOverlay")
        self.input_overlay.setFixedSize(600, 80)
        self.input_overlay.move((self.width() - 600) // 2, self.height() - 100)
        
        input_layout = QVBoxLayout(self.input_overlay)
        input_layout.setContentsMargins(10, 10, 10, 10)
        
        # Mini input widget
        self.mini_input = InputWidget(self.config)
        input_layout.addWidget(self.mini_input)
        
        # Make overlays children of central widget for proper positioning
        self.chat_overlay.setParent(self.centralWidget())
        self.input_overlay.setParent(self.centralWidget())
    
    def create_hud_controls(self):
        """Create HUD control buttons"""
        # Control overlay (top-left)
        self.control_overlay = QFrame()
        self.control_overlay.setObjectName("controlOverlay")
        self.control_overlay.setFixedSize(200, 150)
        self.control_overlay.move(20, 20)
        
        control_layout = QVBoxLayout(self.control_overlay)
        control_layout.setContentsMargins(10, 10, 10, 10)
        
        # HUD control buttons
        self.exit_hud_button = QPushButton("❌ EXIT HUD")
        self.exit_hud_button.setObjectName("hudControlButton")
        control_layout.addWidget(self.exit_hud_button)
        
        self.fullscreen_button = QPushButton("🖥️ FULLSCREEN")
        self.fullscreen_button.setObjectName("hudControlButton")
        self.fullscreen_button.setCheckable(True)
        control_layout.addWidget(self.fullscreen_button)
        
        self.voice_button = QPushButton("🎤 VOICE")
        self.voice_button.setObjectName("hudControlButton")
        self.voice_button.setCheckable(True)
        control_layout.addWidget(self.voice_button)
        
        # Make control overlay child of central widget
        self.control_overlay.setParent(self.centralWidget())
    
    def setup_iron_man_styling(self):
        """Setup Iron Man HUD styling"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background: transparent;
            }}
            
            #chatOverlay {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 30, 60, 0.9),
                    stop:1 rgba(0, 20, 40, 0.8));
                border: 2px solid rgba(0, 150, 255, 0.8);
                border-radius: 15px;
            }}
            
            #inputOverlay {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 30, 60, 0.9),
                    stop:1 rgba(0, 20, 40, 0.8));
                border: 2px solid rgba(0, 150, 255, 0.8);
                border-radius: 15px;
            }}
            
            #controlOverlay {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 30, 60, 0.9),
                    stop:1 rgba(0, 20, 40, 0.8));
                border: 2px solid rgba(0, 150, 255, 0.8);
                border-radius: 15px;
            }}
            
            #hudControlButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0, 100, 200, 0.8),
                    stop:1 rgba(0, 50, 100, 0.8));
                border: 1px solid rgba(0, 150, 255, 0.6);
                border-radius: 8px;
                color: #00CCFF;
                font-weight: bold;
                font-size: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                padding: 5px;
                margin: 2px;
            }}
            
            #hudControlButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0, 150, 255, 0.9),
                    stop:1 rgba(0, 100, 200, 0.9));
                border: 2px solid rgba(0, 200, 255, 0.8);
            }}
            
            #hudControlButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0, 255, 100, 0.8),
                    stop:1 rgba(0, 200, 50, 0.8));
            }}
        """)
    
    def setup_connections(self):
        """Setup signal connections"""
        # Connect input to main JARVIS functionality
        self.mini_input.message_sent.connect(self.handle_hud_message)
        self.mini_input.special_command.connect(self.handle_hud_command)
        
        # Connect control buttons
        self.exit_hud_button.clicked.connect(self.exit_hud_mode)
        self.fullscreen_button.clicked.connect(self.toggle_fullscreen)
        self.voice_button.clicked.connect(self.toggle_voice_mode)
    
    def handle_hud_message(self, message: str):
        """Handle message from HUD input"""
        # Add to mini chat
        self.mini_chat.add_user_message(message)
        
        # Forward to main JARVIS window for processing
        if self.main_window:
            self.main_window.handle_user_message(message)
            
        # Add HUD notification
        self.iron_man_hud.add_hud_message(f"Processing: {message[:30]}...", "info")
    
    def handle_hud_command(self, command: str):
        """Handle special command from HUD"""
        if self.main_window:
            self.main_window.handle_special_command(command)
    
    def add_ai_response(self, response: str):
        """Add AI response to HUD chat"""
        self.mini_chat.add_ai_message(response)
        
        # Show brief notification on HUD
        self.iron_man_hud.add_hud_message("JARVIS Response Ready", "success")
    
    def exit_hud_mode(self):
        """Exit HUD mode and return to normal interface"""
        if self.main_window:
            # Toggle off the HUD button in main window
            self.main_window.hud_button.setChecked(False)
            self.main_window.toggle_iron_man_hud()
        
        self.close()
    
    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.fullscreen_button.isChecked():
            self.showFullScreen()
            self.fullscreen_button.setText("🪟 WINDOW")
        else:
            self.showNormal()
            self.fullscreen_button.setText("🖥️ FULLSCREEN")
    
    def toggle_voice_mode(self):
        """Toggle voice input mode"""
        if self.voice_button.isChecked():
            self.voice_button.setText("🔇 VOICE OFF")
            # Enable voice input
            if self.main_window and hasattr(self.main_window, 'voice_input'):
                self.main_window.voice_input.start_listening()
        else:
            self.voice_button.setText("🎤 VOICE")
            # Disable voice input
            if self.main_window and hasattr(self.main_window, 'voice_input'):
                self.main_window.voice_input.stop_listening()
    
    def resizeEvent(self, event):
        """Handle window resize"""
        super().resizeEvent(event)
        
        # Reposition overlay panels
        if hasattr(self, 'chat_overlay'):
            self.chat_overlay.move(self.width() - 420, 20)
        
        if hasattr(self, 'input_overlay'):
            self.input_overlay.move((self.width() - 600) // 2, self.height() - 100)
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key.Key_Escape:
            self.exit_hud_mode()
        else:
            super().keyPressEvent(event)
    
    def closeEvent(self, event):
        """Handle close event"""
        # Make sure to restore main window
        if self.main_window:
            self.main_window.iron_man_mode = False
            if hasattr(self.main_window, 'hud_button'):
                self.main_window.hud_button.setChecked(False)
                self.main_window.hud_button.setText("🚀 HUD")
        
        event.accept()
