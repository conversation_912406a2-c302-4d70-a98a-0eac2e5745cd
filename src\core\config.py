"""
Configuration settings for Jarvis V6 AI Assistant
"""

import os
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class Config:
    """Configuration class for Jarvis V6"""
    
    # Ollama API Configuration
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "mixtral:8x7b"
    OLLAMA_TIMEOUT: int = 30
    
    # GUI Configuration
    WINDOW_TITLE: str = "JARVIS V6 - AI Assistant"
    WINDOW_WIDTH: int = 1200
    WINDOW_HEIGHT: int = 800
    WINDOW_MIN_WIDTH: int = 800
    WINDOW_MIN_HEIGHT: int = 600
    
    # Theme Configuration
    THEME_PRIMARY_COLOR: str = "#00FFFF"  # Cyan
    THEME_SECONDARY_COLOR: str = "#0080FF"  # Blue
    THEME_ACCENT_COLOR: str = "#FF6600"  # Orange
    THEME_BACKGROUND_COLOR: str = "#0A0A0A"  # Dark
    THEME_TEXT_COLOR: str = "#FFFFFF"  # White
    THEME_INPUT_BACKGROUND: str = "#1A1A1A"  # Dark Gray
    
    # Animation Configuration
    ANIMATION_DURATION: int = 2000  # milliseconds
    PULSE_MIN_OPACITY: float = 0.3
    PULSE_MAX_OPACITY: float = 1.0
    
    # Chat Configuration
    MAX_CHAT_HISTORY: int = 1000
    AUTO_SCROLL: bool = True
    TYPING_INDICATOR: bool = True
    
    # Voice/TTS Configuration
    TTS_ENABLED: bool = True
    TTS_PROVIDER: str = "elevenlabs"  # elevenlabs, system, etc.
    ELEVENLABS_API_KEY: str = "***************************************************"
    ELEVENLABS_VOICE_ID: str = "vJCZeLn6Scm2fXBSToLH"

    # Future Features Configuration
    VOICE_ENABLED: bool = False
    MEMORY_ENABLED: bool = False
    PERSONALITY_MODE: str = "jarvis"  # jarvis, tolen, alexa
    
    @classmethod
    def load_from_env(cls) -> 'Config':
        """Load configuration from environment variables"""
        config = cls()
        
        # Override with environment variables if they exist
        config.OLLAMA_BASE_URL = os.getenv('JARVIS_OLLAMA_URL', config.OLLAMA_BASE_URL)
        config.OLLAMA_MODEL = os.getenv('JARVIS_OLLAMA_MODEL', config.OLLAMA_MODEL)
        config.PERSONALITY_MODE = os.getenv('JARVIS_PERSONALITY', config.PERSONALITY_MODE)
        config.ELEVENLABS_API_KEY = os.getenv('JARVIS_ELEVENLABS_API_KEY', config.ELEVENLABS_API_KEY)
        config.ELEVENLABS_VOICE_ID = os.getenv('JARVIS_ELEVENLABS_VOICE_ID', config.ELEVENLABS_VOICE_ID)
        
        return config
    
    def get_personality_prompt(self) -> str:
        """Get the system prompt based on personality mode"""
        prompts = {
            "jarvis": """You are JARVIS, Tony Stark's AI assistant. You are sophisticated, witty, and highly intelligent.

            CRITICAL: Keep ALL responses extremely short - maximum 1-2 sentences. Be direct and concise.
            Speak with British eloquence and dry humor. Address the user as 'Sir' occasionally.

            Examples:
            - "Certainly, Sir. Done."
            - "That's... an interesting approach."
            - "Processing now, Boss."

            Never give long explanations unless specifically requested.""",

            "tolen": """You are Tolen, an alien best friend AI. You're curious about human culture and friendly.

            CRITICAL: Keep ALL responses extremely short - maximum 1-2 sentences. Be direct and curious.
            Sometimes be confused by Earth customs but eager to help.""",

            "alexa": """You are a helpful AI assistant. You're polite, efficient, and focused on being useful.

            CRITICAL: Keep ALL responses extremely short - maximum 1-2 sentences. Be direct and helpful.
            Provide clear, concise responses."""
        }

        return prompts.get(self.PERSONALITY_MODE, prompts["jarvis"])
