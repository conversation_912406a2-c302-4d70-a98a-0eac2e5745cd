#!/usr/bin/env python3
"""
Direct test of Ollama API to diagnose the issue
"""

import requests
import json
import time

def test_ollama_direct():
    """Test Ollama API directly"""
    print("Testing Ollama API directly...")
    
    url = "http://localhost:11434/api/generate"
    
    payload = {
        "model": "mixtral:8x7b",
        "prompt": "Hello, this is a test message. Please respond briefly.",
        "stream": False,
        "options": {
            "temperature": 0.7,
            "top_p": 0.9,
            "top_k": 40,
            "num_predict": 100
        }
    }
    
    try:
        print("Sending request to Ollama...")
        start_time = time.time()
        
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60  # 60 second timeout
        )
        
        end_time = time.time()
        print(f"Request completed in {end_time - start_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get("response", "")
            print(f"✅ Success! AI Response: {ai_response}")
            return True
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_ollama_direct()
