"""
Screen Vision Service for JARVIS V6
===================================

Provides screen capture and computer vision capabilities:
- Real-time screen monitoring
- Screenshot analysis
- Text extraction (OCR)
- UI element detection
- Application identification
- Content analysis and description
- Contextual assistance based on screen content

This service enables JARVIS to see and understand what's happening on your screen.
"""

import cv2
import numpy as np
import pyautogui
import pytesseract
import time
import threading
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import base64
import io
from PIL import Image, ImageDraw, ImageFont
import re
import os

class ScreenAnalysisType(Enum):
    """Types of screen analysis"""
    FULL_SCREEN = "full_screen"
    ACTIVE_WINDOW = "active_window"
    REGION = "region"
    TEXT_EXTRACTION = "text_extraction"
    UI_ELEMENTS = "ui_elements"
    APPLICATION_DETECTION = "application_detection"

class ContentType(Enum):
    """Types of content detected on screen"""
    TEXT_DOCUMENT = "text_document"
    CODE_EDITOR = "code_editor"
    WEB_BROWSER = "web_browser"
    TERMINAL = "terminal"
    IMAGE_VIEWER = "image_viewer"
    VIDEO_PLAYER = "video_player"
    GAME = "game"
    DESKTOP = "desktop"
    UNKNOWN = "unknown"

@dataclass
class ScreenRegion:
    """Represents a region of the screen"""
    x: int
    y: int
    width: int
    height: int
    confidence: float = 1.0

@dataclass
class DetectedElement:
    """Represents a detected UI element"""
    element_type: str
    region: ScreenRegion
    text: Optional[str] = None
    confidence: float = 0.0

@dataclass
class ScreenAnalysis:
    """Result of screen analysis"""
    success: bool
    content_type: ContentType
    description: str
    extracted_text: str
    detected_elements: List[DetectedElement]
    applications: List[str]
    suggestions: List[str]
    confidence: float
    processing_time: float
    screenshot_path: Optional[str] = None
    error_message: Optional[str] = None

class ScreenVisionService:
    """
    Screen Vision Service for JARVIS V6
    
    Provides comprehensive screen monitoring and analysis capabilities
    including OCR, UI element detection, and contextual understanding.
    """
    
    def __init__(self, enable_continuous_monitoring: bool = False):
        self.monitoring_active = False
        self.continuous_monitoring = enable_continuous_monitoring
        self.last_screenshot = None
        self.last_analysis = None
        self.monitoring_thread = None
        self.analysis_history = []
        self.screenshot_dir = "screenshots"
        
        # Create screenshots directory
        os.makedirs(self.screenshot_dir, exist_ok=True)
        
        # Configure pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
        print("👁️ Screen Vision Service initialized")
        print(f"📁 Screenshots directory: {self.screenshot_dir}")
        print(f"🔄 Continuous monitoring: {'Enabled' if enable_continuous_monitoring else 'Disabled'}")
    
    def capture_screenshot(self, region: Optional[ScreenRegion] = None) -> Optional[np.ndarray]:
        """Capture screenshot of screen or specific region"""
        try:
            if region:
                # Capture specific region
                screenshot = pyautogui.screenshot(region=(region.x, region.y, region.width, region.height))
            else:
                # Capture full screen
                screenshot = pyautogui.screenshot()
            
            # Convert PIL image to OpenCV format
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            self.last_screenshot = screenshot_cv
            
            return screenshot_cv
            
        except Exception as e:
            print(f"Error capturing screenshot: {e}")
            return None
    
    def save_screenshot(self, image: np.ndarray, prefix: str = "screen") -> str:
        """Save screenshot to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{prefix}_{timestamp}.png"
            filepath = os.path.join(self.screenshot_dir, filename)
            
            cv2.imwrite(filepath, image)
            return filepath
            
        except Exception as e:
            print(f"Error saving screenshot: {e}")
            return ""
    
    def extract_text_ocr(self, image: np.ndarray) -> str:
        """Extract text from image using OCR"""
        try:
            # Convert to PIL Image for pytesseract
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

            # Extract text using Tesseract OCR
            extracted_text = pytesseract.image_to_string(pil_image, config='--psm 6')

            # Clean up the text
            cleaned_text = re.sub(r'\n+', '\n', extracted_text.strip())

            return cleaned_text

        except Exception as e:
            print(f"OCR Error: {e}")
            if "tesseract" in str(e).lower():
                return "[OCR unavailable - Tesseract not installed. Screen analysis will work without text extraction.]"
            return "[OCR extraction failed]"
    
    def detect_ui_elements(self, image: np.ndarray) -> List[DetectedElement]:
        """Detect UI elements like buttons, text fields, etc."""
        try:
            elements = []
            
            # Convert to grayscale for processing
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Detect buttons (rectangular shapes with text)
            contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                # Filter by area to avoid noise
                area = cv2.contourArea(contour)
                if 100 < area < 10000:  # Reasonable button size
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Check if it's roughly rectangular (button-like)
                    aspect_ratio = w / h
                    if 0.5 < aspect_ratio < 5.0:  # Reasonable aspect ratio
                        region = ScreenRegion(x, y, w, h, 0.7)
                        
                        # Try to extract text from this region
                        roi = image[y:y+h, x:x+w]
                        text = self.extract_text_ocr(roi)
                        
                        if text.strip():  # If text found, likely a button
                            element = DetectedElement(
                                element_type="button",
                                region=region,
                                text=text.strip(),
                                confidence=0.7
                            )
                            elements.append(element)
            
            return elements[:10]  # Limit to top 10 elements
            
        except Exception as e:
            print(f"Error detecting UI elements: {e}")
            return []
    
    def identify_applications(self, image: np.ndarray, extracted_text: str) -> List[str]:
        """Identify applications based on visual and text cues"""
        try:
            applications = []
            text_lower = extracted_text.lower()
            
            # Check for common application indicators
            app_indicators = {
                "Visual Studio Code": ["visual studio code", "vscode", "explorer", "terminal", "problems"],
                "Chrome": ["chrome", "google", "address bar", "bookmarks", "tabs"],
                "Firefox": ["firefox", "mozilla", "address bar", "bookmarks"],
                "Notepad": ["notepad", "untitled", "save as"],
                "Word": ["microsoft word", "document", "page layout", "review"],
                "Excel": ["microsoft excel", "worksheet", "formula bar"],
                "PowerPoint": ["microsoft powerpoint", "slide", "presentation"],
                "Terminal": ["command prompt", "powershell", "terminal", "cmd", "bash"],
                "File Explorer": ["file explorer", "this pc", "documents", "downloads"],
                "Discord": ["discord", "direct messages", "voice channels"],
                "Slack": ["slack", "channels", "direct messages", "workspace"],
                "Zoom": ["zoom", "meeting", "participants", "share screen"],
                "Spotify": ["spotify", "now playing", "playlists", "artists"]
            }
            
            for app_name, keywords in app_indicators.items():
                if any(keyword in text_lower for keyword in keywords):
                    applications.append(app_name)
            
            # If no specific app detected, try to determine content type
            if not applications:
                if any(keyword in text_lower for keyword in ["def ", "class ", "function", "import", "console"]):
                    applications.append("Code Editor")
                elif any(keyword in text_lower for keyword in ["http", "www", "browser", "search"]):
                    applications.append("Web Browser")
                elif len(text_lower.split()) > 50:  # Lots of text
                    applications.append("Text Document")
            
            return applications
            
        except Exception as e:
            print(f"Error identifying applications: {e}")
            return ["Unknown"]
    
    def determine_content_type(self, applications: List[str], extracted_text: str) -> ContentType:
        """Determine the type of content being displayed"""
        try:
            text_lower = extracted_text.lower()
            
            # Check applications first
            for app in applications:
                if "code" in app.lower() or "visual studio" in app.lower():
                    return ContentType.CODE_EDITOR
                elif "chrome" in app.lower() or "firefox" in app.lower() or "browser" in app.lower():
                    return ContentType.WEB_BROWSER
                elif "terminal" in app.lower() or "cmd" in app.lower() or "powershell" in app.lower():
                    return ContentType.TERMINAL
                elif "word" in app.lower() or "notepad" in app.lower():
                    return ContentType.TEXT_DOCUMENT
            
            # Check content patterns
            if any(keyword in text_lower for keyword in ["def ", "class ", "function", "import", "console.log"]):
                return ContentType.CODE_EDITOR
            elif any(keyword in text_lower for keyword in ["http", "www", "search", "browser"]):
                return ContentType.WEB_BROWSER
            elif any(keyword in text_lower for keyword in ["c:\\", "directory", "folder", "file"]):
                return ContentType.TERMINAL
            elif len(extracted_text.split()) > 100:  # Long text content
                return ContentType.TEXT_DOCUMENT
            elif not extracted_text.strip():  # No text, might be desktop
                return ContentType.DESKTOP
            else:
                return ContentType.UNKNOWN
                
        except Exception as e:
            print(f"Error determining content type: {e}")
            return ContentType.UNKNOWN
    
    def generate_description(self, content_type: ContentType, applications: List[str], extracted_text: str, elements: List[DetectedElement]) -> str:
        """Generate human-readable description of screen content"""
        try:
            description_parts = []
            
            # Application context
            if applications:
                app_list = ", ".join(applications)
                description_parts.append(f"Currently viewing: {app_list}")
            
            # Content type description
            content_descriptions = {
                ContentType.CODE_EDITOR: "You're working in a code editor",
                ContentType.WEB_BROWSER: "You're browsing the web",
                ContentType.TERMINAL: "You're using a terminal/command line",
                ContentType.TEXT_DOCUMENT: "You're viewing a text document",
                ContentType.DESKTOP: "You're on the desktop",
                ContentType.UNKNOWN: "You're viewing some application"
            }
            
            description_parts.append(content_descriptions.get(content_type, "Unknown content"))
            
            # Text content summary
            if extracted_text.strip():
                word_count = len(extracted_text.split())
                if word_count > 50:
                    description_parts.append(f"The screen contains substantial text content ({word_count} words)")
                elif word_count > 10:
                    description_parts.append(f"The screen contains some text content ({word_count} words)")
                else:
                    description_parts.append("The screen contains minimal text")
            
            # UI elements
            if elements:
                button_count = len([e for e in elements if e.element_type == "button"])
                if button_count > 0:
                    description_parts.append(f"I can see {button_count} interactive elements")
            
            return ". ".join(description_parts) + "."
            
        except Exception as e:
            print(f"Error generating description: {e}")
            return "I can see your screen but couldn't analyze the content."
    
    def generate_suggestions(self, content_type: ContentType, applications: List[str], extracted_text: str) -> List[str]:
        """Generate contextual suggestions based on screen content"""
        try:
            suggestions = []
            
            if content_type == ContentType.CODE_EDITOR:
                suggestions.extend([
                    "I can help analyze or improve the code you're working on",
                    "Ask me to explain any code concepts you're unsure about",
                    "I can suggest optimizations or best practices for your code"
                ])
            elif content_type == ContentType.WEB_BROWSER:
                suggestions.extend([
                    "I can help summarize web content you're reading",
                    "Ask me questions about what you're viewing",
                    "I can help with research or finding related information"
                ])
            elif content_type == ContentType.TERMINAL:
                suggestions.extend([
                    "I can help with command line operations",
                    "Ask me about shell commands or scripting",
                    "I can explain terminal output or error messages"
                ])
            elif content_type == ContentType.TEXT_DOCUMENT:
                suggestions.extend([
                    "I can help edit or improve the text you're working on",
                    "Ask me to proofread or suggest improvements",
                    "I can help with formatting or structure"
                ])
            
            # General suggestions
            suggestions.extend([
                "Ask me 'what do you see?' for a detailed analysis",
                "I can help with any questions about what's on your screen"
            ])
            
            return suggestions[:5]  # Limit to 5 suggestions
            
        except Exception as e:
            print(f"Error generating suggestions: {e}")
            return ["I can help analyze what's on your screen"]
    
    def analyze_screen(self, analysis_type: ScreenAnalysisType = ScreenAnalysisType.FULL_SCREEN, region: Optional[ScreenRegion] = None) -> ScreenAnalysis:
        """Perform comprehensive screen analysis"""
        start_time = time.time()
        
        try:
            # Capture screenshot
            screenshot = self.capture_screenshot(region)
            if screenshot is None:
                return ScreenAnalysis(
                    success=False,
                    content_type=ContentType.UNKNOWN,
                    description="Failed to capture screenshot",
                    extracted_text="",
                    detected_elements=[],
                    applications=[],
                    suggestions=[],
                    confidence=0.0,
                    processing_time=time.time() - start_time,
                    error_message="Screenshot capture failed"
                )
            
            # Save screenshot
            screenshot_path = self.save_screenshot(screenshot, "analysis")
            
            # Extract text using OCR
            extracted_text = self.extract_text_ocr(screenshot)
            
            # Detect UI elements
            detected_elements = self.detect_ui_elements(screenshot)
            
            # Identify applications
            applications = self.identify_applications(screenshot, extracted_text)
            
            # Determine content type
            content_type = self.determine_content_type(applications, extracted_text)
            
            # Generate description
            description = self.generate_description(content_type, applications, extracted_text, detected_elements)
            
            # Generate suggestions
            suggestions = self.generate_suggestions(content_type, applications, extracted_text)
            
            # Calculate confidence based on text extraction and element detection
            confidence = min(1.0, (len(extracted_text) / 100 + len(detected_elements) / 10) / 2)
            
            analysis = ScreenAnalysis(
                success=True,
                content_type=content_type,
                description=description,
                extracted_text=extracted_text,
                detected_elements=detected_elements,
                applications=applications,
                suggestions=suggestions,
                confidence=confidence,
                processing_time=time.time() - start_time,
                screenshot_path=screenshot_path
            )
            
            # Store in history
            self.analysis_history.append({
                "analysis": analysis,
                "timestamp": datetime.now().isoformat()
            })
            
            # Keep only last 10 analyses
            if len(self.analysis_history) > 10:
                self.analysis_history = self.analysis_history[-10:]
            
            self.last_analysis = analysis
            return analysis
            
        except Exception as e:
            return ScreenAnalysis(
                success=False,
                content_type=ContentType.UNKNOWN,
                description=f"Error analyzing screen: {str(e)}",
                extracted_text="",
                detected_elements=[],
                applications=[],
                suggestions=[],
                confidence=0.0,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def start_continuous_monitoring(self, interval: float = 5.0):
        """Start continuous screen monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        def monitor_loop():
            while self.monitoring_active:
                try:
                    self.analyze_screen()
                    time.sleep(interval)
                except Exception as e:
                    print(f"Error in monitoring loop: {e}")
                    time.sleep(interval)
        
        self.monitoring_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitoring_thread.start()
        
        print(f"👁️ Started continuous screen monitoring (interval: {interval}s)")
    
    def stop_continuous_monitoring(self):
        """Stop continuous screen monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1.0)
        
        print("👁️ Stopped continuous screen monitoring")
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get service status and statistics"""
        return {
            "service_name": "Screen Vision Service",
            "monitoring_active": self.monitoring_active,
            "continuous_monitoring": self.continuous_monitoring,
            "analysis_history_count": len(self.analysis_history),
            "last_analysis_time": self.analysis_history[-1]["timestamp"] if self.analysis_history else None,
            "screenshot_directory": self.screenshot_dir,
            "capabilities": [
                "Screenshot capture",
                "OCR text extraction", 
                "UI element detection",
                "Application identification",
                "Content analysis",
                "Contextual suggestions"
            ]
        }
