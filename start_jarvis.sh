#!/bin/bash

echo "Starting Jarvis V6 AI Assistant..."
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8+ and try again"
    exit 1
fi

# Check if <PERSON>llama is running
echo "Checking Ollama connection..."
if ! python3 -c "import requests; requests.get('http://localhost:11434/api/tags', timeout=5)" &> /dev/null; then
    echo "Warning: Ollama server is not running"
    echo "Please start Ollama and ensure Mixtral model is available"
    echo
    echo "To install Ollama and Mixtral:"
    echo "1. Download Ollama from https://ollama.ai"
    echo "2. Run: ollama pull mixtral:8x7b"
    echo "3. Start Ollama service"
    echo
    read -p "Continue anyway? (y/n): " choice
    if [[ ! "$choice" =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Start the application
echo "Starting Jarvis V6..."
python3 main.py
