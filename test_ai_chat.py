#!/usr/bin/env python3
"""
Test the AI chat functionality
"""

import sys
import time

def test_ai_worker():
    """Test the OllamaWorker class"""
    print("Testing AI chat functionality...")
    
    try:
        # Import our modules
        from src.gui.qt_compat import QApplication, QObject
        from src.core.config import Config
        from src.ai.ollama_client import OllamaWorker
        
        # Create QApplication (required for Qt signals)
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Load config
        config = Config.load_from_env()
        
        # Test message
        test_message = "Hello, this is a test. Please respond briefly."
        system_prompt = config.get_personality_prompt()
        
        print(f"Sending message: '{test_message}'")
        print("Waiting for AI response...")
        
        # Create worker
        worker = OllamaWorker(config, test_message, system_prompt)
        
        # Track response
        response_received = False
        error_occurred = None
        ai_response = ""
        
        def on_response(response):
            nonlocal response_received, ai_response
            response_received = True
            ai_response = response
            print(f"✅ AI Response: {response}")
        
        def on_error(error):
            nonlocal error_occurred
            error_occurred = error
            print(f"❌ Error: {error}")
        
        # Connect signals
        worker.response_ready.connect(on_response)
        worker.error_occurred.connect(on_error)
        
        # Start worker
        start_time = time.time()
        worker.start()
        
        # Wait for completion (with timeout)
        timeout = 60  # 60 seconds
        while not response_received and error_occurred is None:
            app.processEvents()  # Process Qt events
            time.sleep(0.1)
            
            if time.time() - start_time > timeout:
                print("⚠️ Test timed out")
                break
        
        # Wait for worker to finish
        worker.wait()
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response_received:
            print(f"✅ Test completed successfully in {duration:.2f} seconds")
            return True
        elif error_occurred:
            print(f"❌ Test failed with error: {error_occurred}")
            return False
        else:
            print("❌ Test timed out")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ai_worker()
    if success:
        print("\n🎉 AI chat is working correctly!")
    else:
        print("\n❌ AI chat test failed!")
    
    sys.exit(0 if success else 1)
