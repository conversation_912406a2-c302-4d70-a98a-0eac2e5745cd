"""
Chat Widget for Jarvis V6
Displays conversation history with futuristic styling
"""

from src.gui.qt_compat import (QWidget, QVBoxLayout, QTextEdit, QLabel, QFrame,
                               Qt, QTimer, pyqtSignal, QFont, QTextCursor, QColor)
from datetime import datetime
from src.core.config import Config

class ChatWidget(QWidget):
    """Widget for displaying chat conversation"""
    
    message_clicked = pyqtSignal(str)
    
    def __init__(self, config: Config, parent=None):
        super().__init__(parent)
        self.config = config
        self.typing_timer = QTimer()
        self.typing_dots = 0
        
        self.init_ui()
        self.setup_styling()
        self.setup_connections()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # Chat display area
        self.chat_display = QTextEdit()
        self.chat_display.setObjectName("chatDisplay")
        self.chat_display.setReadOnly(True)
        self.chat_display.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.chat_display.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # Welcome message
        self.add_system_message("JARVIS V6 AI Assistant initialized. How may I assist you today?")
        
        layout.addWidget(self.chat_display)
        
    def setup_styling(self):
        """Setup the HUD chat widget styling"""
        self.setStyleSheet(f"""
            #chatDisplay {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 20, 40, 0.9),
                    stop:1 rgba(0, 10, 20, 0.8));
                border: 2px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 15px;
                color: {self.config.THEME_TEXT_COLOR};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                padding: 15px;
                selection-background-color: {self.config.THEME_SECONDARY_COLOR};
            }}
            
            QScrollBar:vertical {{
                background-color: {self.config.THEME_INPUT_BACKGROUND};
                width: 12px;
                border-radius: 6px;
            }}
            
            QScrollBar::handle:vertical {{
                background-color: {self.config.THEME_PRIMARY_COLOR};
                border-radius: 6px;
                min-height: 20px;
            }}
            
            QScrollBar::handle:vertical:hover {{
                background-color: {self.config.THEME_SECONDARY_COLOR};
            }}
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
            }}
        """)
        
    def setup_connections(self):
        """Setup signal connections"""
        self.typing_timer.timeout.connect(self.update_typing_indicator)
        
    def add_user_message(self, message: str):
        """Add a user message to the chat"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Format user message
        formatted_message = f"""
        <div style="margin: 10px 0; padding: 8px; background-color: rgba(0, 128, 255, 0.1); 
                    border-left: 3px solid {self.config.THEME_SECONDARY_COLOR}; border-radius: 5px;">
            <span style="color: {self.config.THEME_SECONDARY_COLOR}; font-weight: bold;">
                [{timestamp}] USER:
            </span><br>
            <span style="color: {self.config.THEME_TEXT_COLOR}; margin-left: 10px;">
                {self.escape_html(message)}
            </span>
        </div>
        """
        
        self.chat_display.append(formatted_message)
        self.scroll_to_bottom()
        
    def add_ai_message(self, message: str):
        """Add an AI response message to the chat"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        personality = self.config.PERSONALITY_MODE.upper()
        
        # Format AI message
        formatted_message = f"""
        <div style="margin: 10px 0; padding: 8px; background-color: rgba(0, 255, 255, 0.1); 
                    border-left: 3px solid {self.config.THEME_PRIMARY_COLOR}; border-radius: 5px;">
            <span style="color: {self.config.THEME_PRIMARY_COLOR}; font-weight: bold;">
                [{timestamp}] {personality}:
            </span><br>
            <span style="color: {self.config.THEME_TEXT_COLOR}; margin-left: 10px;">
                {self.escape_html(message)}
            </span>
        </div>
        """
        
        self.chat_display.append(formatted_message)
        self.scroll_to_bottom()
        
    def add_system_message(self, message: str):
        """Add a system message to the chat"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Format system message
        formatted_message = f"""
        <div style="margin: 10px 0; padding: 8px; background-color: rgba(255, 102, 0, 0.1); 
                    border-left: 3px solid {self.config.THEME_ACCENT_COLOR}; border-radius: 5px;">
            <span style="color: {self.config.THEME_ACCENT_COLOR}; font-weight: bold;">
                [{timestamp}] SYSTEM:
            </span><br>
            <span style="color: {self.config.THEME_TEXT_COLOR}; margin-left: 10px; font-style: italic;">
                {self.escape_html(message)}
            </span>
        </div>
        """
        
        self.chat_display.append(formatted_message)
        self.scroll_to_bottom()
        
    def show_typing_indicator(self):
        """Show typing indicator animation"""
        if self.typing_timer and self.typing_timer.isActive():
            return  # Already showing to prevent spam

        self.typing_dots = 0
        # Disable animation to prevent spam - just show static indicator
        # self.typing_timer.start(500)  # Commented out to stop animation
        
        # Add initial typing indicator
        self.typing_message_html = f"""
        <div id="typing-indicator" style="margin: 10px 0; padding: 8px; 
                    background-color: rgba(0, 255, 255, 0.1); 
                    border-left: 3px solid {self.config.THEME_PRIMARY_COLOR}; border-radius: 5px;">
            <span style="color: {self.config.THEME_PRIMARY_COLOR}; font-weight: bold;">
                {self.config.PERSONALITY_MODE.upper()} is processing...
            </span>
            <span id="dots" style="color: {self.config.THEME_PRIMARY_COLOR};">.</span>
        </div>
        """
        
        self.chat_display.append(self.typing_message_html)
        self.scroll_to_bottom()
        
    def update_typing_indicator(self):
        """Update the typing indicator animation - DISABLED to prevent spam"""
        # Disabled to prevent "JARVIS is thinking..." spam in terminal
        # The static typing indicator is sufficient
        pass
        
    def hide_typing_indicator(self):
        """Hide the typing indicator"""
        self.typing_timer.stop()
        
        # Remove the last message (typing indicator)
        cursor = self.chat_display.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        cursor.movePosition(QTextCursor.MoveOperation.StartOfBlock, QTextCursor.MoveMode.KeepAnchor)
        cursor.removeSelectedText()
        
    def scroll_to_bottom(self):
        """Scroll to the bottom of the chat"""
        if self.config.AUTO_SCROLL:
            scrollbar = self.chat_display.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
    def clear_chat(self):
        """Clear all chat messages"""
        self.chat_display.clear()
        self.add_system_message("Chat cleared. How may I assist you?")
        
    def export_chat(self) -> str:
        """Export chat history as plain text"""
        return self.chat_display.toPlainText()
        
    def escape_html(self, text: str) -> str:
        """Escape HTML characters in text"""
        return (text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace('"', "&quot;")
                   .replace("'", "&#x27;")
                   .replace("\n", "<br>"))
                   
    def set_font_size(self, size: int):
        """Set the chat font size"""
        font = self.chat_display.font()
        font.setPointSize(size)
        self.chat_display.setFont(font)
        
    def get_message_count(self) -> int:
        """Get the number of messages in chat"""
        # This is a simple approximation
        return len(self.chat_display.toPlainText().split('\n'))
        
    def limit_chat_history(self):
        """Limit chat history to max messages"""
        if self.get_message_count() > self.config.MAX_CHAT_HISTORY:
            # Keep only the last portion of messages
            text = self.chat_display.toHtml()
            lines = text.split('\n')
            keep_lines = lines[-int(self.config.MAX_CHAT_HISTORY * 0.8):]
            self.chat_display.setHtml('\n'.join(keep_lines))
