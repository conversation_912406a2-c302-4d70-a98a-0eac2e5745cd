"""
Self-Healing AI Code System for JARVIS V6
Continuously monitors, detects, and fixes code issues autonomously

This system provides:
- Real-time code monitoring and analysis
- Bug detection and inefficiency identification
- Automated improvement proposals
- Safe implementation with simulated testing
- Continuous self-optimization
"""

import os
import ast
import time
import threading
import hashlib
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import json

@dataclass
class CodeIssue:
    """Represents a detected code issue"""
    file_path: str
    line_number: int
    issue_type: str  # 'bug', 'inefficiency', 'improvement', 'security'
    severity: str    # 'critical', 'high', 'medium', 'low'
    description: str
    suggested_fix: str
    confidence: float
    detected_at: datetime
    
@dataclass
class CodeImprovement:
    """Represents a proposed code improvement"""
    issue: CodeIssue
    original_code: str
    improved_code: str
    test_results: Dict[str, Any]
    safety_score: float
    performance_impact: str
    implementation_ready: bool

class SelfHealingSystem:
    """Autonomous self-healing and improvement system for JARVIS"""
    
    def __init__(self, config, deepseek_coder=None):
        self.config = config
        self.deepseek_coder = deepseek_coder
        self.monitoring_active = True
        self.healing_active = True
        
        # System state
        self.monitored_files = []
        self.file_checksums = {}
        self.detected_issues = []
        self.pending_improvements = []
        self.applied_fixes = []
        self.processed_issues = set()  # Track processed issues to avoid loops
        self.last_issue_count = 0  # Track issue count to detect loops
        
        # Monitoring settings
        self.scan_interval = 300  # 5 minutes
        self.auto_fix_threshold = 0.85  # Auto-apply fixes with >85% confidence
        self.critical_fix_threshold = 0.95  # Immediately apply critical fixes
        
        # Directories to monitor
        self.monitored_dirs = [
            'src/ai/',
            'src/gui/',
            'src/utils/',
            'main.py'
        ]
        
        # Initialize monitoring
        self._initialize_monitoring()
        self._start_monitoring_thread()
        
        print("🔥 Self-Healing AI Code System initialized")
        print(f"🔍 Monitoring {len(self.monitored_files)} files")
        print(f"⚡ Auto-fix threshold: {self.auto_fix_threshold:.0%}")
        print(f"🚨 Critical fix threshold: {self.critical_fix_threshold:.0%}")
    
    def _initialize_monitoring(self):
        """Initialize file monitoring system"""
        self.monitored_files = []
        
        for path in self.monitored_dirs:
            if os.path.isfile(path):
                self.monitored_files.append(path)
            elif os.path.isdir(path):
                for root, dirs, files in os.walk(path):
                    for file in files:
                        if file.endswith('.py'):
                            file_path = os.path.join(root, file)
                            self.monitored_files.append(file_path)
        
        # Calculate initial checksums
        self._update_file_checksums()
    
    def _update_file_checksums(self):
        """Update checksums for all monitored files"""
        for file_path in self.monitored_files:
            try:
                if os.path.exists(file_path):
                    with open(file_path, 'rb') as f:
                        content = f.read()
                        checksum = hashlib.md5(content).hexdigest()
                        self.file_checksums[file_path] = checksum
            except Exception as e:
                print(f"⚠️ Error calculating checksum for {file_path}: {e}")
    
    def _start_monitoring_thread(self):
        """Start background monitoring thread"""
        def monitor_loop():
            while self.monitoring_active:
                try:
                    self._perform_health_check()
                    time.sleep(self.scan_interval)
                except Exception as e:
                    print(f"❌ Error in monitoring loop: {e}")
                    time.sleep(60)  # Wait 1 minute before retrying
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        print("🔄 Self-healing monitoring thread started")
    
    def _perform_health_check(self):
        """Perform comprehensive health check of JARVIS code"""
        print("🔍 Performing self-healing health check...")
        
        # Check for file changes
        changed_files = self._detect_file_changes()
        
        # Analyze all monitored files
        new_issues = []
        for file_path in self.monitored_files:
            if os.path.exists(file_path):
                issues = self._analyze_file(file_path)
                new_issues.extend(issues)
        
        # Check for issue loop (same number of issues repeatedly)
        total_issues = len(self.detected_issues) + len(new_issues)
        if total_issues == self.last_issue_count and total_issues > 500:
            print(f"⚠️ Detected potential issue loop ({total_issues} issues). Disabling auto-healing.")
            self.healing_active = False
            self.scan_interval = 3600  # 1 hour
            print("🛡️ Self-healing disabled to prevent system overload")
            return

        # If too many issues detected, reduce frequency
        if total_issues > 1000:
            print(f"⚠️ Too many issues detected ({total_issues}). Reducing scan frequency.")
            self.scan_interval = min(self.scan_interval * 2, 1800)  # Max 30 minutes
            return

        # Process new issues
        if new_issues:
            print(f"🚨 Detected {len(new_issues)} new code issues")
            # Only add truly new issues (not duplicates)
            unique_new_issues = []
            for issue in new_issues:
                issue_key = f"{issue.file_path}:{issue.line_number}:{issue.issue_type}"
                if issue_key not in self.processed_issues:
                    unique_new_issues.append(issue)
                    self.processed_issues.add(issue_key)

            if unique_new_issues:
                self.detected_issues.extend(unique_new_issues)
                self._process_detected_issues(unique_new_issues)
            else:
                print("✅ All detected issues were already processed")
        else:
            print("✅ No new issues detected - JARVIS code is healthy")

        self.last_issue_count = len(self.detected_issues)
        
        # Update checksums
        self._update_file_checksums()
    
    def _detect_file_changes(self) -> List[str]:
        """Detect which files have changed since last check"""
        changed_files = []
        
        for file_path in self.monitored_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'rb') as f:
                        content = f.read()
                        current_checksum = hashlib.md5(content).hexdigest()
                        
                    old_checksum = self.file_checksums.get(file_path)
                    if old_checksum and current_checksum != old_checksum:
                        changed_files.append(file_path)
                        print(f"📝 Detected change in {file_path}")
                        
                except Exception as e:
                    print(f"⚠️ Error checking {file_path}: {e}")
        
        return changed_files
    
    def _analyze_file(self, file_path: str) -> List[CodeIssue]:
        """Analyze a single file for issues"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST for syntax analysis
            try:
                tree = ast.parse(content)
                issues.extend(self._analyze_ast(file_path, tree, content))
            except SyntaxError as e:
                issues.append(CodeIssue(
                    file_path=file_path,
                    line_number=e.lineno or 0,
                    issue_type='bug',
                    severity='critical',
                    description=f"Syntax error: {e.msg}",
                    suggested_fix="Fix syntax error",
                    confidence=1.0,
                    detected_at=datetime.now()
                ))
            
            # Pattern-based analysis
            issues.extend(self._analyze_patterns(file_path, content))
            
            # Performance analysis
            issues.extend(self._analyze_performance(file_path, content))
            
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
        
        return issues
    
    def _analyze_ast(self, file_path: str, tree: ast.AST, content: str) -> List[CodeIssue]:
        """Analyze AST for code issues"""
        issues = []
        lines = content.split('\n')
        
        class IssueVisitor(ast.NodeVisitor):
            def visit_Try(self, node):
                # Check for empty except blocks
                for handler in node.handlers:
                    if not handler.body or (len(handler.body) == 1 and 
                                          isinstance(handler.body[0], ast.Pass)):
                        issues.append(CodeIssue(
                            file_path=file_path,
                            line_number=handler.lineno,
                            issue_type='bug',
                            severity='medium',
                            description="Empty except block - should handle or log exception",
                            suggested_fix="Add proper exception handling or logging",
                            confidence=0.9,
                            detected_at=datetime.now()
                        ))
                self.generic_visit(node)
            
            def visit_FunctionDef(self, node):
                # Check for functions without docstrings
                if not ast.get_docstring(node) and not node.name.startswith('_'):
                    issues.append(CodeIssue(
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type='improvement',
                        severity='low',
                        description=f"Function '{node.name}' lacks documentation",
                        suggested_fix="Add docstring explaining function purpose and parameters",
                        confidence=0.8,
                        detected_at=datetime.now()
                    ))
                
                # Check for long functions (>50 lines)
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    func_length = node.end_lineno - node.lineno
                    if func_length > 50:
                        issues.append(CodeIssue(
                            file_path=file_path,
                            line_number=node.lineno,
                            issue_type='inefficiency',
                            severity='medium',
                            description=f"Function '{node.name}' is too long ({func_length} lines)",
                            suggested_fix="Consider breaking into smaller functions",
                            confidence=0.7,
                            detected_at=datetime.now()
                        ))
                
                self.generic_visit(node)
        
        visitor = IssueVisitor()
        visitor.visit(tree)
        
        return issues
    
    def _analyze_patterns(self, file_path: str, content: str) -> List[CodeIssue]:
        """Analyze code patterns for common issues"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Check for print statements (should use logging)
            if line_stripped.startswith('print(') and 'debug' not in line_stripped.lower():
                issues.append(CodeIssue(
                    file_path=file_path,
                    line_number=i,
                    issue_type='improvement',
                    severity='low',
                    description="Using print() instead of proper logging",
                    suggested_fix="Replace with logging.info() or appropriate log level",
                    confidence=0.8,
                    detected_at=datetime.now()
                ))
            
            # Check for TODO/FIXME comments
            if 'TODO' in line_stripped or 'FIXME' in line_stripped:
                issues.append(CodeIssue(
                    file_path=file_path,
                    line_number=i,
                    issue_type='improvement',
                    severity='low',
                    description="Unresolved TODO/FIXME comment",
                    suggested_fix="Address the TODO/FIXME item",
                    confidence=0.6,
                    detected_at=datetime.now()
                ))
            
            # Check for hardcoded values
            if any(pattern in line_stripped for pattern in ['localhost', '127.0.0.1', 'C:\\', '/tmp/']):
                issues.append(CodeIssue(
                    file_path=file_path,
                    line_number=i,
                    issue_type='improvement',
                    severity='medium',
                    description="Hardcoded path or URL detected",
                    suggested_fix="Move to configuration file or environment variable",
                    confidence=0.7,
                    detected_at=datetime.now()
                ))
        
        return issues
    
    def _analyze_performance(self, file_path: str, content: str) -> List[CodeIssue]:
        """Analyze code for performance issues"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Check for inefficient string concatenation in loops
            if '+=' in line_stripped and 'str' in line_stripped:
                # Look for surrounding loop context
                context_start = max(0, i-5)
                context_lines = lines[context_start:i+2]
                context = '\n'.join(context_lines)
                
                if any(keyword in context for keyword in ['for ', 'while ']):
                    issues.append(CodeIssue(
                        file_path=file_path,
                        line_number=i,
                        issue_type='inefficiency',
                        severity='medium',
                        description="Inefficient string concatenation in loop",
                        suggested_fix="Use list.append() and ''.join() or f-strings",
                        confidence=0.8,
                        detected_at=datetime.now()
                    ))
        
        return issues

    def _process_detected_issues(self, issues: List[CodeIssue]):
        """Process newly detected issues and create improvement proposals"""
        critical_issues = [issue for issue in issues if issue.severity == 'critical']
        high_priority_issues = [issue for issue in issues if issue.severity == 'high']

        # Handle critical issues immediately
        if critical_issues:
            print(f"🚨 Processing {len(critical_issues)} critical issues immediately")
            for issue in critical_issues:
                self._create_and_test_improvement(issue)

        # Handle high priority issues
        if high_priority_issues:
            print(f"⚡ Processing {len(high_priority_issues)} high priority issues")
            for issue in high_priority_issues:
                self._create_and_test_improvement(issue)

        # Queue other issues for batch processing
        other_issues = [issue for issue in issues if issue.severity in ['medium', 'low']]
        if other_issues:
            print(f"📋 Queued {len(other_issues)} issues for batch processing")

    def _create_and_test_improvement(self, issue: CodeIssue) -> Optional[CodeImprovement]:
        """Create and test an improvement for a detected issue"""
        try:
            print(f"🔧 Creating improvement for {issue.issue_type} in {issue.file_path}:{issue.line_number}")

            # Read current file content
            with open(issue.file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            # Generate improved code
            improved_code = self._generate_improved_code(issue, original_content)

            if not improved_code:
                print(f"⚠️ Could not generate improvement for issue in {issue.file_path}")
                return None

            # Create improvement object
            improvement = CodeImprovement(
                issue=issue,
                original_code=original_content,
                improved_code=improved_code,
                test_results={},
                safety_score=0.0,
                performance_impact="unknown",
                implementation_ready=False
            )

            # Test the improvement
            if self._test_improvement(improvement):
                print(f"✅ Improvement tested successfully for {issue.file_path}")

                # Auto-apply if confidence is high enough
                if issue.confidence >= self.auto_fix_threshold:
                    self._apply_improvement(improvement)
                else:
                    self.pending_improvements.append(improvement)
                    print(f"📋 Improvement queued for manual review (confidence: {issue.confidence:.1%})")

            return improvement

        except Exception as e:
            print(f"❌ Error creating improvement for {issue.file_path}: {e}")
            return None

    def _generate_improved_code(self, issue: CodeIssue, original_code: str) -> Optional[str]:
        """Generate improved code for the detected issue"""
        try:
            lines = original_code.split('\n')

            if issue.issue_type == 'bug' and 'empty except block' in issue.description.lower():
                # Fix empty except blocks
                line_index = issue.line_number - 1
                if line_index < len(lines):
                    # Find the except block and add proper handling
                    for i in range(line_index, min(len(lines), line_index + 5)):
                        if 'pass' in lines[i] and 'except' in lines[max(0, i-2):i+1]:
                            lines[i] = lines[i].replace('pass', 'print(f"⚠️ Exception in {issue.file_path}: {e}")')
                            break

            elif issue.issue_type == 'improvement' and 'print(' in issue.description:
                # Replace print with logging
                line_index = issue.line_number - 1
                if line_index < len(lines) and 'print(' in lines[line_index]:
                    # Add logging import if not present
                    if 'import logging' not in original_code:
                        lines.insert(0, 'import logging')

                    # Replace print with logging
                    old_line = lines[line_index]
                    new_line = old_line.replace('print(', 'logging.info(')
                    lines[line_index] = new_line

            elif issue.issue_type == 'improvement' and 'docstring' in issue.description.lower():
                # Add docstring to function
                line_index = issue.line_number - 1
                if line_index < len(lines) and 'def ' in lines[line_index]:
                    func_name = lines[line_index].split('def ')[1].split('(')[0]
                    docstring = f'    """Enhanced {func_name} with self-healing improvements"""'
                    lines.insert(line_index + 1, docstring)

            elif issue.issue_type == 'improvement' and 'hardcoded' in issue.description.lower():
                # Move hardcoded values to configuration
                line_index = issue.line_number - 1
                if line_index < len(lines):
                    line = lines[line_index]
                    # Add comment about configuration
                    lines[line_index] = line + '  # TODO: Move to configuration'

            return '\n'.join(lines)

        except Exception as e:
            print(f"❌ Error generating improved code: {e}")
            return None

    def _test_improvement(self, improvement: CodeImprovement) -> bool:
        """Test the proposed improvement safely"""
        try:
            print(f"🧪 Testing improvement for {improvement.issue.file_path}")

            # Create temporary file for testing
            temp_file = f"{improvement.issue.file_path}.temp_test"

            try:
                # Write improved code to temp file
                with open(temp_file, 'w', encoding='utf-8') as f:
                    f.write(improvement.improved_code)

                # Test 1: Syntax check
                try:
                    ast.parse(improvement.improved_code)
                    improvement.test_results['syntax_check'] = 'passed'
                except SyntaxError as e:
                    improvement.test_results['syntax_check'] = f'failed: {e}'
                    return False

                # Test 2: Import check (if it's a Python module)
                if improvement.issue.file_path.endswith('.py'):
                    try:
                        # Try to compile the code
                        compile(improvement.improved_code, improvement.issue.file_path, 'exec')
                        improvement.test_results['compile_check'] = 'passed'
                    except Exception as e:
                        improvement.test_results['compile_check'] = f'failed: {e}'
                        return False

                # Test 3: Safety analysis
                safety_score = self._analyze_safety(improvement.improved_code)
                improvement.safety_score = safety_score

                if safety_score < 0.7:
                    improvement.test_results['safety_check'] = f'failed: low safety score {safety_score:.2f}'
                    return False

                improvement.test_results['safety_check'] = f'passed: safety score {safety_score:.2f}'
                improvement.implementation_ready = True

                print(f"✅ All tests passed for {improvement.issue.file_path}")
                return True

            finally:
                # Clean up temp file
                if os.path.exists(temp_file):
                    os.remove(temp_file)

        except Exception as e:
            print(f"❌ Error testing improvement: {e}")
            improvement.test_results['error'] = str(e)
            return False

    def _analyze_safety(self, code: str) -> float:
        """Analyze the safety of the improved code"""
        safety_score = 1.0

        # Check for dangerous operations
        dangerous_patterns = [
            'os.system(',
            'subprocess.call(',
            'exec(',
            'eval(',
            '__import__',
            'open(',
            'file(',
            'input(',
            'raw_input('
        ]

        for pattern in dangerous_patterns:
            if pattern in code:
                safety_score -= 0.2
                print(f"⚠️ Detected potentially dangerous operation: {pattern}")

        # Check for good practices
        good_patterns = [
            'try:',
            'except',
            'logging.',
            'if __name__',
            'def ',
            'class ',
            '"""'
        ]

        good_count = sum(1 for pattern in good_patterns if pattern in code)
        safety_score += min(0.3, good_count * 0.05)

        return max(0.0, min(1.0, safety_score))

    def _apply_improvement(self, improvement: CodeImprovement):
        """Apply the tested improvement to the actual file"""
        try:
            print(f"🚀 Applying improvement to {improvement.issue.file_path}")

            # Create backup
            backup_path = f"{improvement.issue.file_path}.backup_{int(time.time())}"
            with open(improvement.issue.file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)

            # Apply improvement
            with open(improvement.issue.file_path, 'w', encoding='utf-8') as f:
                f.write(improvement.improved_code)

            # Record the fix
            fix_record = {
                'file_path': improvement.issue.file_path,
                'issue_type': improvement.issue.issue_type,
                'severity': improvement.issue.severity,
                'description': improvement.issue.description,
                'applied_at': datetime.now().isoformat(),
                'backup_path': backup_path,
                'safety_score': improvement.safety_score,
                'confidence': improvement.issue.confidence
            }

            self.applied_fixes.append(fix_record)

            print(f"✅ Successfully applied improvement to {improvement.issue.file_path}")
            print(f"💾 Backup created: {backup_path}")

        except Exception as e:
            print(f"❌ Error applying improvement: {e}")

    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of JARVIS code"""
        return {
            'monitoring_active': self.monitoring_active,
            'healing_active': self.healing_active,
            'monitored_files': len(self.monitored_files),
            'detected_issues': len(self.detected_issues),
            'pending_improvements': len(self.pending_improvements),
            'applied_fixes': len(self.applied_fixes),
            'last_scan': datetime.now().isoformat(),
            'auto_fix_threshold': self.auto_fix_threshold,
            'critical_issues': len([i for i in self.detected_issues if i.severity == 'critical']),
            'high_priority_issues': len([i for i in self.detected_issues if i.severity == 'high'])
        }

    def force_health_check(self):
        """Force an immediate health check"""
        print("🔥 Forcing immediate self-healing health check...")
        self._perform_health_check()

    def toggle_monitoring(self, enabled: bool):
        """Enable or disable monitoring"""
        self.monitoring_active = enabled
        status = "enabled" if enabled else "disabled"
        print(f"🔄 Self-healing monitoring {status}")

    def toggle_healing(self, enabled: bool):
        """Enable or disable automatic healing"""
        self.healing_active = enabled
        status = "enabled" if enabled else "disabled"
        print(f"🔥 Self-healing auto-fix {status}")

    def set_auto_fix_threshold(self, threshold: float):
        """Set the confidence threshold for automatic fixes"""
        self.auto_fix_threshold = max(0.0, min(1.0, threshold))
        print(f"⚡ Auto-fix threshold set to {self.auto_fix_threshold:.0%}")

    def get_recent_issues(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent issues detected"""
        recent_issues = sorted(self.detected_issues, key=lambda x: x.detected_at, reverse=True)[:limit]
        return [
            {
                'file_path': issue.file_path,
                'line_number': issue.line_number,
                'issue_type': issue.issue_type,
                'severity': issue.severity,
                'description': issue.description,
                'confidence': issue.confidence,
                'detected_at': issue.detected_at.isoformat()
            }
            for issue in recent_issues
        ]

    def get_applied_fixes(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recently applied fixes"""
        return self.applied_fixes[-limit:] if self.applied_fixes else []
