#!/usr/bin/env python3
"""
<PERSON> V6 - AI Assistant
Merging the best features of <PERSON> (Iron Man), <PERSON><PERSON> (Alien Best Friend), and <PERSON><PERSON>
"""

import sys
import asyncio
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QThread, QObject, pyqtSignal
from src.gui.main_window import <PERSON><PERSON><PERSON><PERSON><PERSON>ow
from src.core.config import Config

def main():
    """Main entry point for the Jarvis V6 AI Assistant"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Jarvis V6")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Jarvis AI")
    
    # Create and show main window
    window = JarvisMainWindow()
    window.show()
    
    # Start the application event loop
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
