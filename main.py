#!/usr/bin/env python3
"""
<PERSON> V6 - AI Assistant
Merging the best features of <PERSON> (Iron Man), <PERSON><PERSON> (Alien Best Friend), and <PERSON><PERSON>
"""

import sys
from src.gui.qt_compat import QApplication
from src.gui.main_window import <PERSON><PERSON><PERSON><PERSON><PERSON>ow

def main():
    """Main entry point for the Jarvis V6 AI Assistant"""
    print("🚀 Starting JARVIS V6 AI Assistant...")

    try:
        app = QApplication(sys.argv)

        # Set application properties
        app.setApplicationName("Jarvis V6")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("Jarvis AI")

        print("✅ Application initialized")

        # Create and show main window
        window = JarvisMainWindow()
        window.show()

        print("✅ JARVIS V6 interface ready!")
        print("🎉 Enjoy your AI assistant!")

        # Start the application event loop
        sys.exit(app.exec())

    except Exception as e:
        print(f"❌ Error starting JARVIS V6: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
