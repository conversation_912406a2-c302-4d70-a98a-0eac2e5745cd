# 🏠 JARVIS V6 Smart Home Control Guide

JARVIS V6 now includes comprehensive smart home integration that allows you to control your devices using natural language commands!

## ✨ Features

- **Natural Language Control**: Talk to JARVIS like you would a human
- **Multiple Platform Support**: <PERSON>, TP-<PERSON>, and Demo Mode
- **Room-Based Control**: Control all devices in a room at once
- **Voice Feedback**: JARVIS speaks responses when TTS is enabled
- **Real-time Status**: Check device status and system health
- **Easy Configuration**: JSON-based setup with examples

## 🚀 Quick Start

### 1. Enable Smart Home System
1. Launch JARVIS V6
2. Click the **🏠 SMART HOME** button in the AI Systems panel
3. JARVIS will initialize the smart home system
4. Demo mode is enabled by default for testing

### 2. Try These Commands
```
"Turn on the living room light"
"Switch off bedroom light"
"Lights on in kitchen"
"Check smart home status"
"Turn off all lights in living room"
```

## 🗣️ Voice Commands

### Device Control
- **Turn On**: "Turn on the [device]", "Switch on [device]", "[Device] on"
- **Turn Off**: "Turn off the [device]", "Switch off [device]", "[Device] off"
- **Brightness**: "Set [device] to 75%", "Dim [device] to 25%"
- **Temperature**: "Set temperature to 72 degrees", "Thermostat 68 degrees"

### Room Control
- **All Lights**: "Turn on all lights in [room]", "Lights off in [room]"
- **Room Control**: "Turn on the [room] room", "[Room] lights on"

### Status & Information
- **Device Status**: "Status of [device]", "How is the [device]"
- **System Status**: "Smart home status", "Check all devices"

## 🏠 Supported Devices

### Demo Mode (Default)
- Demo Living Room Light
- Demo Bedroom Light  
- Demo Kitchen Light
- Demo Office Light
- Demo Thermostat

### Real Platforms
- **Philips Hue**: Smart lights with brightness and color control
- **TP-Link Kasa**: Smart switches, plugs, and lights

## ⚙️ Configuration

### Smart Home Config File: `smart_home_config.json`

```json
{
  "platforms": {
    "demo_mode": {
      "enabled": true,
      "description": "Demo mode for testing"
    },
    "philips_hue": {
      "enabled": false,
      "bridge_ip": "*************",
      "username": "your_hue_username"
    },
    "tplink_kasa": {
      "enabled": false,
      "devices": [
        {
          "name": "Living Room Light",
          "ip": "*************",
          "type": "light",
          "room": "Living Room"
        }
      ]
    }
  }
}
```

### Setting Up Real Devices

#### Philips Hue Setup
1. Find your Hue Bridge IP address
2. Press the bridge button
3. Get username using Hue API
4. Update `smart_home_config.json`:
   ```json
   "philips_hue": {
     "enabled": true,
     "bridge_ip": "YOUR_BRIDGE_IP",
     "username": "YOUR_USERNAME"
   }
   ```

#### TP-Link Kasa Setup
1. Find device IP addresses on your network
2. Update `smart_home_config.json`:
   ```json
   "tplink_kasa": {
     "enabled": true,
     "devices": [
       {
         "name": "Living Room Light",
         "ip": "*************",
         "type": "light",
         "room": "Living Room"
       }
     ]
   }
   ```

## 🎯 Example Commands

### Basic Device Control
```
✅ "Turn on the living room light"
✅ "Switch off bedroom light"
✅ "Enable the kitchen light"
✅ "Deactivate office light"
```

### Brightness Control
```
✅ "Set living room light to 50%"
✅ "Dim bedroom light to 25%"
✅ "Brighten kitchen light to 100%"
✅ "Make office light 75% bright"
```

### Room Control
```
✅ "Turn on all lights in living room"
✅ "Lights off in bedroom"
✅ "Switch on the kitchen room"
✅ "Turn off everything in office"
```

### Status Checking
```
✅ "Check smart home status"
✅ "What's the status of living room light"
✅ "How is the thermostat"
✅ "Show me device status"
```

### Temperature Control
```
✅ "Set temperature to 72 degrees"
✅ "Thermostat 68 degrees"
✅ "Make it 70 degrees in living room"
```

## 🔧 Troubleshooting

### Smart Home System Won't Initialize
- Check `smart_home_config.json` exists and is valid JSON
- Ensure at least one platform is enabled
- Demo mode should work out of the box

### Commands Not Recognized
- Use natural language - JARVIS understands variations
- Include device names as configured
- Try different phrasings: "turn on", "switch on", "enable"

### Real Devices Not Responding
- **Philips Hue**: Check bridge IP and username
- **TP-Link Kasa**: Verify device IP addresses
- **Network**: Ensure devices are on same network as JARVIS

### Device Not Found
- Check device names in configuration
- Use partial names - "living room" matches "Demo Living Room Light"
- Try without "demo" prefix for demo devices

## 🎉 Advanced Features

### Voice Integration
- Enable TTS for spoken responses
- JARVIS will confirm actions: "✅ Turned on living room light"
- Error messages are also spoken

### Room Aliases
- "Living room" = "lounge", "family room", "main room"
- "Bedroom" = "bed room", "master bedroom"
- "Kitchen" = "cooking area", "dining room"
- "Office" = "study", "work room", "den"

### Device Aliases
- "Light" = "lamp", "ceiling light", "main light"
- "Thermostat" = "temperature", "heating", "cooling"
- "Fan" = "ceiling fan", "air circulation"

## 📊 System Status

The smart home status shows:
- **Total Devices**: Number of configured devices
- **Platforms**: Number of active platforms
- **Rooms**: Number of configured rooms
- **Device States**: Current on/off status per room

## 🔮 Future Enhancements

Coming soon:
- **Scenes**: "Movie mode", "Bedtime", "Party lights"
- **Schedules**: "Turn on lights at sunset"
- **Sensors**: Motion detection, temperature monitoring
- **More Platforms**: SmartThings, Home Assistant, Alexa
- **Advanced Controls**: Color changing, dimming schedules

---

**Enjoy controlling your smart home with JARVIS V6! 🏠✨**
