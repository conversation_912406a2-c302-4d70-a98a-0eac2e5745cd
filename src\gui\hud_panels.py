"""
HUD Panels for JARVIS Interface
System information, status displays, and data widgets
"""

import psutil
from datetime import datetime
from src.gui.qt_compat import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar,
                               QFrame, QTimer, Qt, QPropertyAnimation, QEasingCurve,
                               pyqtProperty, QFont, QPainter, QPen, QBrush, QColor, QLinearGradient)
from src.core.config import Config

class HUDPanel(QFrame):
    """Base class for HUD panels with glow effects"""
    
    def __init__(self, title: str, config: Config, parent=None):
        super().__init__(parent)
        self.config = config
        self.title = title
        self.glow_intensity = 0.5
        
        self.setFixedSize(280, 120)
        self.setup_ui()
        self.setup_styling()
        self.setup_glow_animation()
    
    def setup_ui(self):
        """Setup the panel UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(5)
        
        # Title
        self.title_label = QLabel(self.title)
        self.title_label.setObjectName("hudPanelTitle")
        layout.addWidget(self.title_label)
        
        # Content area
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.content_widget)
    
    def setup_styling(self):
        """Setup panel styling"""
        self.setStyleSheet(f"""
            HUDPanel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 50, 100, 0.8),
                    stop:1 rgba(0, 30, 60, 0.6));
                border: 2px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 10px;
            }}
            
            #hudPanelTitle {{
                color: {self.config.THEME_PRIMARY_COLOR};
                font-size: 12px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
        """)
    
    def setup_glow_animation(self):
        """Setup glow animation"""
        self.glow_animation = QPropertyAnimation(self, b"glow_intensity")
        self.glow_animation.setDuration(3000)
        self.glow_animation.setStartValue(0.3)
        self.glow_animation.setEndValue(1.0)
        self.glow_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        self.glow_animation.setLoopCount(-1)
        self.glow_animation.start()
    
    @pyqtProperty(float)
    def glow_intensity(self):
        return self._glow_intensity
    
    @glow_intensity.setter
    def glow_intensity(self, value):
        self._glow_intensity = value
        self.update_glow()
    
    def update_glow(self):
        """Update glow effect"""
        glow_color = f"rgba(0, 255, 255, {int(100 * self._glow_intensity)})"
        self.setStyleSheet(self.styleSheet() + f"""
            HUDPanel {{
                border: 2px solid {glow_color};
                box-shadow: 0 0 20px {glow_color};
            }}
        """)

class SystemInfoPanel(HUDPanel):
    """System information panel"""
    
    def __init__(self, config: Config, parent=None):
        super().__init__("SYSTEM STATUS", config, parent)
        self.setup_content()
        self.setup_update_timer()
    
    def setup_content(self):
        """Setup system info content"""
        # CPU
        cpu_layout = QHBoxLayout()
        cpu_layout.addWidget(QLabel("CPU:"))
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setObjectName("hudProgressBar")
        self.cpu_bar.setFixedHeight(8)
        cpu_layout.addWidget(self.cpu_bar)
        self.cpu_label = QLabel("0%")
        self.cpu_label.setObjectName("hudValue")
        self.cpu_label.setFixedWidth(35)
        cpu_layout.addWidget(self.cpu_label)
        self.content_layout.addLayout(cpu_layout)
        
        # Memory
        mem_layout = QHBoxLayout()
        mem_layout.addWidget(QLabel("RAM:"))
        self.mem_bar = QProgressBar()
        self.mem_bar.setObjectName("hudProgressBar")
        self.mem_bar.setFixedHeight(8)
        mem_layout.addWidget(self.mem_bar)
        self.mem_label = QLabel("0%")
        self.mem_label.setObjectName("hudValue")
        self.mem_label.setFixedWidth(35)
        mem_layout.addWidget(self.mem_label)
        self.content_layout.addLayout(mem_layout)
        
        # Disk
        disk_layout = QHBoxLayout()
        disk_layout.addWidget(QLabel("DISK:"))
        self.disk_bar = QProgressBar()
        self.disk_bar.setObjectName("hudProgressBar")
        self.disk_bar.setFixedHeight(8)
        disk_layout.addWidget(self.disk_bar)
        self.disk_label = QLabel("0%")
        self.disk_label.setObjectName("hudValue")
        self.disk_label.setFixedWidth(35)
        disk_layout.addWidget(self.disk_label)
        self.content_layout.addLayout(disk_layout)
        
        # Apply styling
        self.setStyleSheet(self.styleSheet() + f"""
            QLabel {{
                color: {self.config.THEME_TEXT_COLOR};
                font-size: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
            }}
            
            #hudProgressBar {{
                border: 1px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 3px;
                background-color: rgba(0, 0, 0, 0.5);
                text-align: center;
            }}
            
            #hudProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.config.THEME_PRIMARY_COLOR},
                    stop:1 {self.config.THEME_SECONDARY_COLOR});
                border-radius: 2px;
            }}
            
            #hudValue {{
                color: {self.config.THEME_ACCENT_COLOR};
                font-weight: bold;
            }}
        """)
    
    def setup_update_timer(self):
        """Setup timer to update system info"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_system_info)
        self.update_timer.start(2000)  # Update every 2 seconds
        
        # Initial update
        self.update_system_info()
    
    def update_system_info(self):
        """Update system information"""
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=None)
            self.cpu_bar.setValue(int(cpu_percent))
            self.cpu_label.setText(f"{cpu_percent:.0f}%")
            
            # Memory
            memory = psutil.virtual_memory()
            self.mem_bar.setValue(int(memory.percent))
            self.mem_label.setText(f"{memory.percent:.0f}%")
            
            # Disk
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.disk_bar.setValue(int(disk_percent))
            self.disk_label.setText(f"{disk_percent:.0f}%")
            
        except Exception as e:
            print(f"Error updating system info: {e}")

class TimePanel(HUDPanel):
    """Time and date panel"""
    
    def __init__(self, config: Config, parent=None):
        super().__init__("TEMPORAL STATUS", config, parent)
        self.setup_content()
        self.setup_update_timer()
    
    def setup_content(self):
        """Setup time content"""
        self.time_label = QLabel()
        self.time_label.setObjectName("hudTimeLabel")
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.content_layout.addWidget(self.time_label)
        
        self.date_label = QLabel()
        self.date_label.setObjectName("hudDateLabel")
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.content_layout.addWidget(self.date_label)
        
        # Apply styling
        self.setStyleSheet(self.styleSheet() + f"""
            #hudTimeLabel {{
                color: {self.config.THEME_PRIMARY_COLOR};
                font-size: 24px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
            }}
            
            #hudDateLabel {{
                color: {self.config.THEME_TEXT_COLOR};
                font-size: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
        """)
    
    def setup_update_timer(self):
        """Setup timer to update time"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_time)
        self.update_timer.start(1000)  # Update every second
        
        # Initial update
        self.update_time()
    
    def update_time(self):
        """Update time display"""
        now = datetime.now()
        self.time_label.setText(now.strftime("%H:%M:%S"))
        self.date_label.setText(now.strftime("%A, %B %d, %Y"))

class StatusPanel(HUDPanel):
    """AI status and connection panel"""
    
    def __init__(self, config: Config, parent=None):
        super().__init__("AI STATUS", config, parent)
        self.setup_content()
    
    def setup_content(self):
        """Setup status content"""
        # AI Status
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("AI CORE:"))
        self.ai_status = QLabel("ONLINE")
        self.ai_status.setObjectName("hudStatusOnline")
        status_layout.addWidget(self.ai_status)
        status_layout.addStretch()
        self.content_layout.addLayout(status_layout)
        
        # TTS Status
        tts_layout = QHBoxLayout()
        tts_layout.addWidget(QLabel("VOICE:"))
        self.tts_status = QLabel("READY")
        self.tts_status.setObjectName("hudStatusReady")
        tts_layout.addWidget(self.tts_status)
        tts_layout.addStretch()
        self.content_layout.addLayout(tts_layout)
        
        # Model Info
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("MODEL:"))
        self.model_label = QLabel("MIXTRAL-8X7B")
        self.model_label.setObjectName("hudModelLabel")
        model_layout.addWidget(self.model_label)
        model_layout.addStretch()
        self.content_layout.addLayout(model_layout)
        
        # Apply styling
        self.setStyleSheet(self.styleSheet() + f"""
            QLabel {{
                color: {self.config.THEME_TEXT_COLOR};
                font-size: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
            }}
            
            #hudStatusOnline {{
                color: #00FF00;
                font-weight: bold;
            }}
            
            #hudStatusReady {{
                color: {self.config.THEME_PRIMARY_COLOR};
                font-weight: bold;
            }}
            
            #hudModelLabel {{
                color: {self.config.THEME_ACCENT_COLOR};
                font-weight: bold;
            }}
        """)
    
    def update_ai_status(self, status: str, color: str = None):
        """Update AI status"""
        self.ai_status.setText(status)
        if color:
            self.ai_status.setStyleSheet(f"color: {color}; font-weight: bold;")
    
    def update_tts_status(self, status: str, color: str = None):
        """Update TTS status"""
        self.tts_status.setText(status)
        if color:
            self.tts_status.setStyleSheet(f"color: {color}; font-weight: bold;")
