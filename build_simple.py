#!/usr/bin/env python3
"""
Simple JARVIS V6 Executable Builder
===================================

Quick and easy .exe builder for JARVIS V6
"""

import os
import sys
import subprocess

def main():
    print("🚀 Building JARVIS V6 Executable...")
    print("=" * 40)
    
    # Install PyInstaller if needed
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("📦 Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed")
    
    # Simple build command
    print("🔨 Building executable...")

    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # Single file
        "--windowed",                   # No console
        "--name=JARVIS_V6",            # Executable name
        "--add-data=src;src",          # Include src folder
        "--exclude-module=PyQt6",      # Exclude PyQt6 to avoid conflicts
        "--exclude-module=PyQt5",      # Exclude PyQt5 to avoid conflicts
        "--hidden-import=PySide6.QtCore",
        "--hidden-import=PySide6.QtGui",
        "--hidden-import=PySide6.QtWidgets",
        "--hidden-import=pygame",
        "--hidden-import=requests",
        "--hidden-import=sqlite3",
        "--hidden-import=json",
        "--hidden-import=threading",
        "--hidden-import=datetime",
        "--hidden-import=pathlib",
        "--hidden-import=typing",
        "--hidden-import=dataclasses",
        "jarvis_clean.py"
    ]

    # Add .env file if it exists
    if os.path.exists('.env'):
        cmd.insert(-1, "--add-data=.env;.")
        print("✅ Including .env file")
    else:
        print("ℹ️ No .env file found - will use defaults")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ Build successful!")
        print("📦 Executable created: dist/JARVIS_V6.exe")
        print("\n🎉 You can now run JARVIS by double-clicking JARVIS_V6.exe!")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
