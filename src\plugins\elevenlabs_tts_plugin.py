"""
ElevenLabs TTS Plugin for Jarvis V6
Provides text-to-speech using ElevenLabs API
"""

import os
import io
import tempfile
import threading
from typing import Optional, Dict, Any
import requests
import pygame
from src.gui.qt_compat import QObject, pyqtSignal, QThread
from src.plugins.plugin_manager import VoicePlugin
from src.core.config import Config

class ElevenLabsTTSWorker(QThread):
    """Worker thread for TTS processing"""

    speech_ready = pyqtSignal(bytes)  # Audio data
    error_occurred = pyqtSignal(str)
    generation_started = pyqtSignal()  # Signal when generation actually starts

    def __init__(self, text: str, api_key: str, voice_id: str, voice_settings: Dict[str, Any]):
        super().__init__()
        self.text = text
        self.api_key = api_key
        self.voice_id = voice_id
        self.voice_settings = voice_settings

    def run(self):
        """Generate speech in background thread"""
        try:
            # Signal that generation is starting
            self.generation_started.emit()

            # ElevenLabs API endpoint
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{self.voice_id}"

            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": self.api_key
            }

            # Use faster model for quicker generation
            data = {
                "text": self.text,
                "model_id": "eleven_turbo_v2",  # Faster model
                "voice_settings": self.voice_settings
            }

            response = requests.post(url, json=data, headers=headers, timeout=15)  # Reduced timeout

            if response.status_code == 200:
                self.speech_ready.emit(response.content)
            else:
                error_msg = f"ElevenLabs API error: {response.status_code}"
                if response.text:
                    error_msg += f" - {response.text}"
                self.error_occurred.emit(error_msg)

        except Exception as e:
            self.error_occurred.emit(f"TTS generation failed: {str(e)}")

class TTSSignalHandler(QObject):
    """Signal handler for TTS events"""
    speech_started = pyqtSignal()
    speech_finished = pyqtSignal()
    speech_error = pyqtSignal(str)

class ElevenLabsTTSPlugin(VoicePlugin):
    """TTS plugin using ElevenLabs API"""

    def __init__(self):
        super().__init__()
        self.config = None
        self.api_key = None
        self.voice_id = None
        self.signals = TTSSignalHandler()
        self.voice_settings = {
            "stability": 0.5,  # Lower for faster generation
            "similarity_boost": 0.8,  # Keep voice quality
            "style": 0.0,
            "use_speaker_boost": True
        }
        self.is_speaking = False
        self.current_worker = None
        
        # Initialize pygame mixer for audio playback
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            self.audio_initialized = True
        except Exception as e:
            print(f"Failed to initialize audio: {e}")
            self.audio_initialized = False
            
    @property
    def name(self) -> str:
        return "ElevenLabs TTS"
        
    @property
    def version(self) -> str:
        return "1.0.0"
        
    @property
    def description(self) -> str:
        return "Text-to-speech using ElevenLabs API with high-quality voice synthesis"
        
    def initialize(self, config: Config) -> bool:
        """Initialize the plugin"""
        try:
            self.config = config
            self.api_key = config.ELEVENLABS_API_KEY
            self.voice_id = config.ELEVENLABS_VOICE_ID

            # Test API connection
            if self.test_api_connection():
                print(f"ElevenLabs TTS Plugin initialized successfully")
                return True
            else:
                print("Failed to connect to ElevenLabs API")
                return False
                
        except Exception as e:
            print(f"Failed to initialize ElevenLabs TTS Plugin: {e}")
            return False
            
    def cleanup(self) -> None:
        """Cleanup plugin resources"""
        self.stop_speaking()
        if self.current_worker and self.current_worker.isRunning():
            self.current_worker.terminate()
            self.current_worker.wait()
            
        if self.audio_initialized:
            pygame.mixer.quit()
            
    def test_api_connection(self) -> bool:
        """Test connection to ElevenLabs API"""
        try:
            url = "https://api.elevenlabs.io/v1/voices"
            headers = {"xi-api-key": self.api_key}
            
            response = requests.get(url, headers=headers, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            print(f"API connection test failed: {e}")
            return False
            
    def speak(self, text: str) -> None:
        """Convert text to speech and play it"""
        if not self.audio_initialized:
            self.speech_error.emit("Audio system not initialized")
            return

        if self.is_speaking:
            self.stop_speaking()

        # Clean up text for better speech
        text = self.prepare_text_for_speech(text)

        if not text.strip():
            return

        # Start TTS generation in background
        self.current_worker = ElevenLabsTTSWorker(
            text, self.api_key, self.voice_id, self.voice_settings
        )
        self.current_worker.speech_ready.connect(self.play_audio)
        self.current_worker.error_occurred.connect(self.handle_tts_error)
        self.current_worker.finished.connect(self.cleanup_worker)
        self.current_worker.generation_started.connect(self.on_generation_started)

        self.current_worker.start()
        # Don't emit speech_started here - wait for generation to actually start

    def on_generation_started(self):
        """Called when TTS generation actually starts"""
        # Just for logging/debugging - actual speech_started is emitted when audio plays
        pass
        
    def play_audio(self, audio_data: bytes) -> None:
        """Play audio data using pygame"""
        try:
            # Create temporary file for audio
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name

            # Load and play audio
            pygame.mixer.music.load(temp_file_path)
            pygame.mixer.music.play()

            # NOW emit the speaking signal when audio actually starts
            self.is_speaking = True
            self.signals.speech_started.emit()

            # Monitor playback in separate thread
            playback_thread = threading.Thread(target=self.monitor_playback, args=(temp_file_path,))
            playback_thread.daemon = True
            playback_thread.start()

        except Exception as e:
            self.signals.speech_error.emit(f"Audio playback failed: {str(e)}")
            
    def monitor_playback(self, temp_file_path: str) -> None:
        """Monitor audio playback completion"""
        try:
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                pygame.time.wait(100)
                
            self.is_speaking = False
            self.signals.speech_finished.emit()
            
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except Exception:
                pass  # Ignore cleanup errors
                
        except Exception as e:
            self.signals.speech_error.emit(f"Playback monitoring failed: {str(e)}")
            
    def stop_speaking(self) -> None:
        """Stop current speech"""
        if self.is_speaking:
            pygame.mixer.music.stop()
            self.is_speaking = False
            self.signals.speech_finished.emit()
            
    def handle_tts_error(self, error: str) -> None:
        """Handle TTS generation errors"""
        self.signals.speech_error.emit(error)
        
    def cleanup_worker(self) -> None:
        """Clean up worker thread"""
        if self.current_worker:
            self.current_worker.deleteLater()
            self.current_worker = None
            
    def prepare_text_for_speech(self, text: str) -> str:
        """Prepare text for better speech synthesis"""
        # Remove markdown formatting
        import re
        
        # Remove code blocks
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)
        text = re.sub(r'`.*?`', '', text)
        
        # Remove markdown links
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)
        
        # Remove markdown formatting
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)      # Italic
        text = re.sub(r'__(.*?)__', r'\1', text)      # Bold
        text = re.sub(r'_(.*?)_', r'\1', text)        # Italic
        
        # Remove headers
        text = re.sub(r'^#+\s*', '', text, flags=re.MULTILINE)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Limit length for API
        if len(text) > 2500:  # ElevenLabs has character limits
            text = text[:2500] + "..."
            
        return text
        
    def set_voice_settings(self, stability: float = None, similarity_boost: float = None, 
                          style: float = None, use_speaker_boost: bool = None) -> None:
        """Update voice settings"""
        if stability is not None:
            self.voice_settings["stability"] = max(0.0, min(1.0, stability))
        if similarity_boost is not None:
            self.voice_settings["similarity_boost"] = max(0.0, min(1.0, similarity_boost))
        if style is not None:
            self.voice_settings["style"] = max(0.0, min(1.0, style))
        if use_speaker_boost is not None:
            self.voice_settings["use_speaker_boost"] = use_speaker_boost
            
    def get_voice_settings(self) -> Dict[str, Any]:
        """Get current voice settings"""
        return self.voice_settings.copy()
        
    def is_currently_speaking(self) -> bool:
        """Check if currently speaking"""
        return self.is_speaking
        
    # Voice input methods (not implemented for TTS-only plugin)
    def start_listening(self) -> None:
        """Start voice input (not implemented)"""
        pass
        
    def stop_listening(self) -> None:
        """Stop voice input (not implemented)"""
        pass
