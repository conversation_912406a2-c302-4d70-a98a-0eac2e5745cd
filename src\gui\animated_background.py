"""
Animated Background Widget for Jarvis V6
Creates futuristic pulsing and shifting background effects
"""

from src.gui.qt_compat import (QWidget, QPropertyAnimation, QEasingCurve, QTimer,
                               pyqtProperty, QPainter, QBrush, QLinearGradient,
                               QColor, QRadialGradient)
from src.core.config import Config

class AnimatedBackground(QWidget):
    """Widget that provides animated background effects"""
    
    def __init__(self, config: Config, parent=None):
        super().__init__(parent)
        self.config = config
        self._opacity = 1.0
        self._pulse_phase = 0.0
        self._shift_phase = 0.0
        
        self.setup_animations()
        
    def setup_animations(self):
        """Setup the background animations"""
        # Pulse animation for opacity
        self.pulse_animation = QPropertyAnimation(self, b"opacity")
        self.pulse_animation.setDuration(self.config.ANIMATION_DURATION)
        self.pulse_animation.setStartValue(self.config.PULSE_MIN_OPACITY)
        self.pulse_animation.setEndValue(self.config.PULSE_MAX_OPACITY)
        self.pulse_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        self.pulse_animation.setLoopCount(-1)  # Infinite loop
        
        # Phase shift animation for color shifting
        self.shift_animation = QPropertyAnimation(self, b"shift_phase")
        self.shift_animation.setDuration(self.config.ANIMATION_DURATION * 2)
        self.shift_animation.setStartValue(0.0)
        self.shift_animation.setEndValue(360.0)
        self.shift_animation.setEasingCurve(QEasingCurve.Type.Linear)
        self.shift_animation.setLoopCount(-1)  # Infinite loop
        
        # Start animations
        self.pulse_animation.start()
        self.shift_animation.start()
        
        # Update timer for smooth repainting
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update)
        self.update_timer.start(50)  # 20 FPS
        
    @pyqtProperty(float)
    def opacity(self):
        """Get current opacity value"""
        return self._opacity
        
    @opacity.setter
    def opacity(self, value):
        """Set opacity value and trigger repaint"""
        self._opacity = value
        self.update()
        
    @pyqtProperty(float)
    def shift_phase(self):
        """Get current shift phase value"""
        return self._shift_phase
        
    @shift_phase.setter
    def shift_phase(self, value):
        """Set shift phase value and trigger repaint"""
        self._shift_phase = value
        self.update()
        
    def paintEvent(self, event):
        """Paint the animated background"""
        try:
            painter = QPainter(self)
            if not painter.isActive():
                return  # Prevent threading issues
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # Get widget dimensions
            rect = self.rect()
            width = rect.width()
            height = rect.height()

            # Create base gradient
            base_gradient = QLinearGradient(0, 0, width, height)
            base_gradient.setColorAt(0, QColor(self.config.THEME_BACKGROUND_COLOR))
            base_gradient.setColorAt(1, QColor(0, 20, 40, 100))  # Dark blue tint

            painter.fillRect(rect, QBrush(base_gradient))

            # Create pulsing radial gradients
            self.draw_pulsing_circles(painter, width, height)

            # Create shifting lines
            self.draw_shifting_lines(painter, width, height)

        except Exception as e:
            # Silently handle painting errors to prevent terminal spam
            pass

    def draw_pulsing_circles(self, painter, width, height):
        """Draw pulsing circular gradients"""
        # Calculate circle positions and sizes based on animation phase
        circles = [
            (width * 0.2, height * 0.3, 150),
            (width * 0.8, height * 0.7, 200),
            (width * 0.6, height * 0.2, 100),
            (width * 0.3, height * 0.8, 120),
        ]
        
        for i, (x, y, base_radius) in enumerate(circles):
            # Calculate animated radius and opacity
            phase_offset = i * 90  # Offset each circle's phase
            animated_radius = base_radius * (0.5 + 0.5 * self._opacity)
            
            # Create radial gradient
            gradient = QRadialGradient(x, y, animated_radius)
            
            # Color based on shift phase and circle index
            hue = (self._shift_phase + phase_offset) % 360
            color = QColor.fromHsv(int(hue), 100, 100, int(30 * self._opacity))
            
            gradient.setColorAt(0, color)
            gradient.setColorAt(1, QColor(0, 0, 0, 0))  # Transparent
            
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawEllipse(int(x - animated_radius), int(y - animated_radius),
                              int(animated_radius * 2), int(animated_radius * 2))
            
    def draw_shifting_lines(self, painter, width, height):
        """Draw shifting accent lines"""
        painter.setPen(Qt.PenStyle.NoPen)
        
        # Vertical accent lines
        line_count = 5
        for i in range(line_count):
            x = (width / line_count) * i + (self._shift_phase / 360.0) * (width / line_count)
            x = x % width
            
            # Create vertical gradient
            gradient = QLinearGradient(x, 0, x + 2, height)
            
            # Color based on shift phase
            hue = (self._shift_phase + i * 60) % 360
            color = QColor.fromHsv(int(hue), 150, 200, int(50 * self._opacity))
            
            gradient.setColorAt(0, QColor(0, 0, 0, 0))
            gradient.setColorAt(0.5, color)
            gradient.setColorAt(1, QColor(0, 0, 0, 0))
            
            painter.setBrush(QBrush(gradient))
            painter.drawRect(int(x), 0, 2, height)
            
        # Horizontal accent lines
        for i in range(3):
            y = (height / 3) * i + (self._shift_phase / 180.0) * (height / 6)
            y = y % height
            
            # Create horizontal gradient
            gradient = QLinearGradient(0, y, width, y + 1)
            
            # Color based on shift phase
            hue = (self._shift_phase + i * 120) % 360
            color = QColor.fromHsv(int(hue), 100, 150, int(30 * self._opacity))
            
            gradient.setColorAt(0, QColor(0, 0, 0, 0))
            gradient.setColorAt(0.5, color)
            gradient.setColorAt(1, QColor(0, 0, 0, 0))
            
            painter.setBrush(QBrush(gradient))
            painter.drawRect(0, int(y), width, 1)
            
    def start_animations(self):
        """Start all animations"""
        self.pulse_animation.start()
        self.shift_animation.start()
        self.update_timer.start()
        
    def stop_animations(self):
        """Stop all animations"""
        self.pulse_animation.stop()
        self.shift_animation.stop()
        self.update_timer.stop()
        
    def pause_animations(self):
        """Pause all animations"""
        self.pulse_animation.pause()
        self.shift_animation.pause()
        
    def resume_animations(self):
        """Resume all animations"""
        self.pulse_animation.resume()
        self.shift_animation.resume()
