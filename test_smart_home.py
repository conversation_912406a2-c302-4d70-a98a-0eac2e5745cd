#!/usr/bin/env python3
"""
Test Smart Home Integration for JARVIS V6
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from plugins.smart_home_plugin import SmartHomeManager
from ai.smart_home_commands import SmartHomeCommandProcessor

async def test_smart_home_system():
    """Test the smart home system"""
    print("🏠 JARVIS V6 Smart Home System Test")
    print("=" * 50)
    
    # Initialize smart home manager
    print("1. Initializing Smart Home Manager...")
    manager = SmartHomeManager()
    
    # Initialize the system
    print("2. Connecting to platforms...")
    success = await manager.initialize()
    
    if success:
        print("✅ Smart Home system initialized successfully")
        
        # Show device status
        status = manager.get_device_status()
        print(f"\n📊 System Status:")
        print(f"   Total Devices: {status['total_devices']}")
        print(f"   Platforms: {status['platforms']}")
        print(f"   Rooms: {status['rooms']}")
        
        print(f"\n🏠 Devices by Room:")
        for room, devices in status['devices_by_room'].items():
            if devices:
                print(f"   {room}:")
                for device in devices:
                    state_icon = "🟢" if device['state'] == "on" else "🔴"
                    print(f"     {state_icon} {device['name']} ({device['type']})")
        
        # Test command processor
        print(f"\n3. Testing Command Processor...")
        processor = SmartHomeCommandProcessor()
        
        test_commands = [
            "turn on the living room light",
            "switch off demo bedroom light",
            "lights on in kitchen",
            "check smart home status",
            "turn off all lights in living room"
        ]
        
        for command_text in test_commands:
            print(f"\n🗣️ Command: '{command_text}'")
            command = processor.parse_command(command_text)
            
            if command and command.confidence > 0.5:
                print(f"   ✅ Parsed: {command.command_type.value}")
                print(f"   🎯 Target: {command.target}")
                print(f"   📊 Confidence: {command.confidence:.2f}")
                
                # Execute the command
                if command.command_type.value == "turn_on":
                    result = await manager.turn_on_device(command.target)
                    print(f"   🔄 Result: {'Success' if result else 'Failed'}")
                
                elif command.command_type.value == "turn_off":
                    result = await manager.turn_off_device(command.target)
                    print(f"   🔄 Result: {'Success' if result else 'Failed'}")
                
                elif command.command_type.value == "room_control":
                    controlled = await manager.control_room(command.target, command.value)
                    print(f"   🔄 Result: Controlled {controlled} devices")
                
                elif command.command_type.value == "status":
                    print(f"   📊 Status request processed")
                
            else:
                print(f"   ❌ Could not parse command")
        
        # Show final status
        print(f"\n4. Final Device Status:")
        final_status = manager.get_device_status()
        for room, devices in final_status['devices_by_room'].items():
            if devices:
                print(f"   {room}:")
                for device in devices:
                    state_icon = "🟢" if device['state'] == "on" else "🔴"
                    print(f"     {state_icon} {device['name']}")
        
        print(f"\n🎉 Smart Home test completed successfully!")
        return True
        
    else:
        print("❌ Smart Home system initialization failed")
        print("💡 Make sure smart_home_config.json is configured properly")
        return False

def test_command_examples():
    """Show example commands"""
    print(f"\n📝 Example Smart Home Commands:")
    print("=" * 50)
    
    examples = [
        "Turn on the living room light",
        "Switch off bedroom light", 
        "Set kitchen light to 75%",
        "Dim the office light to 25%",
        "Turn on all lights in the living room",
        "Lights off in bedroom",
        "Set temperature to 72 degrees",
        "Check smart home status",
        "What's the status of the thermostat",
        "Turn off everything in the kitchen"
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i:2d}. {example}")
    
    print(f"\n💡 Tips:")
    print("   • Commands are case-insensitive")
    print("   • You can use 'the' or omit it")
    print("   • Room names and device names are flexible")
    print("   • Demo mode is enabled by default for testing")

async def main():
    """Main test function"""
    try:
        # Test the smart home system
        success = await test_smart_home_system()
        
        # Show command examples
        test_command_examples()
        
        if success:
            print(f"\n✅ All tests passed! Smart Home integration is ready.")
        else:
            print(f"\n❌ Some tests failed. Check configuration.")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
