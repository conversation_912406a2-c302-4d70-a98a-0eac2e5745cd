"""
Ollama API Client for Jarvis V6
Handles communication with local Mixtral 8x7B model
"""

import json
import asyncio
import aiohttp
import importlib.util
from typing import Dict, Any, Optional, AsyncGenerator
from PyQt6.QtCore import QObject, pyqtSignal, QThread
from src.core.config import Config

class OllamaClient(QObject):
    """Async client for Ollama API communication"""
    
    # Signals for GUI updates
    response_received = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    connection_status_changed = pyqtSignal(bool)
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        self.base_url = config.OLLAMA_BASE_URL
        self.model = config.OLLAMA_MODEL
        self.timeout = config.OLLAMA_TIMEOUT
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_connected = False
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
            
    async def check_connection(self) -> bool:
        """Check if Ollama server is running and accessible"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=5)
                )
                
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    self.is_connected = True
                    self.connection_status_changed.emit(True)
                    return True
                    
        except Exception as e:
            self.is_connected = False
            self.connection_status_changed.emit(False)
            self.error_occurred.emit(f"Connection failed: {str(e)}")
            
        return False
        
    async def generate_response(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate response from Mixtral model"""
        if not await self.check_connection():
            raise ConnectionError("Cannot connect to Ollama server")
            
        # Prepare the request payload
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "num_predict": 2048
            }
        }
        
        if system_prompt:
            payload["system"] = system_prompt
            
        try:
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    response_text = result.get("response", "")
                    self.response_received.emit(response_text)
                    return response_text
                else:
                    error_msg = f"API Error: {response.status}"
                    self.error_occurred.emit(error_msg)
                    raise Exception(error_msg)
                    
        except Exception as e:
            error_msg = f"Request failed: {str(e)}"
            self.error_occurred.emit(error_msg)
            raise
            
    async def generate_streaming_response(self, prompt: str, system_prompt: Optional[str] = None) -> AsyncGenerator[str, None]:
        """Generate streaming response from Mixtral model"""
        if not await self.check_connection():
            raise ConnectionError("Cannot connect to Ollama server")
            
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": True,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "num_predict": 2048
            }
        }
        
        if system_prompt:
            payload["system"] = system_prompt
            
        try:
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    async for line in response.content:
                        if line:
                            try:
                                data = json.loads(line.decode('utf-8'))
                                if 'response' in data:
                                    chunk = data['response']
                                    if chunk:
                                        yield chunk
                                        
                                if data.get('done', False):
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                else:
                    error_msg = f"Streaming API Error: {response.status}"
                    self.error_occurred.emit(error_msg)
                    raise Exception(error_msg)
                    
        except Exception as e:
            error_msg = f"Streaming request failed: {str(e)}"
            self.error_occurred.emit(error_msg)
            raise


class OllamaWorker(QThread):
    """Worker thread for handling Ollama API calls"""
    
    response_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, config: Config, prompt: str, system_prompt: Optional[str] = None):
        super().__init__()
        self.config = config
        self.prompt = prompt
        self.system_prompt = system_prompt
        
    def run(self):
        """Run the async Ollama request in a separate thread"""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the async function
            response = loop.run_until_complete(self._generate_response())
            self.response_ready.emit(response)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
        finally:
            loop.close()
            
    async def _generate_response(self) -> str:
        """Generate response using OllamaClient"""
        async with OllamaClient(self.config) as client:
            return await client.generate_response(self.prompt, self.system_prompt)
