#!/usr/bin/env python3
"""
JARVIS V6 Executable Builder
============================

Creates a standalone .exe file for JARVIS V6 AI Assistant
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("✅ PyInstaller already installed")
        return True
    except ImportError:
        print("📦 Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install PyInstaller: {e}")
            return False

def create_spec_file():
    """Create PyInstaller spec file for JARVIS"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Data files to include
datas = [
    ('src', 'src'),
    ('assets', 'assets'),
    ('.env', '.'),
]

# Hidden imports for dynamic loading
hiddenimports = [
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'pygame',
    'requests',
    'sqlite3',
    'json',
    'threading',
    'asyncio',
    'datetime',
    'pathlib',
    'typing',
    'dataclasses',
    'collections',
    'functools',
    'itertools',
    'os',
    'sys',
    'time',
    'random',
    'math',
    're',
    'urllib',
    'http',
    'ssl',
    'socket',
    'email',
    'base64',
    'hashlib',
    'hmac',
    'uuid',
    'tempfile',
    'shutil',
    'zipfile',
    'tarfile',
    'csv',
    'xml',
    'html',
    'logging',
    'configparser',
    'argparse',
    'subprocess',
    'multiprocessing',
    'concurrent.futures',
    'queue',
    'weakref',
    'gc',
    'ctypes',
    'struct',
    'array',
    'io',
    'codecs',
    'locale',
    'platform',
    'traceback',
    'warnings',
    'inspect',
    'ast',
    'dis',
    'types',
    'copy',
    'pickle',
    'shelve',
    'dbm',
    'sqlite3',
    'src.core.config',
    'src.gui.main_window',
    'src.gui.chat_widget',
    'src.gui.input_widget',
    'src.gui.qt_compat',
    'src.ai.ollama_client',
    'src.ai.training_system',
    'src.ai.self_edit_system',
    'src.ai.knowledge_base',
    'src.ai.function_manager',
    'src.ai.advanced_memory',
    'src.ai.self_evolution',
    'src.plugins.tts_elevenlabs',
]

a = Analysis(
    ['jarvis_clean.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='JARVIS_V6',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/jarvis_icon.ico' if os.path.exists('assets/jarvis_icon.ico') else None,
)
'''
    
    with open('jarvis.spec', 'w') as f:
        f.write(spec_content)
    
    print("✅ Created jarvis.spec file")

def create_icon():
    """Create a simple icon file if it doesn't exist"""
    assets_dir = Path('assets')
    assets_dir.mkdir(exist_ok=True)
    
    icon_path = assets_dir / 'jarvis_icon.ico'
    if not icon_path.exists():
        print("📝 Creating default icon...")
        # For now, we'll skip icon creation - PyInstaller will use default
        print("ℹ️ Using default icon (you can add jarvis_icon.ico to assets/ folder later)")

def build_executable():
    """Build the executable using PyInstaller"""
    print("🔨 Building JARVIS V6 executable...")
    print("This may take a few minutes...")
    
    try:
        # Run PyInstaller with the spec file
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "jarvis.spec"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build completed successfully!")
            
            # Check if exe was created
            exe_path = Path('dist/JARVIS_V6.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📦 Executable created: {exe_path}")
                print(f"📏 Size: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Executable not found in dist/ folder")
                return False
        else:
            print("❌ Build failed!")
            print("Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_launcher_batch():
    """Create a simple batch file launcher"""
    batch_content = '''@echo off
title JARVIS V6 AI Assistant
echo Starting JARVIS V6...
echo.
JARVIS_V6.exe
pause
'''
    
    with open('Launch_JARVIS.bat', 'w') as f:
        f.write(batch_content)
    
    print("✅ Created Launch_JARVIS.bat")

def main():
    """Main build process"""
    print("🚀 JARVIS V6 Executable Builder")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('jarvis_clean.py'):
        print("❌ Error: jarvis_clean.py not found!")
        print("Please run this script from the JARVIS V6 project directory")
        return False
    
    # Install PyInstaller
    if not install_pyinstaller():
        return False
    
    # Create necessary files
    create_icon()
    create_spec_file()
    
    # Build executable
    if build_executable():
        create_launcher_batch()
        
        print("\n🎉 JARVIS V6 Executable Build Complete!")
        print("=" * 50)
        print("📁 Files created:")
        print("   • dist/JARVIS_V6.exe - Main executable")
        print("   • Launch_JARVIS.bat - Easy launcher")
        print("\n🚀 To run JARVIS:")
        print("   1. Double-click JARVIS_V6.exe in the dist/ folder")
        print("   2. Or double-click Launch_JARVIS.bat")
        print("\n💡 You can copy JARVIS_V6.exe anywhere and it will work!")
        print("   (Make sure to copy your .env file with it)")
        
        return True
    else:
        print("\n❌ Build failed. Check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
