#!/usr/bin/env python3
"""
Test Enhanced AI Processing System for JARVIS V6
===============================================

Test the new enhanced AI processing with multiple providers, caching,
parallel processing, and smart model selection.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai.enhanced_ai_processor import EnhancedAIProcessor, QueryType, AIProvider

async def test_enhanced_ai():
    """Test the enhanced AI processing system"""
    print("🚀 Testing JARVIS Enhanced AI Processing System")
    print("=" * 60)
    
    # Progress callback for testing
    def progress_callback(percentage, status):
        print(f"📊 Progress: {percentage}% - {status}")
    
    # Initialize enhanced AI processor
    ai_processor = EnhancedAIProcessor(progress_callback=progress_callback)
    
    print(f"✅ Enhanced AI processor initialized")
    
    # Check provider status
    print("\n🌐 Provider Status:")
    print("-" * 40)
    status = ai_processor.get_provider_status()
    for provider, info in status.items():
        availability = "✅ Available" if info['available'] else "❌ Unavailable"
        health = "🟢 Healthy" if info['healthy'] else "🔴 Unhealthy"
        print(f"   {provider}: {availability} {health}")
        print(f"      Speed: {info['speed_rating']}/10, Quality: {info['quality_rating']}/10")
        print(f"      Success Rate: {info['success_rate']}")
    
    print("\n🎯 Testing Query Classification...")
    print("-" * 40)
    
    # Test query classification
    test_queries = [
        ("What is artificial intelligence?", QueryType.SIMPLE_QUESTION),
        ("Write a creative story about robots", QueryType.CREATIVE_TASK),
        ("Analyze the pros and cons of AI", QueryType.COMPLEX_ANALYSIS),
        ("Help me debug this Python code", QueryType.TECHNICAL_HELP),
        ("Hello, how are you today?", QueryType.CONVERSATION),
        ("Turn on the lights", QueryType.COMMAND)
    ]
    
    for query, expected_type in test_queries:
        classified_type = ai_processor._classify_query(query)
        match = "✅" if classified_type == expected_type else "⚠️"
        print(f"   {match} '{query[:30]}...' → {classified_type.value}")
    
    print("\n🧪 Testing AI Processing...")
    print("-" * 40)
    
    # Test simple query
    print("\n1. Testing Simple Query Processing:")
    query = "What is JARVIS?"
    system_prompt = "You are JARVIS, an advanced AI assistant. Be helpful and professional."
    
    try:
        response = await ai_processor.process_query(query, system_prompt)
        print(f"✅ Response received from {response.provider.value}")
        print(f"   Model: {response.model}")
        print(f"   Response Time: {response.response_time:.2f}s")
        print(f"   Cached: {response.cached}")
        print(f"   Content: {response.content[:100]}...")
        
        # Test cache hit
        print("\n2. Testing Cache Hit:")
        cached_response = await ai_processor.process_query(query, system_prompt)
        if cached_response.cached:
            print(f"⚡ Cache hit! Response time: {cached_response.response_time:.3f}s")
        else:
            print("⚠️ Cache miss - this might indicate an issue")
            
    except Exception as e:
        print(f"❌ Error testing simple query: {e}")
    
    print("\n🏁 Testing Parallel Processing...")
    print("-" * 40)
    
    # Test parallel processing (if multiple providers available)
    available_providers = [p for p, info in status.items() if info['available']]
    if len(available_providers) > 1:
        try:
            parallel_query = "Explain quantum computing in simple terms"
            print(f"Query: {parallel_query}")
            
            parallel_response = await ai_processor.process_query_parallel(
                parallel_query, system_prompt, max_providers=3
            )
            
            print(f"🏆 Fastest response from {parallel_response.provider.value}")
            print(f"   Response Time: {parallel_response.response_time:.2f}s")
            print(f"   Content: {parallel_response.content[:100]}...")
            
        except Exception as e:
            print(f"❌ Error testing parallel processing: {e}")
    else:
        print("⚠️ Only one provider available - skipping parallel test")
    
    print("\n📊 Performance Statistics:")
    print("-" * 40)
    
    # Get performance stats
    perf_stats = ai_processor.get_performance_stats()
    
    print(f"Cache Entries: {perf_stats['cache_stats']['total_entries']}")
    
    for provider, stats in perf_stats['providers'].items():
        if stats['total_requests'] > 0:
            print(f"\n{provider}:")
            print(f"   Requests: {stats['total_requests']}")
            print(f"   Avg Response Time: {stats['avg_response_time']}s")
            print(f"   Success Rate: {stats['success_rate']}")
            print(f"   Status: {'Healthy' if stats['healthy'] else 'Unhealthy'}")
    
    print("\n🔧 Testing Provider Selection...")
    print("-" * 40)
    
    # Test provider selection for different query types
    for query_type in QueryType:
        optimal_providers = ai_processor._select_optimal_provider(query_type)
        if optimal_providers:
            print(f"{query_type.value}: {[p.value for p in optimal_providers[:3]]}")
    
    print("\n🧹 Testing Cache Management...")
    print("-" * 40)
    
    cache_before = len(ai_processor.response_cache)
    print(f"Cache entries before clear: {cache_before}")
    
    ai_processor.clear_cache()
    
    cache_after = len(ai_processor.response_cache)
    print(f"Cache entries after clear: {cache_after}")
    
    print("\n🎉 Enhanced AI Testing Complete!")
    print("=" * 60)
    
    return ai_processor

async def test_real_world_scenarios():
    """Test real-world AI processing scenarios"""
    print("\n🌍 Testing Real-World Scenarios...")
    print("=" * 50)
    
    def progress_callback(percentage, status):
        print(f"📊 {percentage}% - {status}")
    
    ai_processor = EnhancedAIProcessor(progress_callback=progress_callback)
    
    real_world_tests = [
        {
            "query": "How do I improve my productivity?",
            "type": "Simple advice request",
            "expected_provider": "fast provider (Groq/Gemini)"
        },
        {
            "query": "Write a Python function to calculate fibonacci numbers with memoization",
            "type": "Technical coding task",
            "expected_provider": "high-quality provider (OpenAI/Ollama)"
        },
        {
            "query": "Compare the advantages and disadvantages of renewable energy sources",
            "type": "Complex analysis",
            "expected_provider": "high-quality provider (OpenAI/Ollama)"
        },
        {
            "query": "Hello JARVIS, how are you today?",
            "type": "Casual conversation",
            "expected_provider": "fast provider (Groq/Gemini)"
        }
    ]
    
    system_prompt = "You are JARVIS, an advanced AI assistant. Be helpful, professional, and concise."
    
    for i, test in enumerate(real_world_tests, 1):
        print(f"\n{i}. {test['type']}")
        print(f"   Query: '{test['query']}'")
        print(f"   Expected: {test['expected_provider']}")
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await ai_processor.process_query(test['query'], system_prompt)
            end_time = asyncio.get_event_loop().time()
            
            print(f"   ✅ Response from: {response.provider.value} ({response.model})")
            print(f"   ⏱️ Time: {response.response_time:.2f}s")
            print(f"   📝 Length: {len(response.content)} chars")
            print(f"   💾 Cached: {response.cached}")
            
            if len(response.content) > 100:
                print(f"   📄 Preview: {response.content[:100]}...")
            else:
                print(f"   📄 Content: {response.content}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n✅ Real-world scenario testing complete!")

if __name__ == "__main__":
    try:
        # Test enhanced AI processing
        ai_processor = asyncio.run(test_enhanced_ai())
        
        # Test real-world scenarios
        asyncio.run(test_real_world_scenarios())
        
        print(f"\n🎊 All enhanced AI tests completed successfully!")
        print(f"🚀 JARVIS now has advanced multi-provider AI processing!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
