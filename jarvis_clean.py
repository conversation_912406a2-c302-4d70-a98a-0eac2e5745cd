#!/usr/bin/env python3
"""
Clean JARVIS V6 Launcher
Suppresses Qt warnings for a cleaner terminal output
"""

import sys
import os
import warnings

def main():
    """Launch JARVIS V6 with clean output"""
    print("🚀 Starting JARVIS V6 AI Assistant...")
    print("=" * 50)
    
    # Suppress Qt warnings about unknown CSS properties
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    try:
        # Import PySide6 directly
        print("Loading PySide6...")
        from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
        from PySide6.QtWidgets import QTextEdit, QLineEdit, QPushButton, QLabel, QFrame, QProgressBar
        from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QPointF, QRectF
        from PySide6.QtCore import QThread, QObject, Signal, Property
        from PySide6.QtGui import QFont, QColor, QPainter, QBrush, QLinearGradient, QPen, QRadialGradient, QTextCursor
        print("✅ PySide6 loaded successfully")
        
        # Monkey patch the compatibility
        import src.gui.qt_compat as qt_compat
        qt_compat.QApplication = QApplication
        qt_compat.QMainWindow = QMainWindow
        qt_compat.QWidget = QWidget
        qt_compat.QVBoxLayout = QVBoxLayout
        qt_compat.QHBoxLayout = QHBoxLayout
        qt_compat.QTextEdit = QTextEdit
        qt_compat.QLineEdit = QLineEdit
        qt_compat.QPushButton = QPushButton
        qt_compat.QLabel = QLabel
        qt_compat.QFrame = QFrame
        qt_compat.QProgressBar = QProgressBar
        qt_compat.Qt = Qt
        qt_compat.QTimer = QTimer
        qt_compat.QPropertyAnimation = QPropertyAnimation
        qt_compat.QEasingCurve = QEasingCurve
        qt_compat.QPointF = QPointF
        qt_compat.QRectF = QRectF
        qt_compat.QThread = QThread
        qt_compat.QObject = QObject
        qt_compat.QFont = QFont
        qt_compat.QColor = QColor
        qt_compat.QPainter = QPainter
        qt_compat.QBrush = QBrush
        qt_compat.QLinearGradient = QLinearGradient
        qt_compat.QPen = QPen
        qt_compat.QRadialGradient = QRadialGradient
        qt_compat.QTextCursor = QTextCursor
        qt_compat.pyqtSignal = Signal
        qt_compat.pyqtProperty = Property
        qt_compat.QT_BACKEND = "PySide6"
        
        print("✅ Compatibility layer patched")
        
        # Now import our modules
        print("Loading JARVIS modules...")
        from src.gui.main_window import JarvisMainWindow
        print("✅ JARVIS modules loaded successfully")
        
        # Create application
        print("Creating application...")
        app = QApplication(sys.argv)
        app.setApplicationName("Jarvis V6")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("Jarvis AI")
        print("✅ Application created")
        
        # Create main window
        print("Initializing JARVIS interface...")
        window = JarvisMainWindow()
        window.show()
        print("✅ JARVIS interface ready!")
        
        print("\n🎉 JARVIS V6 is now running!")
        print("Features available:")
        print("  • Futuristic JARVIS HUD interface")
        print("  • Animated AI core visualization")
        print("  • Real-time system monitoring")
        print("  • ElevenLabs TTS voice output")
        print("  • Mixtral 8x7B AI chat")
        print("\n💬 Start chatting with your AI assistant!")
        print("📝 Type your message in the input field and press Enter")
        print("🔊 Toggle voice output using the TTS control panel")
        print("\nEnjoy your AI assistant! 🤖")
        
        # Start the application
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure PySide6 is installed: pip install PySide6")
        print("2. Check Python version (3.8+ required)")
        return 1
        
    except Exception as e:
        print(f"❌ Error starting JARVIS: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    # Don't use input() in executable - causes RuntimeError: lost sys.stdin
    sys.exit(exit_code)
