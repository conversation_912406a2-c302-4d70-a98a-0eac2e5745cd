#!/usr/bin/env python3
"""
Test main.py step by step
"""

import sys

print("Step 1: Testing qt_compat import...")
try:
    from src.gui.qt_compat import QApplication
    print("✅ qt_compat import successful")
except Exception as e:
    print(f"❌ qt_compat import failed: {e}")
    sys.exit(1)

print("Step 2: Testing main_window import...")
try:
    from src.gui.main_window import Jarvis<PERSON><PERSON><PERSON><PERSON><PERSON>
    print("✅ main_window import successful")
except Exception as e:
    print(f"❌ main_window import failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("Step 3: Creating QApplication...")
try:
    app = QApplication(sys.argv)
    print("✅ QApplication created successfully")
except Exception as e:
    print(f"❌ QApplication creation failed: {e}")
    sys.exit(1)

print("Step 4: Creating main window...")
try:
    window = JarvisMainWindow()
    print("✅ Main window created successfully")
except Exception as e:
    print(f"❌ Main window creation failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("🎉 All tests passed! JARVIS V6 is ready to run!")
print("The application should work now.")
