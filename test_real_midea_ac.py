#!/usr/bin/env python3
"""
Test Real Midea AC Connection for JARVIS V6
IP: ************
MAC: 9c:c9:eb:67:bf:b1
"""

import asyncio
import sys
import os
import socket
import subprocess
import platform

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from plugins.smart_home_plugin import SmartHomeManager
from ai.smart_home_commands import SmartHomeCommandProcessor

async def test_network_connectivity():
    """Test basic network connectivity to your Midea AC"""
    print("🌡️ Testing Network Connectivity to Your Midea AC")
    print("=" * 60)
    
    ac_ip = "************"
    ac_mac = "9c:c9:eb:67:bf:b1"
    
    print(f"🎯 Target Device:")
    print(f"   IP Address: {ac_ip}")
    print(f"   MAC Address: {ac_mac}")
    print()
    
    # Test 1: Ping test
    print("1. Testing Ping Connectivity...")
    try:
        if platform.system().lower() == "windows":
            cmd = ["ping", "-n", "3", "-w", "3000", ac_ip]
        else:
            cmd = ["ping", "-c", "3", "-W", "3", ac_ip]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"   ✅ Ping successful - Device is reachable")
            # Extract ping statistics
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if 'time=' in line.lower() or 'reply from' in line.lower():
                    print(f"   📊 {line.strip()}")
        else:
            print(f"   ❌ Ping failed - Device may be offline or blocking ping")
            print(f"   📄 Error: {result.stderr.strip()}")
    
    except Exception as e:
        print(f"   ❌ Ping test error: {e}")
    
    print()
    
    # Test 2: Port scanning
    print("2. Testing Common AC Ports...")
    common_ports = [6444, 80, 443, 8080, 502, 1883, 8883]
    
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((ac_ip, port))
            
            if result == 0:
                print(f"   ✅ Port {port}: OPEN")
            else:
                print(f"   🔒 Port {port}: Closed/Filtered")
            
            sock.close()
            
        except Exception as e:
            print(f"   ❌ Port {port}: Error - {e}")
    
    print()
    
    # Test 3: ARP table check (Windows)
    print("3. Checking ARP Table for MAC Address...")
    try:
        if platform.system().lower() == "windows":
            result = subprocess.run(["arp", "-a"], capture_output=True, text=True, timeout=5)
            if ac_mac.lower() in result.stdout.lower():
                print(f"   ✅ MAC address {ac_mac} found in ARP table")
                # Find the line with the MAC address
                for line in result.stdout.split('\n'):
                    if ac_mac.lower() in line.lower():
                        print(f"   📊 {line.strip()}")
            else:
                print(f"   ⚠️ MAC address {ac_mac} not found in ARP table")
                print(f"   💡 This is normal if the device hasn't communicated recently")
        else:
            print(f"   ℹ️ ARP check not implemented for this OS")
    
    except Exception as e:
        print(f"   ❌ ARP check error: {e}")
    
    print()

async def test_smart_home_integration():
    """Test JARVIS smart home integration with real AC"""
    print("🏠 Testing JARVIS Smart Home Integration")
    print("=" * 50)
    
    # Initialize smart home manager
    print("1. Initializing Smart Home Manager...")
    manager = SmartHomeManager()
    
    # Initialize the system
    print("2. Connecting to your Midea AC...")
    success = await manager.initialize()
    
    if success:
        print("✅ Smart Home system initialized")
        
        # Show device status
        status = manager.get_device_status()
        print(f"\n📊 System Status:")
        print(f"   Total Devices: {status['total_devices']}")
        print(f"   Platforms: {status['platforms']}")
        print(f"   Rooms: {status['rooms']}")
        
        print(f"\n🌡️ Your Midea AC:")
        for room, devices in status['devices_by_room'].items():
            for device in devices:
                if device['type'] == 'ac':
                    state_icon = "🟢" if device['state'] == "on" else "🔴"
                    print(f"   {state_icon} {device['name']} ({device['platform']})")
        
        # Test commands
        print(f"\n3. Testing AC Commands...")
        processor = SmartHomeCommandProcessor()
        
        test_commands = [
            "turn on the ac",
            "set ac to 24 degrees",
            "check ac status"
        ]
        
        for command_text in test_commands:
            print(f"\n🗣️ Command: '{command_text}'")
            command = processor.parse_command(command_text)
            
            if command and command.confidence > 0.5:
                print(f"   ✅ Parsed successfully")
                print(f"   🎯 Type: {command.command_type.value}")
                print(f"   📍 Target: {command.target}")
                
                # Execute command (this will attempt real device communication)
                try:
                    if command.command_type.value == "turn_on":
                        result = await manager.turn_on_device(command.target)
                        print(f"   🔄 Result: {'✅ Success' if result else '❌ Failed'}")
                    
                    elif command.command_type.value == "set_temperature":
                        device = manager.find_device_by_name(command.target)
                        if device:
                            platform_key = device.platform.lower().replace(" ", "_").replace("-", "_")
                            platform = manager.platforms.get(platform_key)
                            if platform and hasattr(platform, 'set_temperature'):
                                result = await platform.set_temperature(device.id, command.value)
                                print(f"   🔄 Result: {'✅ Temperature set' if result else '❌ Failed'}")
                    
                    elif command.command_type.value == "status":
                        device = manager.find_device_by_name(command.target)
                        if device:
                            state_icon = "🟢" if device.state.value == "on" else "🔴"
                            print(f"   📊 Status: {state_icon} {device.name}")
                
                except Exception as e:
                    print(f"   ❌ Command execution error: {e}")
            
            else:
                print(f"   ❌ Could not parse command")
        
        return True
        
    else:
        print("❌ Smart Home system initialization failed")
        return False

def show_next_steps():
    """Show next steps for real device integration"""
    print("\n🔮 Next Steps for Full Midea AC Integration")
    print("=" * 60)
    
    print("📋 Current Status:")
    print("   ✅ Network connectivity testing implemented")
    print("   ✅ Basic device discovery working")
    print("   ✅ Command parsing and routing functional")
    print("   ⚠️ Midea protocol implementation needed")
    print()
    
    print("🛠️ To Enable Full Control:")
    print("   1. Install Midea protocol library:")
    print("      pip install msmart")
    print()
    print("   2. Or implement custom Midea protocol:")
    print("      - Device authentication")
    print("      - Command encryption/decryption")
    print("      - UDP/TCP communication")
    print()
    print("   3. Alternative: Use Midea cloud API")
    print("      - Requires Midea account credentials")
    print("      - Internet connection required")
    print()
    
    print("💡 Current Capabilities:")
    print("   ✅ Voice command recognition")
    print("   ✅ Network connectivity testing")
    print("   ✅ Device status tracking")
    print("   ✅ JARVIS integration complete")
    print()
    
    print("🎯 Your AC Configuration:")
    print("   IP: ************")
    print("   MAC: 9c:c9:eb:67:bf:b1")
    print("   Status: Ready for protocol implementation")

async def main():
    """Main test function"""
    try:
        # Test network connectivity
        await test_network_connectivity()
        
        # Test smart home integration
        success = await test_smart_home_integration()
        
        # Show next steps
        show_next_steps()
        
        if success:
            print(f"\n🎉 Your Midea AC is ready for JARVIS control!")
            print(f"💬 Try saying: 'JARVIS, turn on the AC'")
        else:
            print(f"\n⚠️ Setup needs attention. Check network connectivity.")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
