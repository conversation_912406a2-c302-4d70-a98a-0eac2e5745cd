"""
Memory Viewer for JARVIS V6
===========================

GUI interface for viewing and managing JARVIS memory system including:
- Conversation history
- Knowledge database
- User profile information
- Memory statistics
"""

from src.gui.qt_compat import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QTextEdit, QLabel, QListWidget, QListWidgetItem,
                               QPushButton, QLineEdit, QComboBox, QSpinBox,
                               QGroupBox, QScrollArea, QWidget, QSplitter,
                               Qt, QFont, QTimer)
from datetime import datetime, timedelta
import json


class MemoryViewer(QDialog):
    """Memory viewer dialog for JARVIS memory system"""
    
    def __init__(self, memory_system, parent=None):
        super().__init__(parent)
        self.memory_system = memory_system
        self.setWindowTitle("JARVIS Memory System Viewer")
        self.setGeometry(100, 100, 1200, 800)
        self.setup_ui()
        self.load_data()
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # Refresh every 30 seconds
    
    def setup_ui(self):
        """Setup the memory viewer interface"""
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("🧠 JARVIS Memory System")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setStyleSheet("color: #00FF00; margin: 10px;")
        layout.addWidget(title)
        
        # Tab widget for different memory views
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #333;
                background-color: #1a1a1a;
            }
            QTabBar::tab {
                background-color: #2a2a2a;
                color: #00FF00;
                padding: 8px 16px;
                margin: 2px;
            }
            QTabBar::tab:selected {
                background-color: #00FF00;
                color: #000000;
            }
        """)
        
        # Conversation History Tab
        self.setup_conversation_tab()
        
        # Knowledge Database Tab
        self.setup_knowledge_tab()
        
        # User Profile Tab
        self.setup_profile_tab()
        
        # Memory Statistics Tab
        self.setup_stats_tab()
        
        layout.addWidget(self.tab_widget)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("🔄 Refresh")
        self.refresh_button.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_button)
        
        self.export_button = QPushButton("📤 Export Data")
        self.export_button.clicked.connect(self.export_data)
        button_layout.addWidget(self.export_button)
        
        button_layout.addStretch()
        
        self.close_button = QPushButton("❌ Close")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
        # Apply dark theme
        self.setStyleSheet("""
            QDialog {
                background-color: #0a0a0a;
                color: #00FF00;
            }
            QTextEdit, QListWidget {
                background-color: #1a1a1a;
                color: #00FF00;
                border: 1px solid #333;
                font-family: 'Courier New', monospace;
            }
            QPushButton {
                background-color: #2a2a2a;
                color: #00FF00;
                border: 1px solid #00FF00;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #00FF00;
                color: #000000;
            }
            QLabel {
                color: #00FF00;
            }
        """)
    
    def setup_conversation_tab(self):
        """Setup conversation history tab"""
        conv_widget = QWidget()
        layout = QVBoxLayout(conv_widget)
        
        # Search and filter controls
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Search:"))
        
        self.conv_search = QLineEdit()
        self.conv_search.setPlaceholderText("Search conversations...")
        self.conv_search.textChanged.connect(self.filter_conversations)
        search_layout.addWidget(self.conv_search)
        
        search_layout.addWidget(QLabel("Limit:"))
        self.conv_limit = QSpinBox()
        self.conv_limit.setRange(10, 1000)
        self.conv_limit.setValue(50)
        self.conv_limit.valueChanged.connect(self.load_conversations)
        search_layout.addWidget(self.conv_limit)
        
        layout.addLayout(search_layout)
        
        # Conversation list
        self.conversation_list = QTextEdit()
        self.conversation_list.setReadOnly(True)
        layout.addWidget(self.conversation_list)
        
        self.tab_widget.addTab(conv_widget, "💬 Conversations")
    
    def setup_knowledge_tab(self):
        """Setup knowledge database tab"""
        knowledge_widget = QWidget()
        layout = QVBoxLayout(knowledge_widget)
        
        # Search controls
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Search Knowledge:"))
        
        self.knowledge_search = QLineEdit()
        self.knowledge_search.setPlaceholderText("Search knowledge base...")
        self.knowledge_search.textChanged.connect(self.filter_knowledge)
        search_layout.addWidget(self.knowledge_search)
        
        layout.addLayout(search_layout)
        
        # Knowledge display
        self.knowledge_display = QTextEdit()
        self.knowledge_display.setReadOnly(True)
        layout.addWidget(self.knowledge_display)
        
        self.tab_widget.addTab(knowledge_widget, "📚 Knowledge")
    
    def setup_profile_tab(self):
        """Setup user profile tab"""
        profile_widget = QWidget()
        layout = QVBoxLayout(profile_widget)
        
        # Profile information
        self.profile_display = QTextEdit()
        self.profile_display.setReadOnly(True)
        layout.addWidget(self.profile_display)
        
        self.tab_widget.addTab(profile_widget, "👤 Profile")
    
    def setup_stats_tab(self):
        """Setup memory statistics tab"""
        stats_widget = QWidget()
        layout = QVBoxLayout(stats_widget)
        
        # Statistics display
        self.stats_display = QTextEdit()
        self.stats_display.setReadOnly(True)
        layout.addWidget(self.stats_display)
        
        self.tab_widget.addTab(stats_widget, "📊 Statistics")
    
    def load_data(self):
        """Load all memory data"""
        self.load_conversations()
        self.load_knowledge()
        self.load_profile()
        self.load_statistics()
    
    def load_conversations(self):
        """Load conversation history"""
        try:
            limit = self.conv_limit.value()
            conversations = self.memory_system.get_conversation_history(limit=limit)
            
            conv_text = f"📅 Conversation History (Last {len(conversations)} messages)\n"
            conv_text += "=" * 60 + "\n\n"
            
            for conv in conversations:
                timestamp = datetime.fromisoformat(conv.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                conv_text += f"🕒 {timestamp} | Session: {conv.session_id[:8]}...\n"
                conv_text += f"👤 USER: {conv.user_message}\n"
                conv_text += f"🤖 JARVIS: {conv.jarvis_response}\n"
                
                if conv.tags:
                    conv_text += f"🏷️ Tags: {', '.join(conv.tags)}\n"
                
                conv_text += f"😊 Emotional Tone: {conv.emotional_tone:.2f}\n"
                conv_text += "-" * 60 + "\n\n"
            
            self.conversation_list.setText(conv_text)
            
        except Exception as e:
            self.conversation_list.setText(f"Error loading conversations: {e}")
    
    def load_knowledge(self):
        """Load knowledge database"""
        try:
            # Get all knowledge entries
            knowledge_entries = self.memory_system.search_knowledge("", limit=100)
            
            knowledge_text = f"📚 Knowledge Database ({len(knowledge_entries)} entries)\n"
            knowledge_text += "=" * 60 + "\n\n"
            
            for entry in knowledge_entries:
                timestamp = datetime.fromisoformat(entry.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                verified_icon = "✅" if entry.verified else "⚠️"
                
                knowledge_text += f"{verified_icon} {entry.topic}\n"
                knowledge_text += f"📅 {timestamp} | Confidence: {entry.confidence:.2f}\n"
                knowledge_text += f"📝 {entry.content}\n"
                knowledge_text += f"🔗 Source: {entry.source}\n"
                
                if entry.tags:
                    knowledge_text += f"🏷️ Tags: {', '.join(entry.tags)}\n"
                
                knowledge_text += "-" * 60 + "\n\n"
            
            self.knowledge_display.setText(knowledge_text)
            
        except Exception as e:
            self.knowledge_display.setText(f"Error loading knowledge: {e}")
    
    def load_profile(self):
        """Load user profile information"""
        try:
            profile = self.memory_system.user_profile
            
            profile_text = f"👤 User Profile: {profile.name}\n"
            profile_text += "=" * 60 + "\n\n"
            
            profile_text += f"🆔 User ID: {profile.user_id}\n"
            profile_text += f"💬 Communication Style: {profile.communication_style}\n"
            profile_text += f"🎓 Learning Style: {profile.learning_style}\n"
            profile_text += f"📅 Created: {profile.created_at}\n"
            profile_text += f"🔄 Updated: {profile.updated_at}\n\n"
            
            profile_text += "⚙️ Preferences:\n"
            for key, value in profile.preferences.items():
                profile_text += f"  • {key}: {value}\n"
            
            profile_text += "\n🎯 Favorite Topics:\n"
            for topic in profile.favorite_topics:
                profile_text += f"  • {topic}\n"
            
            profile_text += "\n🎯 Goals & Interests:\n"
            for goal in profile.goals_and_interests:
                profile_text += f"  • {goal}\n"
            
            profile_text += "\n📊 Personal Facts:\n"
            for key, value in profile.personal_facts.items():
                profile_text += f"  • {key}: {value}\n"
            
            self.profile_display.setText(profile_text)
            
        except Exception as e:
            self.profile_display.setText(f"Error loading profile: {e}")
    
    def load_statistics(self):
        """Load memory statistics"""
        try:
            stats = self.memory_system.get_memory_stats()
            
            stats_text = "📊 Memory System Statistics\n"
            stats_text += "=" * 60 + "\n\n"
            
            stats_text += f"🧠 Total Memories: {stats['total_memories']}\n"
            stats_text += f"💬 Total Conversations: {stats['total_conversations']}\n"
            stats_text += f"📚 Knowledge Entries: {stats['total_knowledge_entries']}\n"
            stats_text += f"✅ Verified Knowledge: {stats['verified_knowledge']}\n"
            stats_text += f"🎯 Current Session Messages: {stats['current_session_messages']}\n"
            stats_text += f"📅 Total Sessions: {stats['total_sessions']}\n"
            stats_text += f"⭐ Average Importance: {stats['average_importance']}\n\n"
            
            stats_text += "🧠 Memory Types:\n"
            for mem_type, count in stats['memory_types'].items():
                stats_text += f"  • {mem_type}: {count}\n"
            
            stats_text += f"\n🎭 Current Emotional State:\n"
            stats_text += f"  • Valence: {stats['emotional_state']['valence']:.2f}\n"
            stats_text += f"  • Arousal: {stats['emotional_state']['arousal']:.2f}\n"
            
            stats_text += f"\n🔗 Session Info:\n"
            stats_text += f"  • Current Session: {stats['session_id']}\n"
            
            self.stats_display.setText(stats_text)
            
        except Exception as e:
            self.stats_display.setText(f"Error loading statistics: {e}")
    
    def filter_conversations(self):
        """Filter conversations based on search text"""
        # This would implement conversation filtering
        # For now, just reload conversations
        self.load_conversations()
    
    def filter_knowledge(self):
        """Filter knowledge based on search text"""
        search_text = self.knowledge_search.text()
        if search_text:
            try:
                knowledge_entries = self.memory_system.search_knowledge(search_text, limit=50)
                
                knowledge_text = f"🔍 Search Results for '{search_text}' ({len(knowledge_entries)} entries)\n"
                knowledge_text += "=" * 60 + "\n\n"
                
                for entry in knowledge_entries:
                    timestamp = datetime.fromisoformat(entry.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                    verified_icon = "✅" if entry.verified else "⚠️"
                    
                    knowledge_text += f"{verified_icon} {entry.topic}\n"
                    knowledge_text += f"📅 {timestamp} | Confidence: {entry.confidence:.2f}\n"
                    knowledge_text += f"📝 {entry.content}\n"
                    knowledge_text += f"🔗 Source: {entry.source}\n"
                    
                    if entry.tags:
                        knowledge_text += f"🏷️ Tags: {', '.join(entry.tags)}\n"
                    
                    knowledge_text += "-" * 60 + "\n\n"
                
                self.knowledge_display.setText(knowledge_text)
                
            except Exception as e:
                self.knowledge_display.setText(f"Error searching knowledge: {e}")
        else:
            self.load_knowledge()
    
    def refresh_data(self):
        """Refresh all data"""
        self.load_data()
    
    def export_data(self):
        """Export memory data to file"""
        try:
            from PySide6.QtWidgets import QFileDialog
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "Export Memory Data", 
                f"jarvis_memory_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )
            
            if filename:
                # Export comprehensive memory data
                export_data = {
                    "export_timestamp": datetime.now().isoformat(),
                    "user_profile": self.memory_system.user_profile.__dict__,
                    "memory_stats": self.memory_system.get_memory_stats(),
                    "conversations": [conv.__dict__ for conv in self.memory_system.get_conversation_history(limit=1000)],
                    "knowledge": [knowledge.__dict__ for knowledge in self.memory_system.search_knowledge("", limit=1000)]
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)
                
                self.stats_display.append(f"\n✅ Data exported to: {filename}")
                
        except Exception as e:
            self.stats_display.append(f"\n❌ Export error: {e}")
