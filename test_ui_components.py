#!/usr/bin/env python3
"""
Test UI Components for JARVIS V6
================================

Simple test to verify all UI components are working correctly.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.gui.qt_compat import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from src.core.config import Config

def test_qt_imports():
    """Test that all Qt imports are working"""
    print("🧪 Testing Qt imports...")
    
    try:
        from src.gui.qt_compat import (
            QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
            QTextEdit, QLineEdit, QPushButton, QLabel, QFrame,
            QSplitter, QStatusBar, QGridLayout, Qt, QTimer,
            pyqtSignal, QPropertyAnimation, QEasingCurve,
            QFont, QPalette, QColor, QPixmap, QPainter, QBrush, 
            QLinearGradient, QDialog, QTabWidget, QSizePolicy
        )
        print("✅ All Qt imports successful")
        return True
    except ImportError as e:
        print(f"❌ Qt import failed: {e}")
        return False

def test_ai_systems_panel():
    """Test AI systems panel creation"""
    print("🧪 Testing AI Systems Panel...")
    
    try:
        app = QApplication(sys.argv)
        
        # Create test widget
        widget = QWidget()
        widget.setWindowTitle("JARVIS V6 - AI Systems Test")
        widget.resize(400, 600)
        
        layout = QVBoxLayout(widget)
        
        # Test AI Systems Panel
        ai_frame = QWidget()
        ai_frame.setObjectName("ttsControlFrame")
        ai_frame.setFixedHeight(320)
        ai_frame.setMinimumWidth(280)
        ai_layout = QVBoxLayout(ai_frame)
        
        # AI Systems Title
        ai_systems_title = QLabel("AI SYSTEMS")
        ai_systems_title.setObjectName("hudPanelTitle")
        ai_layout.addWidget(ai_systems_title)
        
        # Test buttons
        buttons = [
            ("🧠 LEARNING SYSTEM", "aiSystemButton"),
            ("✏️ SELF-EDIT SYSTEM", "aiSystemButton"),
            ("🧠 MEMORY SYSTEM", "aiSystemButton"),
            ("📊 MEMORY VIEWER", "aiSystemButton"),
            ("🧠 SEMANTIC AI", "aiSystemButton"),
            ("⚡ ENHANCED AI", "aiSystemButton"),
            ("🔬 EVOLUTION SYSTEM", "aiSystemButton"),
            ("🏠 SMART HOME", "aiSystemButton")
        ]
        
        for button_text, object_name in buttons:
            button = QPushButton(button_text)
            button.setObjectName(object_name)
            button.setCheckable(True)
            button.setChecked(True)
            button.setMinimumWidth(260)
            button.setMinimumHeight(55)
            button.setMaximumHeight(55)
            ai_layout.addWidget(button)
            ai_layout.addSpacing(5)
        
        layout.addWidget(ai_frame)
        
        # Apply basic styling
        widget.setStyleSheet("""
            QWidget {
                background-color: #000000;
                color: #00FFFF;
                font-family: 'Consolas', 'Monaco', monospace;
            }
            
            #hudPanelTitle {
                color: #00FFFF;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                text-align: center;
            }
            
            #aiSystemButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #003366, stop:1 #001122);
                border: 1px solid #00FFFF;
                border-radius: 8px;
                color: #00FFFF;
                font-size: 12px;
                font-weight: bold;
                padding: 8px;
                text-align: center;
                min-width: 260px;
                min-height: 55px;
                max-width: 260px;
                max-height: 55px;
            }
            
            #aiSystemButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FF00, stop:1 #00AA00);
                color: #000000;
            }
            
            #aiSystemButton:!checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF6600, stop:1 #CC4400);
                color: #FFFFFF;
            }
            
            #aiSystemButton:hover {
                border: 2px solid rgba(0, 255, 255, 0.8);
            }
        """)
        
        widget.show()
        print("✅ AI Systems Panel test widget created successfully")
        print("🎯 You should see a window with AI system buttons")
        print("📝 Check if all buttons are visible with proper styling")
        
        # Don't run the event loop in test mode
        return True
        
    except Exception as e:
        print(f"❌ AI Systems Panel test failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    print("🧪 Testing configuration loading...")
    
    try:
        config = Config.load_from_env()
        print(f"✅ Config loaded successfully")
        print(f"   - Model: {config.OLLAMA_MODEL}")
        print(f"   - Host: {config.OLLAMA_HOST}")
        print(f"   - Theme: {config.THEME_PRIMARY_COLOR}")
        return True
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def main():
    """Run all UI component tests"""
    print("🤖 JARVIS V6 UI Component Tests")
    print("=" * 40)
    
    tests = [
        ("Qt Imports", test_qt_imports),
        ("Configuration", test_config_loading),
        ("AI Systems Panel", test_ai_systems_panel)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 40)
    print("📊 Test Results Summary:")
    print("-" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! UI components should be working correctly.")
    else:
        print("⚠️ Some tests failed. Check the error messages above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
