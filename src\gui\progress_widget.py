"""
Progress Widget for JARVIS V6
=============================

A futuristic progress bar widget that shows current task progress with:
- Task name and description
- Progress percentage
- Estimated time remaining
- Animated progress bar with JARVIS-style design
"""

import time
from datetime import datetime, timedelta
from src.gui.qt_compat import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QProgressBar, QTimer, Qt, QPropertyAnimation, 
                               QEasingCurve, QGraphicsOpacityEffect)


class JarvisProgressWidget(QWidget):
    """JARVIS-style progress widget with task tracking"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_task = None
        self.start_time = None
        self.estimated_duration = 0
        self.is_active = False
        
        self.setup_ui()
        self.setup_animations()
        self.hide()  # Hidden by default
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_progress)
        self.update_timer.setInterval(100)  # Update every 100ms
    
    def setup_ui(self):
        """Setup the progress widget UI"""
        self.setFixedHeight(80)
        self.setMinimumWidth(350)
        self.setMaximumWidth(450)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(5)
        
        # Task info layout
        task_layout = QHBoxLayout()
        
        # Task name label
        self.task_label = QLabel("Initializing...")
        self.task_label.setObjectName("progressTaskLabel")
        task_layout.addWidget(self.task_label)
        
        task_layout.addStretch()
        
        # Percentage label
        self.percentage_label = QLabel("0%")
        self.percentage_label.setObjectName("progressPercentageLabel")
        task_layout.addWidget(self.percentage_label)
        
        layout.addLayout(task_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("jarvisProgressBar")
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Time info layout
        time_layout = QHBoxLayout()
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setObjectName("progressStatusLabel")
        time_layout.addWidget(self.status_label)
        
        time_layout.addStretch()
        
        # Time remaining label
        self.time_label = QLabel("--:--")
        self.time_label.setObjectName("progressTimeLabel")
        time_layout.addWidget(self.time_label)
        
        layout.addLayout(time_layout)
        
        # Apply JARVIS styling
        self.setStyleSheet("""
            JarvisProgressWidget {
                background-color: rgba(0, 20, 40, 0.9);
                border: 2px solid #00FF00;
                border-radius: 10px;
                color: #00FF00;
            }
            
            #progressTaskLabel {
                color: #00FF00;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Courier New', monospace;
            }
            
            #progressPercentageLabel {
                color: #00FFFF;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Courier New', monospace;
            }
            
            #progressStatusLabel {
                color: #CCCCCC;
                font-size: 10px;
                font-family: 'Courier New', monospace;
            }
            
            #progressTimeLabel {
                color: #FFFF00;
                font-size: 10px;
                font-family: 'Courier New', monospace;
            }
            
            #jarvisProgressBar {
                border: 1px solid #00FF00;
                border-radius: 5px;
                background-color: rgba(0, 0, 0, 0.5);
                height: 8px;
            }
            
            #jarvisProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00FF00, stop:0.5 #00FFFF, stop:1 #0080FF);
                border-radius: 4px;
            }
        """)
    
    def setup_animations(self):
        """Setup animations for the progress widget"""
        # Opacity effect for fade in/out
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        # Fade in animation
        self.fade_in_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Fade out animation
        self.fade_out_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_out_animation.setDuration(300)
        self.fade_out_animation.setStartValue(1.0)
        self.fade_out_animation.setEndValue(0.0)
        self.fade_out_animation.setEasingCurve(QEasingCurve.Type.InCubic)
        self.fade_out_animation.finished.connect(self.hide)
    
    def start_task(self, task_name: str, estimated_duration: float = 0, status: str = "Processing..."):
        """Start tracking a new task"""
        self.current_task = task_name
        self.estimated_duration = estimated_duration
        self.start_time = time.time()
        self.is_active = True
        
        # Update UI
        self.task_label.setText(task_name)
        self.status_label.setText(status)
        self.percentage_label.setText("0%")
        self.progress_bar.setValue(0)
        
        if estimated_duration > 0:
            self.time_label.setText(self.format_time(estimated_duration))
        else:
            self.time_label.setText("--:--")
        
        # Show widget with fade in
        self.show()
        self.fade_in_animation.start()
        
        # Start update timer
        self.update_timer.start()
        
        print(f"🔄 Progress: Started task '{task_name}' (est. {estimated_duration:.1f}s)")
    
    def update_task_progress(self, percentage: float, status: str = None):
        """Update task progress manually"""
        if not self.is_active:
            return
        
        # Clamp percentage between 0 and 100
        percentage = max(0, min(100, percentage))
        
        # Update progress bar and percentage
        self.progress_bar.setValue(int(percentage))
        self.percentage_label.setText(f"{percentage:.1f}%")
        
        # Update status if provided
        if status:
            self.status_label.setText(status)
        
        # Update time remaining
        if self.estimated_duration > 0 and percentage > 0:
            elapsed = time.time() - self.start_time
            if percentage < 100:
                estimated_total = elapsed * (100 / percentage)
                remaining = max(0, estimated_total - elapsed)
                self.time_label.setText(self.format_time(remaining))
            else:
                self.time_label.setText("00:00")
    
    def update_progress(self):
        """Auto-update progress based on elapsed time"""
        if not self.is_active or not self.start_time:
            return
        
        elapsed = time.time() - self.start_time
        
        if self.estimated_duration > 0:
            # Calculate progress based on estimated duration
            percentage = min(95, (elapsed / self.estimated_duration) * 100)
            remaining = max(0, self.estimated_duration - elapsed)
            
            self.progress_bar.setValue(int(percentage))
            self.percentage_label.setText(f"{percentage:.1f}%")
            self.time_label.setText(self.format_time(remaining))
            
            # Auto-complete if we've exceeded estimated time significantly
            if elapsed > self.estimated_duration * 1.5:
                self.complete_task("Completed")
        else:
            # Indeterminate progress - just show elapsed time
            self.time_label.setText(self.format_time(elapsed))
    
    def complete_task(self, final_status: str = "Completed"):
        """Complete the current task"""
        if not self.is_active:
            return
        
        self.is_active = False
        self.update_timer.stop()
        
        # Set to 100% completion
        self.progress_bar.setValue(100)
        self.percentage_label.setText("100%")
        self.status_label.setText(final_status)
        self.time_label.setText("00:00")
        
        print(f"✅ Progress: Completed task '{self.current_task}'")
        
        # Hide after a short delay
        QTimer.singleShot(2000, self.hide_with_fade)
    
    def cancel_task(self, reason: str = "Cancelled"):
        """Cancel the current task"""
        if not self.is_active:
            return
        
        self.is_active = False
        self.update_timer.stop()
        
        self.status_label.setText(reason)
        
        print(f"❌ Progress: Cancelled task '{self.current_task}' - {reason}")
        
        # Hide after a short delay
        QTimer.singleShot(1500, self.hide_with_fade)
    
    def hide_with_fade(self):
        """Hide the widget with fade out animation"""
        if self.isVisible():
            self.fade_out_animation.start()
    
    def format_time(self, seconds: float) -> str:
        """Format time in MM:SS format"""
        if seconds < 0:
            return "00:00"
        
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        
        if minutes > 99:
            return "99:59"
        
        return f"{minutes:02d}:{seconds:02d}"
    
    def set_indeterminate(self, task_name: str, status: str = "Processing..."):
        """Set progress to indeterminate mode (no estimated time)"""
        self.start_task(task_name, 0, status)
        
        # Set progress bar to indeterminate style
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(0)  # Indeterminate mode
        self.percentage_label.setText("...")
    
    def set_determinate(self, estimated_duration: float):
        """Switch back to determinate mode"""
        self.estimated_duration = estimated_duration
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.percentage_label.setText("0%")
    
    def get_current_task(self) -> str:
        """Get the current task name"""
        return self.current_task if self.is_active else None
    
    def is_task_active(self) -> bool:
        """Check if a task is currently active"""
        return self.is_active
