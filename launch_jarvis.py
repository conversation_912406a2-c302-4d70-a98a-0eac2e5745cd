#!/usr/bin/env python3
"""
JARVIS V6 Launcher
Alternative launcher to work around PyQt6 import issues
"""

import sys
import os

def main():
    """Launch JARVIS V6 with proper error handling"""
    print("🚀 Starting JARVIS V6 AI Assistant...")
    print("=" * 50)

    try:
        # Test Qt compatibility layer
        print("Loading Qt compatibility layer...")
        from src.gui.qt_compat import QApplication
        print("✅ Qt compatibility layer loaded successfully")

        # Test our modules
        print("Loading JARVIS modules...")
        from src.gui.main_window import JarvisMainWindow
        print("✅ JARVIS modules loaded successfully")

        # Create application
        print("Creating application...")
        app = QApplication(sys.argv)
        app.setApplicationName("Jarvis V6")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("Jarvis AI")
        print("✅ Application created")

        # Create main window
        print("Initializing JARVIS interface...")
        window = JarvisMainWindow()
        window.show()
        print("✅ JARVIS interface ready!")

        print("\n🎉 JARVIS V6 is now running!")
        print("Features available:")
        print("  • Futuristic JARVIS HUD interface")
        print("  • Animated AI core visualization")
        print("  • Real-time system monitoring")
        print("  • ElevenLabs TTS voice output")
        print("  • Mixtral 8x7B AI chat")
        print("\nEnjoy your AI assistant! 🤖")

        # Start the application
        sys.exit(app.exec())

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Try: pip install PySide6")
        print("3. Check Python version (3.8+ required)")
        return 1

    except Exception as e:
        print(f"❌ Error starting JARVIS: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    if exit_code:
        input("\nPress Enter to exit...")
    sys.exit(exit_code)
