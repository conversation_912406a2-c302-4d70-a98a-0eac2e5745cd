#!/usr/bin/env python3
"""
Test Advanced AI Systems for JARVIS V6
======================================

Comprehensive test of all advanced AI systems:
- Enhanced Training System
- Advanced Memory System  
- Self-Evolution System
- Knowledge Base
- Function Registry
"""

import sys
import asyncio
import time

def test_enhanced_training_system():
    """Test the enhanced training system"""
    print("🧠 Testing Enhanced Training System...")
    
    try:
        from src.core.config import Config
        from src.ai.training_system import TrainingSystem
        
        config = Config.load_from_env()
        training = TrainingSystem(config)
        
        # Start a training session
        session_id = training.start_training_session(duration_minutes=5, session_name="Test Session")
        print(f"✅ Started training session: {session_id}")
        
        # Test enhanced feedback processing
        training.process_feedback(
            "Hello JARVIS",
            "Hello! How can I assist you today?",
            "positive",
            5
        )
        
        training.process_feedback(
            "What's the weather?",
            "I don't have access to weather data.",
            "neutral",
            3
        )
        
        # Get training recommendations
        recommendations = training.get_training_recommendations()
        print(f"✅ Training recommendations: {len(recommendations)} suggestions")
        for rec in recommendations[:2]:
            print(f"   - {rec}")
        
        # Get enhanced stats
        stats = training.get_training_stats()
        print(f"✅ Enhanced Training: {stats['conversations_recorded']} conversations")
        print(f"   User satisfaction: {stats['user_satisfaction']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced Training Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_memory_system():
    """Test the advanced memory system"""
    print("🧠 Testing Advanced Memory System...")
    
    try:
        from src.ai.advanced_memory import AdvancedMemorySystem
        
        memory = AdvancedMemorySystem()
        
        # Store different types of memories
        memory.store_memory(
            "User prefers concise responses",
            memory_type="semantic",
            importance=0.8,
            emotional_valence=0.2,
            tags=["user_preference", "communication"],
            context={"learned_from": "conversation_pattern"}
        )
        
        memory.store_memory(
            "Had a great conversation about AI",
            memory_type="episodic",
            importance=0.6,
            emotional_valence=0.7,
            tags=["conversation", "AI", "positive"],
            context={"topic": "artificial_intelligence"}
        )
        
        # Test emotional analysis
        emotional_context = memory.analyze_emotional_context(
            "I love this new AI system! It's amazing and wonderful!"
        )
        print(f"✅ Emotional analysis: valence={emotional_context['valence']:.2f}")
        
        # Test memory recall
        memories = memory.recall_memories("AI conversation", limit=5)
        print(f"✅ Memory recall: Found {len(memories)} relevant memories")
        
        # Test contextual summary
        summary = memory.get_contextual_summary(limit=3)
        print(f"✅ Contextual summary: {len(summary)} characters")
        
        # Update user preferences
        memory.update_user_preferences({
            "response_style": "concise",
            "topics_of_interest": ["AI", "technology", "programming"]
        })
        
        # Get memory stats
        stats = memory.get_memory_stats()
        print(f"✅ Advanced Memory: {stats['total_memories']} memories stored")
        print(f"   Memory types: {list(stats['memory_types'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced Memory Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_self_evolution_system():
    """Test the self-evolution system"""
    print("🔬 Testing Self-Evolution System...")
    
    try:
        from src.core.config import Config
        from src.ai.self_evolution import SelfEvolutionSystem
        
        config = Config.load_from_env()
        evolution = SelfEvolutionSystem(config)
        
        # Test code analysis
        test_file = "src/ai/ollama_client.py"
        if os.path.exists(test_file):
            analysis = evolution.analyze_code_file(test_file)
            if analysis:
                print(f"✅ Code analysis for {test_file}:")
                print(f"   Complexity: {analysis.complexity_score:.2f}")
                print(f"   Performance: {analysis.performance_score:.2f}")
                print(f"   Maintainability: {analysis.maintainability_score:.2f}")
                print(f"   Risk level: {analysis.risk_level}")
                print(f"   Suggestions: {len(analysis.suggestions)}")
        
        # Create evolution tasks
        task_id = evolution.create_evolution_task(
            task_type="optimization",
            description="Optimize response generation speed",
            priority=7,
            complexity=5
        )
        print(f"✅ Created evolution task: {task_id}")
        
        # Get evolution stats
        stats = evolution.get_evolution_stats()
        print(f"✅ Self-Evolution: {stats['total_tasks']} tasks created")
        print(f"   Evolution enabled: {stats['evolution_enabled']}")
        print(f"   Safety mode: {stats['safety_mode']}")
        print(f"   Success rate: {stats['success_rate']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ Self-Evolution Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_systems():
    """Test integration between systems"""
    print("🔗 Testing System Integration...")
    
    try:
        from src.core.config import Config
        from src.ai.training_system import TrainingSystem
        from src.ai.advanced_memory import AdvancedMemorySystem
        from src.ai.knowledge_base import KnowledgeBase
        
        config = Config.load_from_env()
        
        # Initialize systems
        training = TrainingSystem(config)
        memory = AdvancedMemorySystem()
        knowledge = KnowledgeBase()
        
        # Simulate a conversation flow
        user_input = "Tell me about machine learning"
        ai_response = "Machine learning is a subset of AI that enables systems to learn from data."
        
        # 1. Store in training system
        training.record_conversation(user_input, ai_response)
        
        # 2. Store in advanced memory with emotional context
        emotional_context = memory.analyze_emotional_context(user_input + " " + ai_response)
        memory.store_memory(
            f"User: {user_input}\nJARVIS: {ai_response}",
            memory_type="episodic",
            importance=0.7,
            emotional_valence=emotional_context['valence'],
            tags=["conversation", "machine_learning"],
            context={"topic": "education"}
        )
        
        # 3. Learn knowledge
        knowledge.learn_from_conversation(user_input, ai_response)
        
        # 4. Test retrieval integration
        relevant_context = knowledge.get_relevant_context("machine learning")
        memories = memory.recall_memories("machine learning", limit=3)
        
        print(f"✅ Integration test completed:")
        print(f"   Knowledge context: {len(relevant_context)} characters")
        print(f"   Related memories: {len(memories)} found")
        print(f"   Emotional valence: {emotional_context['valence']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all advanced AI system tests"""
    print("🚀 Testing JARVIS V6 Advanced AI Systems")
    print("=" * 60)
    
    results = []
    
    # Test each system
    print("\n1. Enhanced Training System")
    print("-" * 30)
    results.append(test_enhanced_training_system())
    
    print("\n2. Advanced Memory System")
    print("-" * 30)
    results.append(test_advanced_memory_system())
    
    print("\n3. Self-Evolution System")
    print("-" * 30)
    results.append(test_self_evolution_system())
    
    print("\n4. System Integration")
    print("-" * 30)
    results.append(test_integrated_systems())
    
    # Summary
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 ALL {total} ADVANCED AI SYSTEMS ARE WORKING!")
        print("\n🤖 JARVIS V6 Enhanced Features:")
        print("• 🧠 Enhanced Training with session management")
        print("• 🧠 Advanced Memory with emotional intelligence")
        print("• 🔬 Self-Evolution with code analysis")
        print("• 📚 Integrated Knowledge Base")
        print("• ⚙️ Dynamic Function Registry")
        print("• 🔗 Seamless system integration")
        print("\n✨ Your AI assistant is now significantly more intelligent!")
        print("   Toggle these systems in the JARVIS interface for full control.")
    else:
        print(f"⚠️ {passed}/{total} systems working.")
        print("Some advanced features may not be available.")
    
    return passed == total

if __name__ == "__main__":
    import os
    success = asyncio.run(main())
    
    if success:
        print("\n🎯 Ready to launch enhanced JARVIS V6!")
        print("Run: python jarvis_clean.py")
    
    sys.exit(0 if success else 1)
