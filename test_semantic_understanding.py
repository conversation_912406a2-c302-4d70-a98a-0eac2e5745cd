#!/usr/bin/env python3
"""
Test Semantic Understanding System for JARVIS V6
===============================================

Test the new semantic understanding and autonomous action capabilities.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai.semantic_understanding import SemanticUnderstandingSystem, SemanticIntent
from ai.advanced_memory import AdvancedMemorySystem

async def test_semantic_understanding():
    """Test the semantic understanding system"""
    print("🧠 Testing JARVIS Semantic Understanding System")
    print("=" * 60)
    
    # Initialize systems
    memory = AdvancedMemorySystem()
    semantic_system = SemanticUnderstandingSystem(memory_system=memory)
    
    print(f"✅ Semantic system initialized")
    print(f"🎯 Autonomous capabilities: {list(semantic_system.autonomous_capabilities.keys())}")
    print()
    
    # Test semantic understanding with various requests
    test_requests = [
        {
            "message": "make yourself more autonomous",
            "expected_intent": SemanticIntent.AUTONOMOUS_IMPROVEMENT,
            "description": "Request for enhanced autonomy"
        },
        {
            "message": "improve your intelligence and become smarter",
            "expected_intent": SemanticIntent.AUTONOMOUS_IMPROVEMENT,
            "description": "Intelligence enhancement request"
        },
        {
            "message": "change your behavior to be more helpful",
            "expected_intent": SemanticIntent.BEHAVIOR_ADJUSTMENT,
            "description": "Behavior modification request"
        },
        {
            "message": "learn to respond better and adapt to my style",
            "expected_intent": SemanticIntent.LEARNING_REQUEST,
            "description": "Learning and adaptation request"
        },
        {
            "message": "enhance your capabilities and add new features",
            "expected_intent": SemanticIntent.CAPABILITY_ENHANCEMENT,
            "description": "Capability enhancement request"
        },
        {
            "message": "be more proactive and take initiative",
            "expected_intent": SemanticIntent.AUTONOMOUS_IMPROVEMENT,
            "description": "Proactive behavior request"
        },
        {
            "message": "remember that I prefer short responses",
            "expected_intent": SemanticIntent.LEARNING_REQUEST,
            "description": "Preference learning request"
        }
    ]
    
    print("🎯 Testing Semantic Intent Recognition...")
    print("-" * 50)
    
    for i, test in enumerate(test_requests, 1):
        print(f"\n{i}. {test['description']}")
        print(f"   Input: '{test['message']}'")
        
        # Analyze semantic intent
        analysis = await semantic_system.analyze_semantic_intent(test['message'])
        
        print(f"   🧠 Detected Intent: {analysis.intent.value}")
        print(f"   📊 Confidence: {analysis.confidence:.2f}")
        print(f"   💭 Concepts: {', '.join(analysis.extracted_concepts)}")
        print(f"   🎯 Expected: {test['expected_intent'].value}")
        
        # Check if intent matches expectation
        if analysis.intent == test['expected_intent']:
            print(f"   ✅ Intent recognition: CORRECT")
        else:
            print(f"   ⚠️ Intent recognition: DIFFERENT (but may still be valid)")
        
        print(f"   📝 Reasoning: {analysis.reasoning}")
        
        if analysis.action_plan:
            print(f"   🤖 Planned Actions: {len(analysis.action_plan)}")
            for action in analysis.action_plan:
                print(f"      • {action.description} (confidence: {action.confidence:.2f})")
    
    print("\n" + "=" * 60)
    print("🤖 Testing Autonomous Action Execution...")
    print("-" * 50)
    
    # Test autonomous action execution
    test_message = "make yourself more autonomous and intelligent"
    print(f"\nExecuting autonomous actions for: '{test_message}'")
    
    # Analyze and execute
    analysis = await semantic_system.analyze_semantic_intent(test_message)
    print(f"🧠 Analysis complete - Intent: {analysis.intent.value} (confidence: {analysis.confidence:.2f})")
    
    if analysis.action_plan:
        print(f"🤖 Executing {len(analysis.action_plan)} autonomous actions...")
        
        results = await semantic_system.execute_autonomous_actions(analysis, test_message)
        
        print(f"\n📊 Execution Results:")
        print(f"   ✅ Successful actions: {results['success_count']}")
        print(f"   ⚠️ Skipped actions: {len(results['actions_skipped'])}")
        print(f"   ❌ Failed actions: {results['error_count']}")
        print(f"   ⏱️ Total time: {results['total_time']:.2f} seconds")
        
        if results['actions_taken']:
            print(f"\n🔧 Actions Executed:")
            for action in results['actions_taken']:
                print(f"   • {action['description']}")
                print(f"     Result: {action['result']}")
                print(f"     Time: {action['time']:.2f}s")
        
        if results['actions_skipped']:
            print(f"\n⚠️ Actions Skipped:")
            for action in results['actions_skipped']:
                print(f"   • {action['description']}: {action['reason']}")
    
    print("\n" + "=" * 60)
    print("📈 Testing Learning and Adaptation...")
    print("-" * 50)
    
    # Test learning capabilities
    learning_requests = [
        "remember that I prefer technical explanations",
        "learn that I like detailed responses",
        "note that I work better with step-by-step instructions"
    ]
    
    for request in learning_requests:
        print(f"\n📚 Learning request: '{request}'")
        analysis = await semantic_system.analyze_semantic_intent(request)
        
        if analysis.action_plan:
            results = await semantic_system.execute_autonomous_actions(analysis, request)
            print(f"   ✅ Learning completed: {results['success_count']} actions executed")
    
    print("\n" + "=" * 60)
    print("🎭 Testing Behavior Adaptation...")
    print("-" * 50)
    
    # Test behavior adaptation
    behavior_requests = [
        "be more helpful and proactive",
        "change your communication style to be more casual",
        "adapt your responses to be more concise"
    ]
    
    for request in behavior_requests:
        print(f"\n🎭 Behavior request: '{request}'")
        analysis = await semantic_system.analyze_semantic_intent(request)
        
        if analysis.action_plan:
            results = await semantic_system.execute_autonomous_actions(analysis, request)
            print(f"   ✅ Behavior adapted: {results['success_count']} actions executed")
    
    print("\n" + "=" * 60)
    print("📊 Final System Status...")
    print("-" * 50)
    
    # Get final autonomy status
    status = semantic_system.get_autonomy_status()
    
    print(f"🧠 Learning Patterns Active: {len(status['learning_patterns'])}")
    for pattern, config in status['learning_patterns'].items():
        print(f"   • {pattern}: {config}")
    
    print(f"\n🤖 Action History: {status['action_history_count']} actions recorded")
    print(f"⚡ Proactive Mode: {'ENABLED' if status['proactive_mode'] else 'DISABLED'}")
    
    if status['last_action']:
        last_action = status['last_action']
        print(f"🕒 Last Action: {last_action['timestamp']}")
        print(f"   Message: {last_action['user_message'][:50]}...")
        print(f"   Results: {last_action['results']['success_count']} successful")
    
    print("\n🎉 Semantic Understanding Test Complete!")
    print("=" * 60)
    
    return semantic_system

async def test_real_world_scenarios():
    """Test real-world semantic understanding scenarios"""
    print("\n🌍 Testing Real-World Scenarios...")
    print("=" * 50)
    
    memory = AdvancedMemorySystem()
    semantic_system = SemanticUnderstandingSystem(memory_system=memory)
    
    real_world_tests = [
        "JARVIS, I want you to be more independent and make decisions on your own",
        "Can you improve yourself and become better at understanding what I need?",
        "Please learn my preferences and adapt to how I like to communicate",
        "Make yourself smarter and more capable of helping me",
        "I need you to be more proactive and anticipate what I might need",
        "Enhance your intelligence and become more autonomous",
        "Learn to be more helpful and responsive to my needs"
    ]
    
    for i, test_message in enumerate(real_world_tests, 1):
        print(f"\n{i}. Real-world test: '{test_message}'")
        
        # Full semantic processing
        analysis = await semantic_system.analyze_semantic_intent(test_message)
        
        print(f"   🧠 Intent: {analysis.intent.value} (confidence: {analysis.confidence:.2f})")
        print(f"   💭 Concepts: {', '.join(analysis.extracted_concepts)}")
        
        if analysis.action_plan and analysis.confidence > 0.6:
            results = await semantic_system.execute_autonomous_actions(analysis, test_message)
            print(f"   🤖 Executed {results['success_count']} autonomous actions")
            
            # Show key results
            for action in results['actions_taken'][:2]:  # Show first 2 actions
                print(f"      • {action['description']}")
        else:
            print(f"   ⚠️ Low confidence or no actions planned")
    
    print(f"\n✅ Real-world scenario testing complete!")

if __name__ == "__main__":
    try:
        # Test semantic understanding
        semantic_system = asyncio.run(test_semantic_understanding())
        
        # Test real-world scenarios
        asyncio.run(test_real_world_scenarios())
        
        print(f"\n🎊 All tests completed successfully!")
        print(f"🧠 JARVIS now has advanced semantic understanding and autonomous capabilities!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
