#!/usr/bin/env python3
"""
Test Ollama Connection and Model Availability
"""

import requests
import json
import time

def test_ollama_connection():
    """Test if Ollama server is running and accessible"""
    print("🔍 Testing Ollama Connection...")
    print("=" * 50)
    
    base_url = "http://localhost:11434"
    
    # Test 1: Check if server is running
    print("1. Testing server connectivity...")
    try:
        response = requests.get(f"{base_url}/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama server is running")
            models = response.json()
            print(f"📋 Available models: {len(models.get('models', []))}")
            
            # List available models
            for model in models.get('models', []):
                print(f"   - {model.get('name', 'Unknown')}")
                
        else:
            print(f"❌ Server responded with status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama server")
        print("💡 Make sure Ollama is running: ollama serve")
        return False
    except requests.exceptions.Timeout:
        print("❌ Connection timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Test 2: Check if Mixtral model is available
    print("\n2. Testing Mixtral 8x7B model...")
    try:
        models = response.json().get('models', [])
        mixtral_available = any('mixtral' in model.get('name', '').lower() for model in models)
        
        if mixtral_available:
            print("✅ Mixtral model found")
        else:
            print("❌ Mixtral model not found")
            print("💡 Install it with: ollama pull mixtral:8x7b")
            return False
            
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False
    
    # Test 3: Test actual generation
    print("\n3. Testing response generation...")
    try:
        test_prompt = "Hello, can you hear me?"
        
        payload = {
            "model": "mixtral:8x7b",
            "prompt": test_prompt,
            "stream": False,
            "options": {
                "num_predict": 50,
                "temperature": 0.7
            }
        }
        
        print(f"📤 Sending test prompt: '{test_prompt}'")
        start_time = time.time()
        
        response = requests.post(
            f"{base_url}/api/generate",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get("response", "")
            print(f"✅ Response received in {duration:.2f} seconds")
            print(f"📥 AI Response: {ai_response[:100]}...")
            return True
        else:
            print(f"❌ Generation failed with status: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Generation timed out (120 seconds)")
        print("💡 The model might be loading or the server is busy")
        return False
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return False

def main():
    """Main test function"""
    print("🤖 JARVIS V6 - Ollama Connection Test")
    print("=" * 50)
    
    success = test_ollama_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! Ollama is ready for JARVIS V6")
    else:
        print("❌ Tests failed. Please fix the issues above.")
        print("\n💡 Quick fixes:")
        print("   1. Start Ollama: ollama serve")
        print("   2. Install Mixtral: ollama pull mixtral:8x7b")
        print("   3. Check firewall/antivirus settings")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
