"""
Input Widget for Jarvis V6
Handles user input with futuristic styling and features
"""

from src.gui.qt_compat import (QWidget, QHBoxLayout, QVBoxLayout, QLineEdit, QPushButton, QLabel,
                               Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QFont,
                               QListWidget, QListWidgetItem, QFrame)
try:
    from src.gui.qt_compat import Q<PERSON>eySequence, QShortcut
except ImportError:
    # Handle different import paths between PyQt6 and PySide6
    try:
        from PyQt6.QtGui import QKeySequence, QShortcut
    except ImportError:
        from PySide6.QtGui import <PERSON><PERSON>eySequence, QShortcut
from src.core.config import Config
from src.ai.input_enhancement_system import InputEnhancementSystem, InputType, IntentConfidence

class InputWidget(QWidget):
    """Widget for user input with send button and shortcuts"""

    message_sent = pyqtSignal(str)
    special_command = pyqtSignal(str)
    
    def __init__(self, config: Config, parent=None):
        super().__init__(parent)
        self.config = config
        self.command_history = []
        self.history_index = -1

        # Initialize input enhancement system
        self.input_enhancer = InputEnhancementSystem(config)
        self.suggestions_visible = False
        self.current_suggestions = []

        self.init_ui()
        self.setup_styling()
        self.setup_connections()
        self.setup_shortcuts()
        self.setup_auto_completion()
        
    def init_ui(self):
        """Initialize the user interface"""
        # Main layout (vertical to accommodate suggestions)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)

        # Input row layout
        input_layout = QHBoxLayout()
        input_layout.setSpacing(10)
        
        # Input prompt label
        self.prompt_label = QLabel("►")
        self.prompt_label.setObjectName("promptLabel")
        self.prompt_label.setFixedWidth(20)
        
        # Input field
        self.input_field = QLineEdit()
        self.input_field.setObjectName("inputField")
        self.input_field.setPlaceholderText("Enter your message here...")
        self.input_field.setMinimumHeight(40)
        
        # Send button
        self.send_button = QPushButton("SEND")
        self.send_button.setObjectName("sendButton")
        self.send_button.setFixedSize(80, 40)
        self.send_button.setEnabled(False)  # Disabled until text is entered
        
        # Clear button
        self.clear_button = QPushButton("CLEAR")
        self.clear_button.setObjectName("clearButton")
        self.clear_button.setFixedSize(80, 40)
        
        # Status label for shortcuts/hints
        self.status_label = QLabel("Press Enter to send • Ctrl+L to clear • ↑↓ for history")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        
        # Add widgets to input layout
        input_layout.addWidget(self.prompt_label)
        input_layout.addWidget(self.input_field, 1)  # Give it stretch factor
        input_layout.addWidget(self.send_button)
        input_layout.addWidget(self.clear_button)

        # Add input layout to main layout
        main_layout.addLayout(input_layout)

        # Create suggestions widget (initially hidden)
        self.suggestions_widget = QListWidget()
        self.suggestions_widget.setObjectName("suggestionsWidget")
        self.suggestions_widget.setMaximumHeight(120)
        self.suggestions_widget.setVisible(False)
        main_layout.addWidget(self.suggestions_widget)

        # Status label
        main_layout.addWidget(self.status_label)

        self.setLayout(main_layout)
        
    def setup_styling(self):
        """Setup the HUD input widget styling"""
        self.setStyleSheet(f"""
            #promptLabel {{
                color: {self.config.THEME_PRIMARY_COLOR};
                font-size: 20px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
            }}

            #inputField {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 30, 60, 0.8),
                    stop:1 rgba(0, 20, 40, 0.9));
                border: 2px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 12px;
                color: {self.config.THEME_TEXT_COLOR};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                padding: 12px 16px;
                selection-background-color: {self.config.THEME_SECONDARY_COLOR};
            }}

            #inputField:focus {{
                border-color: {self.config.THEME_SECONDARY_COLOR};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 50, 100, 0.8),
                    stop:1 rgba(0, 30, 60, 0.9));
            }}
            
            #sendButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.config.THEME_PRIMARY_COLOR},
                    stop:1 {self.config.THEME_SECONDARY_COLOR});
                border: none;
                border-radius: 8px;
                color: {self.config.THEME_BACKGROUND_COLOR};
                font-weight: bold;
                font-size: 12px;
            }}
            
            #sendButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.config.THEME_SECONDARY_COLOR},
                    stop:1 {self.config.THEME_PRIMARY_COLOR});
            }}
            
            #sendButton:pressed {{
                background-color: {self.config.THEME_ACCENT_COLOR};
            }}
            
            #sendButton:disabled {{
                background-color: rgba(128, 128, 128, 0.3);
                color: rgba(255, 255, 255, 0.5);
            }}
            
            #clearButton {{
                background-color: rgba(255, 102, 0, 0.8);
                border: 1px solid {self.config.THEME_ACCENT_COLOR};
                border-radius: 8px;
                color: {self.config.THEME_TEXT_COLOR};
                font-weight: bold;
                font-size: 12px;
            }}
            
            #clearButton:hover {{
                background-color: {self.config.THEME_ACCENT_COLOR};
            }}
            
            #clearButton:pressed {{
                background-color: rgba(255, 102, 0, 1.0);
            }}
            
            #statusLabel {{
                color: rgba(255, 255, 255, 0.6);
                font-size: 10px;
                font-style: italic;
            }}

            #suggestionsWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 20, 40, 0.95),
                    stop:1 rgba(0, 30, 60, 0.95));
                border: 1px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 8px;
                color: {self.config.THEME_TEXT_COLOR};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                selection-background-color: {self.config.THEME_SECONDARY_COLOR};
                outline: none;
            }}

            #suggestionsWidget::item {{
                padding: 8px 12px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }}

            #suggestionsWidget::item:hover {{
                background-color: rgba(0, 150, 255, 0.3);
            }}

            #suggestionsWidget::item:selected {{
                background-color: {self.config.THEME_SECONDARY_COLOR};
                color: {self.config.THEME_BACKGROUND_COLOR};
            }}
        """)
        
    def setup_connections(self):
        """Setup signal connections"""
        self.input_field.textChanged.connect(self.on_text_changed)
        self.input_field.returnPressed.connect(self.send_message)
        self.send_button.clicked.connect(self.send_message)
        self.clear_button.clicked.connect(self.clear_input)

        # Suggestions widget connections
        self.suggestions_widget.itemClicked.connect(self.on_suggestion_selected)
        self.suggestions_widget.itemActivated.connect(self.on_suggestion_selected)
        
    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        # Clear shortcut (Ctrl+L)
        clear_shortcut = QShortcut(QKeySequence("Ctrl+L"), self)
        clear_shortcut.activated.connect(self.clear_input)
        
        # History navigation and enhanced key handling
        self.input_field.keyPressEvent = self.handle_key_press

        # Auto-completion shortcut (Ctrl+Space)
        auto_complete_shortcut = QShortcut(QKeySequence("Ctrl+Space"), self)
        auto_complete_shortcut.activated.connect(self.show_suggestions)
        
    def handle_key_press(self, event):
        """Handle special key presses for enhanced input features"""
        if self.suggestions_visible:
            # Handle suggestion navigation
            if event.key() == Qt.Key.Key_Up:
                current_row = self.suggestions_widget.currentRow()
                if current_row > 0:
                    self.suggestions_widget.setCurrentRow(current_row - 1)
                return
            elif event.key() == Qt.Key.Key_Down:
                current_row = self.suggestions_widget.currentRow()
                if current_row < self.suggestions_widget.count() - 1:
                    self.suggestions_widget.setCurrentRow(current_row + 1)
                return
            elif event.key() == Qt.Key.Key_Tab:
                # Accept current suggestion
                current_item = self.suggestions_widget.currentItem()
                if current_item:
                    self.on_suggestion_selected(current_item)
                return
            elif event.key() == Qt.Key.Key_Escape:
                # Hide suggestions
                self.hide_suggestions()
                return
        else:
            # Normal history navigation when suggestions not visible
            if event.key() == Qt.Key.Key_Up:
                self.navigate_history(-1)
                return
            elif event.key() == Qt.Key.Key_Down:
                self.navigate_history(1)
                return

        # Call the original keyPressEvent
        QLineEdit.keyPressEvent(self.input_field, event)
            
    def navigate_history(self, direction: int):
        """Navigate through command history"""
        if not self.command_history:
            return
            
        self.history_index += direction
        
        # Clamp index to valid range
        if self.history_index < 0:
            self.history_index = 0
        elif self.history_index >= len(self.command_history):
            self.history_index = len(self.command_history) - 1
            
        # Set the input field text
        if 0 <= self.history_index < len(self.command_history):
            self.input_field.setText(self.command_history[self.history_index])
            
    def on_text_changed(self, text: str):
        """Handle text change in input field with enhanced features"""
        # Enable/disable send button based on text content
        self.send_button.setEnabled(bool(text.strip()))

        # Reset history index when user types
        if text and self.history_index != -1:
            self.history_index = -1

        # Show auto-completion suggestions
        if len(text) >= 2:
            self.update_suggestions(text)
        else:
            self.hide_suggestions()

        # Update status with input analysis
        self.update_input_status(text)
            
    def send_message(self):
        """Send the current message with enhanced processing and analysis"""
        message = self.input_field.text().strip()
        if not message:
            return

        # Hide suggestions if visible
        self.hide_suggestions()

        # Analyze input before sending
        try:
            analysis = self.input_enhancer.analyze_input(message)
            print(f"📝 Input Analysis: Type={analysis.input_type.value}, Intent={analysis.intent}, Confidence={analysis.confidence.value}")

            # Learn from this input
            self.input_enhancer.learn_from_input(message, 'sent')

        except Exception as e:
            print(f"Error analyzing input: {e}")

        # Check for special commands
        if message.lower() in ['/status', '/systems', '/ai-status']:
            self.input_field.clear()
            self.special_command.emit('show_ai_status')
            return
        elif message.lower() in ['/memory', '/memories']:
            self.input_field.clear()
            self.special_command.emit('show_memory_status')
            return
        elif message.lower() in ['/evolution', '/evolve']:
            self.input_field.clear()
            self.special_command.emit('show_evolution_status')
            return
        elif message.lower() in ['/help', '/commands']:
            self.input_field.clear()
            self.special_command.emit('show_help')
            return
        elif message.lower() in ['/training', '/learn']:
            self.input_field.clear()
            self.special_command.emit('show_training_status')
            return
        elif message.lower() in ['/suggestions', '/stats']:
            self.input_field.clear()
            self.show_input_statistics()
            return

        # Add to command history
        if message not in self.command_history:
            self.command_history.append(message)

        # Limit history size
        if len(self.command_history) > 50:
            self.command_history = self.command_history[-50:]

        # Reset history index
        self.history_index = -1

        # Clear input and emit signal
        self.input_field.clear()
        self.message_sent.emit(message)

        # Animate send button
        self.animate_send_button()

        # Reset status
        self.status_label.setText("Press Enter to send • Ctrl+L to clear • ↑↓ for history • Ctrl+Space for suggestions")
        
    def animate_send_button(self):
        """Animate the send button when clicked"""
        self.send_animation = QPropertyAnimation(self.send_button, b"geometry")
        self.send_animation.setDuration(200)
        
        # Get current geometry
        current_geo = self.send_button.geometry()
        
        # Create slightly smaller geometry for "press" effect
        pressed_geo = current_geo.adjusted(2, 2, -2, -2)
        
        # Set animation values
        self.send_animation.setStartValue(current_geo)
        self.send_animation.setKeyValueAt(0.5, pressed_geo)
        self.send_animation.setEndValue(current_geo)
        self.send_animation.setEasingCurve(QEasingCurve.Type.OutBounce)
        
        self.send_animation.start()
        
    def clear_input(self):
        """Clear the input field"""
        self.input_field.clear()
        self.input_field.setFocus()
        
        # Animate clear button
        self.animate_clear_button()
        
    def animate_clear_button(self):
        """Animate the clear button when clicked"""
        self.clear_animation = QPropertyAnimation(self.clear_button, b"styleSheet")
        self.clear_animation.setDuration(300)
        
        # Flash effect
        normal_style = self.clear_button.styleSheet()
        flash_style = normal_style.replace("rgba(255, 102, 0, 0.8)", "rgba(255, 102, 0, 1.0)")
        
        self.clear_animation.setStartValue(normal_style)
        self.clear_animation.setKeyValueAt(0.5, flash_style)
        self.clear_animation.setEndValue(normal_style)
        
        self.clear_animation.start()
        
    def set_placeholder_text(self, text: str):
        """Set placeholder text for input field"""
        self.input_field.setPlaceholderText(text)
        
    def get_current_text(self) -> str:
        """Get current text in input field"""
        return self.input_field.text()
        
    def set_focus_to_input(self):
        """Set focus to the input field"""
        self.input_field.setFocus()
        
    def disable_input(self):
        """Disable input while processing"""
        self.input_field.setEnabled(False)
        self.send_button.setEnabled(False)
        self.status_label.setText("Processing...")
        
    def enable_input(self):
        """Re-enable input after processing"""
        self.input_field.setEnabled(True)
        self.send_button.setEnabled(bool(self.input_field.text().strip()))
        self.status_label.setText("Press Enter to send • Ctrl+L to clear • ↑↓ for history • Ctrl+Space for suggestions")

    def setup_auto_completion(self):
        """Setup auto-completion system"""
        # We use our custom suggestions widget instead of QCompleter
        # since QCompleter is not available in qt_compat
        print("🎯 Custom auto-completion system ready")

    def update_suggestions(self, text: str):
        """Update auto-completion suggestions based on current text"""
        try:
            # Get suggestions from the enhancement system
            suggestions = self.input_enhancer.get_auto_completions(text, limit=8)

            if suggestions:
                self.current_suggestions = suggestions
                self.show_suggestions_list(suggestions)
            else:
                self.hide_suggestions()
        except Exception as e:
            print(f"Error updating suggestions: {e}")

    def show_suggestions_list(self, suggestions):
        """Show the suggestions list widget"""
        self.suggestions_widget.clear()

        for suggestion in suggestions:
            item = QListWidgetItem()
            # Format suggestion with type and description
            display_text = f"{suggestion.text}"
            if suggestion.description:
                display_text += f" - {suggestion.description}"

            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, suggestion.text)  # Store actual text

            # Color code by type
            if suggestion.type == 'system':
                item.setForeground(self.config.THEME_PRIMARY_COLOR)
            elif suggestion.type == 'code':
                item.setForeground(self.config.THEME_SECONDARY_COLOR)
            elif suggestion.type == 'agents':
                item.setForeground(self.config.THEME_ACCENT_COLOR)

            self.suggestions_widget.addItem(item)

        self.suggestions_widget.setVisible(True)
        self.suggestions_visible = True

        # Select first item
        if self.suggestions_widget.count() > 0:
            self.suggestions_widget.setCurrentRow(0)

    def hide_suggestions(self):
        """Hide the suggestions list widget"""
        self.suggestions_widget.setVisible(False)
        self.suggestions_visible = False
        self.current_suggestions = []

    def show_suggestions(self):
        """Force show suggestions (Ctrl+Space)"""
        text = self.input_field.text()
        if text:
            self.update_suggestions(text)
        else:
            # Show general suggestions
            general_suggestions = self.input_enhancer.get_auto_completions("", limit=10)
            if general_suggestions:
                self.show_suggestions_list(general_suggestions)

    def on_suggestion_selected(self, item):
        """Handle suggestion selection"""
        if item:
            # Get the actual suggestion text
            suggestion_text = item.data(Qt.ItemDataRole.UserRole)
            if suggestion_text:
                self.input_field.setText(suggestion_text)
                self.input_field.setFocus()

                # Learn from this selection
                self.input_enhancer.learn_from_input(suggestion_text, 'accepted')

            self.hide_suggestions()

    def update_input_status(self, text: str):
        """Update status label with input analysis"""
        if not text.strip():
            self.status_label.setText("Press Enter to send • Ctrl+L to clear • ↑↓ for history • Ctrl+Space for suggestions")
            return

        try:
            # Analyze input
            analysis = self.input_enhancer.analyze_input(text)

            # Create status message
            status_parts = []

            # Input type
            type_emoji = {
                'question': '❓',
                'command': '⚡',
                'code_request': '💻',
                'system_query': '🔧',
                'smart_home': '🏠',
                'training_request': '🧠',
                'conversation': '💬'
            }

            emoji = type_emoji.get(analysis.input_type.value, '💬')
            status_parts.append(f"{emoji} {analysis.input_type.value.replace('_', ' ').title()}")

            # Confidence
            confidence_emoji = {
                'high': '🟢',
                'medium': '🟡',
                'low': '🔴'
            }
            conf_emoji = confidence_emoji.get(analysis.confidence.value, '🔴')
            status_parts.append(f"{conf_emoji} {analysis.confidence.value.title()}")

            # Intent
            if analysis.intent != 'general':
                status_parts.append(f"Intent: {analysis.intent.replace('_', ' ').title()}")

            status_text = " • ".join(status_parts)
            self.status_label.setText(status_text)

        except Exception as e:
            print(f"Error updating input status: {e}")
            self.status_label.setText("Ready to send")

    def show_input_statistics(self):
        """Show input enhancement statistics"""
        try:
            stats = self.input_enhancer.get_input_statistics()

            stats_message = f"""📊 **Input Enhancement Statistics**

📝 **Usage Statistics:**
• Total Inputs: {stats.get('total_inputs', 0)}
• Success Rate: {stats.get('success_rate', 0):.1%}
• Most Common Type: {stats.get('most_common_type', 'N/A')}
• Custom Suggestions: {stats.get('custom_suggestions', 0)}

🎯 **Input Types:**"""

            input_types = stats.get('input_types', {})
            for input_type, count in input_types.items():
                stats_message += f"\n• {input_type.replace('_', ' ').title()}: {count}"

            stats_message += f"""

🚀 **Enhancement Features:**
• Smart Auto-completion ✅
• Intent Recognition ✅
• Context Awareness ✅
• Input Validation ✅
• Learning from Usage ✅
• Command History ✅

💡 **Available Commands:**
• /status - System status
• /memory - Memory status
• /help - Show help
• /suggestions - This statistics view"""

            # Emit as special command to show in chat
            self.special_command.emit('show_input_stats')

        except Exception as e:
            print(f"Error showing input statistics: {e}")

    def get_enhancement_system(self):
        """Get the input enhancement system for external access"""
        return self.input_enhancer
