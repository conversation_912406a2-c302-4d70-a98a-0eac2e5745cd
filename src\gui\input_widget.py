"""
Input Widget for Jarvis V6
Handles user input with futuristic styling and features
"""

from src.gui.qt_compat import (QWidget, QHBoxLayout, QLineEdit, QPushButton, QLabel,
                               Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QFont)
try:
    from src.gui.qt_compat import Q<PERSON>eySequence, QShortcut
except ImportError:
    # Handle different import paths between PyQt6 and PySide6
    try:
        from PyQt6.QtGui import QKeySequence, QShortcut
    except ImportError:
        from PySide6.QtGui import QKeySequence, QShortcut
from src.core.config import Config

class InputWidget(QWidget):
    """Widget for user input with send button and shortcuts"""
    
    message_sent = pyqtSignal(str)
    
    def __init__(self, config: Config, parent=None):
        super().__init__(parent)
        self.config = config
        self.command_history = []
        self.history_index = -1
        
        self.init_ui()
        self.setup_styling()
        self.setup_connections()
        self.setup_shortcuts()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Input prompt label
        self.prompt_label = QLabel("►")
        self.prompt_label.setObjectName("promptLabel")
        self.prompt_label.setFixedWidth(20)
        
        # Input field
        self.input_field = QLineEdit()
        self.input_field.setObjectName("inputField")
        self.input_field.setPlaceholderText("Enter your message here...")
        self.input_field.setMinimumHeight(40)
        
        # Send button
        self.send_button = QPushButton("SEND")
        self.send_button.setObjectName("sendButton")
        self.send_button.setFixedSize(80, 40)
        self.send_button.setEnabled(False)  # Disabled until text is entered
        
        # Clear button
        self.clear_button = QPushButton("CLEAR")
        self.clear_button.setObjectName("clearButton")
        self.clear_button.setFixedSize(80, 40)
        
        # Status label for shortcuts/hints
        self.status_label = QLabel("Press Enter to send • Ctrl+L to clear • ↑↓ for history")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        
        # Add widgets to layout
        layout.addWidget(self.prompt_label)
        layout.addWidget(self.input_field, 1)  # Give it stretch factor
        layout.addWidget(self.send_button)
        layout.addWidget(self.clear_button)
        
        # Add status label in a separate row (we'll modify layout for this)
        self.setLayout(layout)
        
    def setup_styling(self):
        """Setup the HUD input widget styling"""
        self.setStyleSheet(f"""
            #promptLabel {{
                color: {self.config.THEME_PRIMARY_COLOR};
                font-size: 20px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                text-shadow: 0 0 10px {self.config.THEME_PRIMARY_COLOR};
            }}

            #inputField {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 30, 60, 0.8),
                    stop:1 rgba(0, 20, 40, 0.9));
                border: 2px solid {self.config.THEME_PRIMARY_COLOR};
                border-radius: 12px;
                color: {self.config.THEME_TEXT_COLOR};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                padding: 12px 16px;
                selection-background-color: {self.config.THEME_SECONDARY_COLOR};
            }}

            #inputField:focus {{
                border-color: {self.config.THEME_SECONDARY_COLOR};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 50, 100, 0.8),
                    stop:1 rgba(0, 30, 60, 0.9));
                box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
            }}
            
            #sendButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.config.THEME_PRIMARY_COLOR},
                    stop:1 {self.config.THEME_SECONDARY_COLOR});
                border: none;
                border-radius: 8px;
                color: {self.config.THEME_BACKGROUND_COLOR};
                font-weight: bold;
                font-size: 12px;
            }}
            
            #sendButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.config.THEME_SECONDARY_COLOR},
                    stop:1 {self.config.THEME_PRIMARY_COLOR});
            }}
            
            #sendButton:pressed {{
                background-color: {self.config.THEME_ACCENT_COLOR};
            }}
            
            #sendButton:disabled {{
                background-color: rgba(128, 128, 128, 0.3);
                color: rgba(255, 255, 255, 0.5);
            }}
            
            #clearButton {{
                background-color: rgba(255, 102, 0, 0.8);
                border: 1px solid {self.config.THEME_ACCENT_COLOR};
                border-radius: 8px;
                color: {self.config.THEME_TEXT_COLOR};
                font-weight: bold;
                font-size: 12px;
            }}
            
            #clearButton:hover {{
                background-color: {self.config.THEME_ACCENT_COLOR};
            }}
            
            #clearButton:pressed {{
                background-color: rgba(255, 102, 0, 1.0);
            }}
            
            #statusLabel {{
                color: rgba(255, 255, 255, 0.6);
                font-size: 10px;
                font-style: italic;
            }}
        """)
        
    def setup_connections(self):
        """Setup signal connections"""
        self.input_field.textChanged.connect(self.on_text_changed)
        self.input_field.returnPressed.connect(self.send_message)
        self.send_button.clicked.connect(self.send_message)
        self.clear_button.clicked.connect(self.clear_input)
        
    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        # Clear shortcut (Ctrl+L)
        clear_shortcut = QShortcut(QKeySequence("Ctrl+L"), self)
        clear_shortcut.activated.connect(self.clear_input)
        
        # History navigation
        self.input_field.keyPressEvent = self.handle_key_press
        
    def handle_key_press(self, event):
        """Handle special key presses for history navigation"""
        if event.key() == Qt.Key.Key_Up:
            self.navigate_history(-1)
        elif event.key() == Qt.Key.Key_Down:
            self.navigate_history(1)
        else:
            # Call the original keyPressEvent
            QLineEdit.keyPressEvent(self.input_field, event)
            
    def navigate_history(self, direction: int):
        """Navigate through command history"""
        if not self.command_history:
            return
            
        self.history_index += direction
        
        # Clamp index to valid range
        if self.history_index < 0:
            self.history_index = 0
        elif self.history_index >= len(self.command_history):
            self.history_index = len(self.command_history) - 1
            
        # Set the input field text
        if 0 <= self.history_index < len(self.command_history):
            self.input_field.setText(self.command_history[self.history_index])
            
    def on_text_changed(self, text: str):
        """Handle text change in input field"""
        # Enable/disable send button based on text content
        self.send_button.setEnabled(bool(text.strip()))
        
        # Reset history index when user types
        if text and self.history_index != -1:
            self.history_index = -1
            
    def send_message(self):
        """Send the current message"""
        message = self.input_field.text().strip()
        if not message:
            return
            
        # Add to command history
        if message not in self.command_history:
            self.command_history.append(message)
            
        # Limit history size
        if len(self.command_history) > 50:
            self.command_history = self.command_history[-50:]
            
        # Reset history index
        self.history_index = -1
        
        # Emit signal
        self.message_sent.emit(message)
        
        # Animate send button
        self.animate_send_button()
        
    def animate_send_button(self):
        """Animate the send button when clicked"""
        self.send_animation = QPropertyAnimation(self.send_button, b"geometry")
        self.send_animation.setDuration(200)
        
        # Get current geometry
        current_geo = self.send_button.geometry()
        
        # Create slightly smaller geometry for "press" effect
        pressed_geo = current_geo.adjusted(2, 2, -2, -2)
        
        # Set animation values
        self.send_animation.setStartValue(current_geo)
        self.send_animation.setKeyValueAt(0.5, pressed_geo)
        self.send_animation.setEndValue(current_geo)
        self.send_animation.setEasingCurve(QEasingCurve.Type.OutBounce)
        
        self.send_animation.start()
        
    def clear_input(self):
        """Clear the input field"""
        self.input_field.clear()
        self.input_field.setFocus()
        
        # Animate clear button
        self.animate_clear_button()
        
    def animate_clear_button(self):
        """Animate the clear button when clicked"""
        self.clear_animation = QPropertyAnimation(self.clear_button, b"styleSheet")
        self.clear_animation.setDuration(300)
        
        # Flash effect
        normal_style = self.clear_button.styleSheet()
        flash_style = normal_style.replace("rgba(255, 102, 0, 0.8)", "rgba(255, 102, 0, 1.0)")
        
        self.clear_animation.setStartValue(normal_style)
        self.clear_animation.setKeyValueAt(0.5, flash_style)
        self.clear_animation.setEndValue(normal_style)
        
        self.clear_animation.start()
        
    def set_placeholder_text(self, text: str):
        """Set placeholder text for input field"""
        self.input_field.setPlaceholderText(text)
        
    def get_current_text(self) -> str:
        """Get current text in input field"""
        return self.input_field.text()
        
    def set_focus_to_input(self):
        """Set focus to the input field"""
        self.input_field.setFocus()
        
    def disable_input(self):
        """Disable input while processing"""
        self.input_field.setEnabled(False)
        self.send_button.setEnabled(False)
        self.status_label.setText("Processing...")
        
    def enable_input(self):
        """Re-enable input after processing"""
        self.input_field.setEnabled(True)
        self.send_button.setEnabled(bool(self.input_field.text().strip()))
        self.status_label.setText("Press Enter to send • Ctrl+L to clear • ↑↓ for history")
