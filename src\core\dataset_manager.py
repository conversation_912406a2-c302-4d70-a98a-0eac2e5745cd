"""
Dataset Management System for JARVIS V6
=======================================
Automatically detects and processes datasets in multiple formats

Supported Formats:
- CSV files (.csv)
- JSON files (.json)
- Text files (.txt)
- Excel files (.xlsx, .xls)
- XML files (.xml)
- YAML files (.yaml, .yml)

Features:
- Auto-detection of new datasets
- Format-specific parsing
- Intelligent data extraction
- Memory integration
- Real-time monitoring
"""

import os
import json
import csv
import xml.etree.ElementTree as ET
import yaml
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import hashlib
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class DatasetHandler(FileSystemEventHandler):
    """File system event handler for dataset monitoring"""
    
    def __init__(self, dataset_manager):
        self.dataset_manager = dataset_manager
        
    def on_created(self, event):
        if not event.is_directory:
            self.dataset_manager.process_new_file(event.src_path)
    
    def on_modified(self, event):
        if not event.is_directory:
            self.dataset_manager.process_modified_file(event.src_path)
    
    def on_deleted(self, event):
        if not event.is_directory:
            self.dataset_manager.remove_dataset(event.src_path)

class DatasetManager:
    """Comprehensive dataset management system"""
    
    def __init__(self, datasets_folder: str = "datasets", memory_system=None):
        self.datasets_folder = Path(datasets_folder)
        self.memory_system = memory_system
        self.datasets_index = {}
        self.supported_formats = {
            '.csv': self.parse_csv,
            '.json': self.parse_json,
            '.txt': self.parse_text,
            '.xlsx': self.parse_excel,
            '.xls': self.parse_excel,
            '.xml': self.parse_xml,
            '.yaml': self.parse_yaml,
            '.yml': self.parse_yaml
        }
        
        # Create datasets folder if it doesn't exist
        self.datasets_folder.mkdir(exist_ok=True)
        
        # Create index file
        self.index_file = self.datasets_folder / "datasets_index.json"
        
        # Load existing index
        self.load_index()
        
        # Setup file monitoring
        self.setup_monitoring()
        
        # Initial scan
        self.scan_datasets()
        
        print(f"🗃️ Dataset Management System initialized")
        print(f"📁 Monitoring folder: {self.datasets_folder}")
        print(f"📊 Supported formats: {list(self.supported_formats.keys())}")
    
    def setup_monitoring(self):
        """Setup file system monitoring for automatic dataset detection"""
        try:
            self.event_handler = DatasetHandler(self)
            self.observer = Observer()
            self.observer.schedule(self.event_handler, str(self.datasets_folder), recursive=True)
            self.observer.start()
            print("👁️ Dataset monitoring started")
        except Exception as e:
            print(f"⚠️ Could not start dataset monitoring: {e}")
            self.observer = None
    
    def load_index(self):
        """Load existing datasets index"""
        try:
            if self.index_file.exists():
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    self.datasets_index = json.load(f)
                print(f"📋 Loaded {len(self.datasets_index)} datasets from index")
            else:
                self.datasets_index = {}
        except Exception as e:
            print(f"❌ Error loading datasets index: {e}")
            self.datasets_index = {}
    
    def save_index(self):
        """Save datasets index to file"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.datasets_index, f, indent=2, default=str)
        except Exception as e:
            print(f"❌ Error saving datasets index: {e}")
    
    def get_file_hash(self, file_path: Path) -> str:
        """Calculate file hash for change detection"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def scan_datasets(self):
        """Scan datasets folder for new or modified files"""
        print("🔍 Scanning datasets folder...")
        processed_count = 0
        
        for file_path in self.datasets_folder.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                if self.process_file(file_path):
                    processed_count += 1
        
        print(f"📊 Processed {processed_count} dataset files")
        self.save_index()
    
    def process_file(self, file_path: Path) -> bool:
        """Process a single dataset file"""
        try:
            file_key = str(file_path.relative_to(self.datasets_folder))

            # Skip processing the index file itself to prevent loops
            if file_key == "datasets_index.json":
                return False

            current_hash = self.get_file_hash(file_path)

            # Check if file is new or modified
            if file_key in self.datasets_index:
                if self.datasets_index[file_key].get('hash') == current_hash:
                    return False  # File unchanged
            
            # Parse the file
            parser = self.supported_formats.get(file_path.suffix.lower())
            if not parser:
                return False
            
            print(f"📄 Processing dataset: {file_key}")
            data = parser(file_path)
            
            if data:
                # Update index
                self.datasets_index[file_key] = {
                    'path': str(file_path),
                    'format': file_path.suffix.lower(),
                    'hash': current_hash,
                    'processed_at': datetime.now().isoformat(),
                    'size': file_path.stat().st_size,
                    'records_count': len(data) if isinstance(data, list) else 1,
                    'data_preview': str(data)[:200] + "..." if len(str(data)) > 200 else str(data)
                }
                
                # Integrate with memory system
                if self.memory_system:
                    self.integrate_with_memory(file_key, data)
                
                print(f"✅ Successfully processed {file_key}")
                return True
            
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")
        
        return False
    
    def parse_csv(self, file_path: Path) -> List[Dict]:
        """Parse CSV file"""
        try:
            data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(dict(row))
            return data
        except Exception as e:
            print(f"❌ CSV parsing error: {e}")
            return []
    
    def parse_json(self, file_path: Path) -> Union[Dict, List]:
        """Parse JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ JSON parsing error: {e}")
            return {}
    
    def parse_text(self, file_path: Path) -> Dict:
        """Parse text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to detect structure
            lines = content.split('\n')
            if len(lines) > 1:
                return {
                    'type': 'text_lines',
                    'content': lines,
                    'line_count': len(lines)
                }
            else:
                return {
                    'type': 'text_content',
                    'content': content,
                    'word_count': len(content.split())
                }
        except Exception as e:
            print(f"❌ Text parsing error: {e}")
            return {}
    
    def parse_excel(self, file_path: Path) -> List[Dict]:
        """Parse Excel file"""
        try:
            df = pd.read_excel(file_path)
            return df.to_dict('records')
        except Exception as e:
            print(f"❌ Excel parsing error: {e}")
            return []
    
    def parse_xml(self, file_path: Path) -> Dict:
        """Parse XML file"""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            def xml_to_dict(element):
                result = {}
                if element.text and element.text.strip():
                    result['text'] = element.text.strip()
                
                for child in element:
                    child_data = xml_to_dict(child)
                    if child.tag in result:
                        if not isinstance(result[child.tag], list):
                            result[child.tag] = [result[child.tag]]
                        result[child.tag].append(child_data)
                    else:
                        result[child.tag] = child_data
                
                result.update(element.attrib)
                return result
            
            return {root.tag: xml_to_dict(root)}
        except Exception as e:
            print(f"❌ XML parsing error: {e}")
            return {}
    
    def parse_yaml(self, file_path: Path) -> Union[Dict, List]:
        """Parse YAML file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ YAML parsing error: {e}")
            return {}
    
    def integrate_with_memory(self, dataset_name: str, data: Any):
        """Integrate dataset with JARVIS memory system"""
        try:
            if not self.memory_system:
                return

            # Create memory entry for dataset
            memory_content = f"Dataset: {dataset_name}\n"

            if isinstance(data, list) and data:
                memory_content += f"Records: {len(data)}\n"
                memory_content += f"Sample: {str(data[0])[:100]}...\n"
            elif isinstance(data, dict):
                memory_content += f"Keys: {list(data.keys())[:10]}\n"
                memory_content += f"Sample: {str(data)[:100]}...\n"
            else:
                memory_content += f"Content: {str(data)[:100]}...\n"

            # Store in memory system (temporarily disabled due to API mismatch)
            # TODO: Fix memory system integration
            print(f"📝 Dataset '{dataset_name}' ready for memory integration")

        except Exception as e:
            print(f"❌ Memory integration error: {e}")
    
    def process_new_file(self, file_path: str):
        """Process newly added file"""
        path = Path(file_path)
        # Skip index file and only process supported formats
        if (path.suffix.lower() in self.supported_formats and
            path.name != "datasets_index.json"):
            print(f"🆕 New dataset detected: {path.name}")
            if self.process_file(path):
                self.save_index()

    def process_modified_file(self, file_path: str):
        """Process modified file"""
        path = Path(file_path)
        # Skip index file and only process supported formats
        if (path.suffix.lower() in self.supported_formats and
            path.name != "datasets_index.json"):
            print(f"🔄 Dataset modified: {path.name}")
            if self.process_file(path):
                self.save_index()
    
    def remove_dataset(self, file_path: str):
        """Remove dataset from index when file is deleted"""
        try:
            path = Path(file_path)
            file_key = str(path.relative_to(self.datasets_folder))
            
            if file_key in self.datasets_index:
                del self.datasets_index[file_key]
                self.save_index()
                print(f"🗑️ Removed dataset: {file_key}")
        except Exception as e:
            print(f"❌ Error removing dataset: {e}")
    
    def get_dataset_info(self) -> Dict:
        """Get information about all datasets"""
        return {
            'total_datasets': len(self.datasets_index),
            'datasets': self.datasets_index,
            'supported_formats': list(self.supported_formats.keys()),
            'datasets_folder': str(self.datasets_folder)
        }
    
    def search_datasets(self, query: str) -> List[Dict]:
        """Search datasets by name or content"""
        results = []
        query_lower = query.lower()
        
        for file_key, info in self.datasets_index.items():
            if (query_lower in file_key.lower() or 
                query_lower in info.get('data_preview', '').lower()):
                results.append({
                    'file': file_key,
                    'info': info
                })
        
        return results
    
    def stop_monitoring(self):
        """Stop file system monitoring"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            print("🛑 Dataset monitoring stopped")
