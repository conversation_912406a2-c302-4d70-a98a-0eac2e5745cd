🤖 JARVIS V6 AI Assistant - Standalone Executable
==================================================

🎉 Welcome to JARVIS V6 Enhanced AI System!

📁 Files in this package:
• JARVIS_V6.exe - Main executable (standalone)
• Launch_JARVIS_V6.bat - Easy launcher with info
• README.txt - This file

🚀 How to Run JARVIS:
1. Double-click JARVIS_V6.exe
   OR
2. Double-click Launch_JARVIS_V6.bat (recommended)

✨ Features Available:
• Futuristic JARVIS HUD interface with animated core
• Advanced AI systems with learning capabilities
• ElevenLabs TTS voice output (requires API key)
• Mixtral 8x7B AI chat (requires Ollama running)
• Enhanced training system with session management
• Advanced memory system with emotional intelligence
• Self-evolution system with code analysis
• Special commands (/status, /memory, /help, etc.)

🎮 Control Panel Features:
• 🔊 Voice Output - Toggle TTS on/off
• 🧠 Learning - Toggle learning systems
• ✏️ Self-Edit - Toggle response improvement
• 🧠 Memory - Toggle advanced memory
• 🔬 Evolution - Toggle self-evolution

💡 Special Commands (type in chat):
• /status - Show all AI systems status
• /memory - Show memory system status
• /evolution - Show evolution system status
• /training - Show training system status
• /help - Show all commands

⚙️ Requirements:
• Windows 10/11 (64-bit)
• Ollama running with Mixtral 8x7b model (for AI chat)
• ElevenLabs API key (for voice output)
• Internet connection (for TTS and AI)

🔧 Configuration:
The executable includes default settings. For custom configuration:
1. Create a .env file in the same folder as JARVIS_V6.exe
2. Add your settings (see project documentation)

📝 Example .env file:
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=mixtral:8x7b
ELEVENLABS_API_KEY=your_api_key_here
ELEVENLABS_VOICE_ID=your_voice_id_here
TTS_ENABLED=true

🎯 Getting Started:
1. Make sure Ollama is running with Mixtral model
2. Launch JARVIS using the .exe or .bat file
3. Try typing "Hello JARVIS" to test the system
4. Use /status to see all available features
5. Toggle AI systems using the control panel

🆘 Troubleshooting:
• If JARVIS doesn't start, check Windows Defender/antivirus
• If no voice output, check your ElevenLabs API key
• If no AI responses, ensure Ollama is running
• For help, type /help in the chat interface

🎉 Enjoy your advanced AI assistant!

Built with ❤️ using Python, PySide6, and PyInstaller
JARVIS V6 - The Future of AI Assistants
