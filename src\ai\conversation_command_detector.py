"""
Conversation vs Command Detection System for JARVIS V6
====================================================
Intelligently distinguishes between casual conversation and specific commands

Features:
- Natural language command parsing
- Conversation vs command classification
- Complex topic extraction for training
- Intent recognition and confidence scoring
"""

import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class MessageType(Enum):
    CONVERSATION = "conversation"
    COMMAND = "command"
    QUESTION = "question"
    GREETING = "greeting"
    TRAINING_REQUEST = "training_request"
    STATUS_REQUEST = "status_request"
    TECHNICAL_REQUEST = "technical_request"

@dataclass
class MessageAnalysis:
    message_type: MessageType
    confidence: float
    intent: str
    extracted_data: Dict[str, any]
    reasoning: str

class ConversationCommandDetector:
    """Detects whether user input is conversation or a command"""
    
    def __init__(self):
        self.command_patterns = self._build_command_patterns()
        self.conversation_patterns = self._build_conversation_patterns()
        self.training_patterns = self._build_training_patterns()
    
    def _build_command_patterns(self) -> Dict[str, List[str]]:
        """Build patterns that indicate commands"""
        return {
            'training_commands': [
                r'train\s+about\s+(.+?)\s+for\s+(.+)',
                r'learn\s+about\s+(.+?)\s+for\s+(.+)',
                r'study\s+(.+?)\s+for\s+(.+)',
                r'research\s+(.+?)\s+for\s+(.+)',
                r'teach\s+yourself\s+(.+?)\s+for\s+(.+)'
            ],
            'status_commands': [
                r'show\s+(me\s+)?(your\s+)?(.+?)\s+status',
                r'what\s+is\s+your\s+(.+?)\s+status',
                r'check\s+(your\s+)?(.+?)\s+status',
                r'display\s+(.+?)\s+information',
                r'give\s+me\s+(.+?)\s+stats'
            ],
            'action_commands': [
                r'turn\s+(on|off)\s+(.+)',
                r'set\s+(.+?)\s+to\s+(.+)',
                r'adjust\s+(.+?)\s+to\s+(.+)',
                r'change\s+(.+?)\s+to\s+(.+)',
                r'start\s+(.+)',
                r'stop\s+(.+)',
                r'enable\s+(.+)',
                r'disable\s+(.+)'
            ],
            'technical_commands': [
                r'analyze\s+(.+)',
                r'debug\s+(.+)',
                r'fix\s+(.+)',
                r'optimize\s+(.+)',
                r'improve\s+(.+)',
                r'scan\s+(.+)',
                r'check\s+for\s+(.+)',
                r'run\s+(.+)',
                r'execute\s+(.+)'
            ]
        }
    
    def _build_conversation_patterns(self) -> List[str]:
        """Build patterns that indicate casual conversation"""
        return [
            r'^(hi|hello|hey)(\s+there)?(\s+jarvis)?[.!?]*$',
            r'^(good\s+)?(morning|afternoon|evening)(\s+jarvis)?[.!?]*$',
            r'^how\s+are\s+(you|u)(\s+doing)?(\s+today)?[.!?]*$',
            r'^what\'?s\s+up[.!?]*$',
            r'^how\'?s\s+it\s+going[.!?]*$',
            r'^nice\s+to\s+(see|meet)\s+you[.!?]*$',
            r'^thanks?(\s+you)?[.!?]*$',
            r'^thank\s+you[.!?]*$',
            r'^(bye|goodbye)(\s+jarvis)?[.!?]*$',
            r'^see\s+you\s+later[.!?]*$',
            r'^talk\s+to\s+you\s+later[.!?]*$',
            r'^have\s+a\s+(good|great|nice)\s+day[.!?]*$',
            r'^(ok|okay|alright)(\s+thanks?)?[.!?]*$',
            r'^(yes|yeah|yep|no|nope)(\s+thanks?)?[.!?]*$'
        ]
    
    def _build_training_patterns(self) -> Dict[str, str]:
        """Build advanced training command patterns"""
        return {
            'complex_topic': r'train\s+about\s+\(([^)]+)\)\s+for\s+(.+)',
            'simple_topic': r'train\s+about\s+([^,\s]+(?:\s+[^,\s]+)*?)\s+for\s+(.+)',
            'learn_complex': r'learn\s+about\s+\(([^)]+)\)\s+for\s+(.+)',
            'learn_simple': r'learn\s+about\s+([^,\s]+(?:\s+[^,\s]+)*?)\s+for\s+(.+)',
            'study_complex': r'study\s+\(([^)]+)\)\s+for\s+(.+)',
            'study_simple': r'study\s+([^,\s]+(?:\s+[^,\s]+)*?)\s+for\s+(.+)'
        }
    
    def analyze_message(self, message: str) -> MessageAnalysis:
        """Analyze message to determine type and extract relevant data"""
        message = message.strip()
        message_lower = message.lower()
        
        # Check for greetings first
        if self._is_greeting(message_lower):
            return MessageAnalysis(
                message_type=MessageType.GREETING,
                confidence=0.95,
                intent="greeting",
                extracted_data={},
                reasoning="Detected common greeting pattern"
            )
        
        # Check for training commands
        training_analysis = self._analyze_training_command(message)
        if training_analysis:
            return training_analysis
        
        # Check for status requests
        status_analysis = self._analyze_status_request(message_lower)
        if status_analysis:
            return status_analysis
        
        # Check for technical commands
        technical_analysis = self._analyze_technical_command(message_lower)
        if technical_analysis:
            return technical_analysis
        
        # Check for action commands
        action_analysis = self._analyze_action_command(message_lower)
        if action_analysis:
            return action_analysis
        
        # Check if it's a question
        if self._is_question(message):
            return MessageAnalysis(
                message_type=MessageType.QUESTION,
                confidence=0.8,
                intent="information_request",
                extracted_data={'question': message},
                reasoning="Detected question pattern"
            )
        
        # Default to conversation
        return MessageAnalysis(
            message_type=MessageType.CONVERSATION,
            confidence=0.6,
            intent="general_conversation",
            extracted_data={'message': message},
            reasoning="No specific command pattern detected, treating as conversation"
        )
    
    def _is_greeting(self, message: str) -> bool:
        """Check if message is a greeting"""
        for pattern in self.conversation_patterns:
            if re.match(pattern, message, re.IGNORECASE):
                return True
        return False
    
    def _analyze_training_command(self, message: str) -> Optional[MessageAnalysis]:
        """Analyze training commands with complex topic extraction"""
        for pattern_name, pattern in self.training_patterns.items():
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                if 'complex' in pattern_name:
                    # Extract topic from parentheses
                    topic = match.group(1).strip()
                    duration = match.group(2).strip()
                else:
                    # Extract simple topic
                    topic = match.group(1).strip()
                    duration = match.group(2).strip()
                
                # Parse duration
                duration_minutes = self._parse_duration(duration)
                
                return MessageAnalysis(
                    message_type=MessageType.TRAINING_REQUEST,
                    confidence=0.95,
                    intent="background_training",
                    extracted_data={
                        'topic': topic,
                        'duration': duration,
                        'duration_minutes': duration_minutes,
                        'pattern_type': pattern_name
                    },
                    reasoning=f"Detected training command with topic '{topic}' for {duration}"
                )
        
        return None
    
    def _analyze_status_request(self, message: str) -> Optional[MessageAnalysis]:
        """Analyze status request commands"""
        for pattern in self.command_patterns['status_commands']:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                # Extract the system/component being queried
                component = match.group(-1).strip() if match.groups() else "general"
                
                return MessageAnalysis(
                    message_type=MessageType.STATUS_REQUEST,
                    confidence=0.9,
                    intent="status_inquiry",
                    extracted_data={'component': component},
                    reasoning=f"Detected status request for '{component}'"
                )
        
        return None
    
    def _analyze_technical_command(self, message: str) -> Optional[MessageAnalysis]:
        """Analyze technical commands"""
        for pattern in self.command_patterns['technical_commands']:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                target = match.group(1).strip() if match.groups() else ""
                action = pattern.split(r'\s+')[0].replace('\\', '')
                
                return MessageAnalysis(
                    message_type=MessageType.TECHNICAL_REQUEST,
                    confidence=0.85,
                    intent="technical_action",
                    extracted_data={
                        'action': action,
                        'target': target
                    },
                    reasoning=f"Detected technical command: {action} {target}"
                )
        
        return None
    
    def _analyze_action_command(self, message: str) -> Optional[MessageAnalysis]:
        """Analyze action commands"""
        for pattern in self.command_patterns['action_commands']:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                groups = match.groups()
                if len(groups) >= 2:
                    action = groups[0]
                    target = groups[1]
                elif len(groups) == 1:
                    # Extract action from pattern
                    action = pattern.split(r'\s+')[0].replace('\\', '')
                    target = groups[0]
                else:
                    continue
                
                return MessageAnalysis(
                    message_type=MessageType.COMMAND,
                    confidence=0.8,
                    intent="device_control",
                    extracted_data={
                        'action': action,
                        'target': target
                    },
                    reasoning=f"Detected action command: {action} {target}"
                )
        
        return None
    
    def _is_question(self, message: str) -> bool:
        """Check if message is a question"""
        # Check for question marks
        if message.strip().endswith('?'):
            return True
        
        # Check for question words at the beginning
        question_words = ['what', 'how', 'why', 'when', 'where', 'who', 'which', 'can', 'could', 'would', 'should', 'is', 'are', 'do', 'does', 'did']
        first_word = message.split()[0].lower() if message.split() else ""
        
        return first_word in question_words
    
    def _parse_duration(self, duration_str: str) -> int:
        """Parse duration string into minutes"""
        duration_str = duration_str.lower().strip()
        
        # Extract numbers and units
        import re
        
        # Pattern for "X hours", "X minutes", "X hrs", "X mins", etc.
        patterns = [
            (r'(\d+(?:\.\d+)?)\s*hours?', 60),
            (r'(\d+(?:\.\d+)?)\s*hrs?', 60),
            (r'(\d+(?:\.\d+)?)\s*h\b', 60),
            (r'(\d+(?:\.\d+)?)\s*minutes?', 1),
            (r'(\d+(?:\.\d+)?)\s*mins?', 1),
            (r'(\d+(?:\.\d+)?)\s*m\b', 1),
        ]
        
        total_minutes = 0
        
        for pattern, multiplier in patterns:
            matches = re.findall(pattern, duration_str)
            for match in matches:
                total_minutes += float(match) * multiplier
        
        # If no pattern matched, try to extract just a number and assume minutes
        if total_minutes == 0:
            number_match = re.search(r'(\d+(?:\.\d+)?)', duration_str)
            if number_match:
                total_minutes = float(number_match.group(1))
        
        return int(total_minutes) if total_minutes > 0 else 30  # Default to 30 minutes
    
    def get_conversation_suggestions(self, message_type: MessageType) -> List[str]:
        """Get suggestions based on message type"""
        suggestions = {
            MessageType.CONVERSATION: [
                "I'm here to chat! What would you like to talk about?",
                "Feel free to ask me anything or just have a conversation.",
                "I enjoy our conversations! What's on your mind?"
            ],
            MessageType.COMMAND: [
                "I can help with various commands. Try 'train about [topic] for [duration]'",
                "Available commands: status checks, training, device control, and more.",
                "Need help with commands? Just ask!"
            ],
            MessageType.TRAINING_REQUEST: [
                "Training format: 'train about (topic) for X hours'",
                "Example: 'train about (machine learning algorithms) for 2 hours'",
                "I can learn about any topic you specify!"
            ],
            MessageType.QUESTION: [
                "I'm ready to answer your questions!",
                "Ask me anything - I have access to extensive knowledge.",
                "Questions help me learn and improve!"
            ]
        }
        
        return suggestions.get(message_type, ["I'm here to help with whatever you need!"])
