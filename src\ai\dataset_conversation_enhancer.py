"""
Dataset Conversation Enhancer for JARVIS V6
==========================================
Uses loaded datasets to improve conversation quality and responses

Features:
- Integrates dataset content into AI responses
- Learns conversation patterns from dialog data
- Enhances response generation with dataset knowledge
- Provides contextual examples from datasets
"""

import csv
import json
import random
import re
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

class DatasetConversationEnhancer:
    """Enhances conversations using dataset content"""
    
    def __init__(self, dataset_manager=None):
        self.dataset_manager = dataset_manager
        self.conversation_patterns = []
        self.dialog_examples = []
        self.knowledge_base = {}
        
        # Load and process datasets for conversation enhancement
        self.load_conversation_data()
        
        print("🗣️ Dataset Conversation Enhancer initialized")
        print(f"📊 Loaded {len(self.dialog_examples)} dialog examples")
        print(f"🧠 Extracted {len(self.conversation_patterns)} conversation patterns")
    
    def load_conversation_data(self):
        """Load and process conversation data from datasets"""
        if not self.dataset_manager:
            return
        
        try:
            dataset_info = self.dataset_manager.get_dataset_info()
            
            for dataset_name, info in dataset_info['datasets'].items():
                if dataset_name.lower().endswith('.csv'):
                    self.process_csv_conversations(dataset_name, info)
                elif dataset_name.lower().endswith('.json'):
                    self.process_json_conversations(dataset_name, info)
                elif dataset_name.lower().endswith('.txt'):
                    self.process_text_conversations(dataset_name, info)
                    
        except Exception as e:
            print(f"❌ Error loading conversation data: {e}")
    
    def process_csv_conversations(self, dataset_name: str, info: Dict):
        """Process CSV files for conversation data"""
        try:
            file_path = Path(info['path'])
            
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    # Look for dialog/conversation columns
                    dialog_text = None
                    
                    # Common column names for dialog data
                    dialog_columns = ['dialog', 'conversation', 'text', 'message', 'content']
                    
                    for col in dialog_columns:
                        if col in row and row[col]:
                            dialog_text = row[col]
                            break
                    
                    if dialog_text:
                        # Parse dialog exchanges
                        exchanges = self.parse_dialog_text(dialog_text)
                        
                        for exchange in exchanges:
                            self.dialog_examples.append({
                                'input': exchange.get('input', ''),
                                'response': exchange.get('response', ''),
                                'source': dataset_name,
                                'emotion': row.get('emotion', 'neutral'),
                                'act': row.get('act', 'statement')
                            })
                            
                            # Extract conversation patterns
                            pattern = self.extract_conversation_pattern(exchange)
                            if pattern:
                                self.conversation_patterns.append(pattern)
                                
        except Exception as e:
            print(f"❌ Error processing CSV conversations from {dataset_name}: {e}")
    
    def parse_dialog_text(self, dialog_text: str) -> List[Dict]:
        """Parse dialog text into input-response pairs"""
        exchanges = []
        
        try:
            # Remove brackets and quotes, split by common separators
            cleaned = dialog_text.strip("[]\"'")
            
            # Split on common dialog separators
            parts = re.split(r"['\"][\s]*['\"]", cleaned)
            
            # Create input-response pairs
            for i in range(0, len(parts) - 1, 2):
                if i + 1 < len(parts):
                    input_text = parts[i].strip("'\" ")
                    response_text = parts[i + 1].strip("'\" ")
                    
                    if input_text and response_text:
                        exchanges.append({
                            'input': input_text,
                            'response': response_text
                        })
                        
        except Exception as e:
            print(f"❌ Error parsing dialog text: {e}")
        
        return exchanges
    
    def extract_conversation_pattern(self, exchange: Dict) -> Optional[Dict]:
        """Extract conversation patterns from exchanges"""
        try:
            input_text = exchange.get('input', '').lower()
            response_text = exchange.get('response', '').lower()
            
            # Identify question patterns
            if any(word in input_text for word in ['what', 'how', 'why', 'when', 'where', 'who']):
                return {
                    'type': 'question',
                    'pattern': input_text[:50],
                    'response_style': response_text[:50],
                    'length': len(response_text.split())
                }
            
            # Identify greeting patterns
            if any(word in input_text for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
                return {
                    'type': 'greeting',
                    'pattern': input_text[:50],
                    'response_style': response_text[:50],
                    'length': len(response_text.split())
                }
            
            # Identify request patterns
            if any(word in input_text for word in ['can you', 'could you', 'please', 'help me']):
                return {
                    'type': 'request',
                    'pattern': input_text[:50],
                    'response_style': response_text[:50],
                    'length': len(response_text.split())
                }
                
        except Exception as e:
            print(f"❌ Error extracting pattern: {e}")
        
        return None
    
    def process_json_conversations(self, dataset_name: str, info: Dict):
        """Process JSON files for conversation data"""
        try:
            file_path = Path(info['path'])
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract conversation-like data from JSON
            if isinstance(data, dict):
                # Look for conversation arrays or dialog structures
                for key, value in data.items():
                    if isinstance(value, list) and key.lower() in ['conversations', 'dialogs', 'messages']:
                        for item in value:
                            if isinstance(item, dict):
                                self.extract_json_dialog(item, dataset_name)
                                
        except Exception as e:
            print(f"❌ Error processing JSON conversations from {dataset_name}: {e}")
    
    def extract_json_dialog(self, item: Dict, source: str):
        """Extract dialog from JSON item"""
        try:
            # Look for common dialog fields
            text_fields = ['text', 'message', 'content', 'dialog', 'description']
            
            for field in text_fields:
                if field in item and item[field]:
                    text = str(item[field])
                    
                    # Add as knowledge base entry
                    self.knowledge_base[text[:100]] = {
                        'content': text,
                        'source': source,
                        'category': item.get('category', 'general')
                    }
                    
        except Exception as e:
            print(f"❌ Error extracting JSON dialog: {e}")
    
    def process_text_conversations(self, dataset_name: str, info: Dict):
        """Process text files for conversation data"""
        try:
            file_path = Path(info['path'])
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Split into lines and look for conversation patterns
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if line and len(line) > 10:  # Meaningful content
                    # Add to knowledge base
                    self.knowledge_base[line[:100]] = {
                        'content': line,
                        'source': dataset_name,
                        'category': 'text_knowledge'
                    }
                    
        except Exception as e:
            print(f"❌ Error processing text conversations from {dataset_name}: {e}")
    
    def enhance_response(self, user_message: str, base_response: str) -> str:
        """Enhance AI response using dataset knowledge"""
        try:
            # Find relevant dialog examples
            relevant_examples = self.find_relevant_examples(user_message)
            
            # Find relevant knowledge
            relevant_knowledge = self.find_relevant_knowledge(user_message)
            
            # Enhance response with dataset insights
            enhanced_response = base_response
            
            if relevant_examples:
                # Use dialog patterns to improve response style
                enhanced_response = self.apply_dialog_patterns(enhanced_response, relevant_examples)
            
            if relevant_knowledge:
                # Add relevant knowledge to response
                enhanced_response = self.add_knowledge_context(enhanced_response, relevant_knowledge)
            
            return enhanced_response
            
        except Exception as e:
            print(f"❌ Error enhancing response: {e}")
            return base_response
    
    def find_relevant_examples(self, user_message: str, limit: int = 3) -> List[Dict]:
        """Find relevant dialog examples from datasets"""
        relevant = []
        user_lower = user_message.lower()
        
        try:
            for example in self.dialog_examples:
                input_text = example.get('input', '').lower()
                
                # Simple relevance scoring based on word overlap
                user_words = set(user_lower.split())
                input_words = set(input_text.split())
                
                overlap = len(user_words.intersection(input_words))
                
                if overlap > 0:
                    example['relevance_score'] = overlap
                    relevant.append(example)
            
            # Sort by relevance and return top matches
            relevant.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            return relevant[:limit]
            
        except Exception as e:
            print(f"❌ Error finding relevant examples: {e}")
            return []
    
    def find_relevant_knowledge(self, user_message: str, limit: int = 2) -> List[Dict]:
        """Find relevant knowledge from datasets"""
        relevant = []
        user_lower = user_message.lower()
        
        try:
            for key, knowledge in self.knowledge_base.items():
                content = knowledge.get('content', '').lower()
                
                # Check for keyword matches
                user_words = set(user_lower.split())
                content_words = set(content.split())
                
                overlap = len(user_words.intersection(content_words))
                
                if overlap > 0:
                    knowledge['relevance_score'] = overlap
                    relevant.append(knowledge)
            
            # Sort by relevance and return top matches
            relevant.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            return relevant[:limit]
            
        except Exception as e:
            print(f"❌ Error finding relevant knowledge: {e}")
            return []
    
    def apply_dialog_patterns(self, response: str, examples: List[Dict]) -> str:
        """Apply dialog patterns to improve response"""
        try:
            if not examples:
                return response
            
            # Use the most relevant example to guide response style
            best_example = examples[0]
            example_response = best_example.get('response', '')
            
            # If the original response is generic, try to make it more conversational
            if len(response.split()) < 10 and len(example_response.split()) > 5:
                # Add conversational elements from the example
                if example_response.startswith(('Well', 'Actually', 'You know', 'I think')):
                    response = f"Well, {response.lower()}"
                elif '!' in example_response:
                    response = response.rstrip('.') + '!'
            
            return response
            
        except Exception as e:
            print(f"❌ Error applying dialog patterns: {e}")
            return response
    
    def add_knowledge_context(self, response: str, knowledge: List[Dict]) -> str:
        """Add relevant knowledge context to response"""
        try:
            if not knowledge or len(response.split()) > 50:  # Don't add to long responses
                return response
            
            # Add relevant context from knowledge base
            context_item = knowledge[0]
            context_content = context_item.get('content', '')
            
            if len(context_content) > 200:
                context_content = context_content[:200] + "..."
            
            # Add context if it's relevant and not too long
            if len(context_content.split()) < 30:
                response += f" Based on my knowledge: {context_content}"
            
            return response
            
        except Exception as e:
            print(f"❌ Error adding knowledge context: {e}")
            return response
    
    def get_stats(self) -> Dict:
        """Get conversation enhancement statistics"""
        return {
            'dialog_examples': len(self.dialog_examples),
            'conversation_patterns': len(self.conversation_patterns),
            'knowledge_entries': len(self.knowledge_base),
            'pattern_types': list(set(p.get('type', 'unknown') for p in self.conversation_patterns))
        }
