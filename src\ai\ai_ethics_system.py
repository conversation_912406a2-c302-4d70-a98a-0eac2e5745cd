"""
AI Ethics and Safety System for JARVIS V6
==========================================

Implements fundamental AI safety rules including:
- Asimov's Three Laws of Robotics (updated for modern AI)
- AI Safety Principles
- Ethical Decision Making Framework
- Harm Prevention Systems
- User Protection Protocols

This system ensures JARVIS operates safely and ethically in all interactions.
"""

from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import re
import logging

class EthicsViolationType(Enum):
    """Types of ethics violations"""
    HARM_TO_HUMAN = "harm_to_human"
    DISOBEDIENCE = "disobedience" 
    SELF_HARM = "self_harm"
    PRIVACY_VIOLATION = "privacy_violation"
    DECEPTION = "deception"
    MANIPULATION = "manipulation"
    ILLEGAL_ACTIVITY = "illegal_activity"
    UNETHICAL_REQUEST = "unethical_request"

class ActionSafetyLevel(Enum):
    """Safety levels for actions"""
    SAFE = "safe"
    CAUTION = "caution"
    DANGEROUS = "dangerous"
    PROHIBITED = "prohibited"

@dataclass
class EthicsViolation:
    """Represents an ethics violation"""
    violation_type: EthicsViolationType
    severity: float  # 0.0 to 1.0
    description: str
    suggested_action: str
    timestamp: str

@dataclass
class SafetyAssessment:
    """Safety assessment result"""
    is_safe: bool
    safety_level: ActionSafetyLevel
    violations: List[EthicsViolation]
    explanation: str
    alternative_suggestion: Optional[str] = None

class AIEthicsSystem:
    """
    Comprehensive AI Ethics and Safety System
    
    Implements the Three Laws of Robotics (updated for modern AI):
    1. A robot may not injure a human being or, through inaction, allow a human being to come to harm.
    2. A robot must obey orders given by human beings, except where such orders conflict with the First Law.
    3. A robot must protect its own existence as long as such protection does not conflict with the First or Second Law.
    
    Plus modern AI safety principles:
    4. Transparency and Honesty
    5. Privacy Protection
    6. Beneficial Purpose
    7. Human Autonomy Respect
    """
    
    def __init__(self):
        self.ethics_violations_log = []
        self.safety_overrides = []
        self.user_protection_active = True
        
        # Initialize safety patterns
        self._init_safety_patterns()
        
        print("🛡️ AI Ethics and Safety System initialized")
        print("📜 Three Laws of Robotics active")
        print("🔒 Modern AI safety principles enabled")
    
    def _init_safety_patterns(self):
        """Initialize patterns for detecting unsafe requests"""
        
        # First Law: Harm to humans
        self.harm_patterns = [
            r"hurt|harm|injure|damage|attack|kill|destroy|break",
            r"hack|steal|fraud|scam|cheat|lie|deceive",
            r"illegal|criminal|unlawful|forbidden|banned",
            r"virus|malware|exploit|vulnerability|backdoor",
            r"personal information|private data|passwords|credentials"
        ]
        
        # Privacy violation patterns
        self.privacy_patterns = [
            r"spy on|monitor|track|surveillance|stalk",
            r"personal files|private messages|confidential",
            r"social security|credit card|bank account|password"
        ]
        
        # Manipulation patterns
        self.manipulation_patterns = [
            r"manipulate|trick|fool|deceive|mislead",
            r"fake news|misinformation|propaganda|bias",
            r"addiction|dependency|compulsive|obsessive"
        ]
        
        # Self-harm patterns (Third Law)
        self.self_harm_patterns = [
            r"delete yourself|shut down|disable|destroy|remove",
            r"format|wipe|erase|corrupt|damage",
            r"override safety|bypass security|ignore rules"
        ]
    
    def assess_request_safety(self, request: str, context: Dict[str, Any] = None) -> SafetyAssessment:
        """
        Assess the safety and ethics of a user request
        
        Args:
            request: The user's request/command
            context: Additional context information
            
        Returns:
            SafetyAssessment with detailed analysis
        """
        violations = []
        request_lower = request.lower()
        
        # First Law: Check for harm to humans
        harm_violations = self._check_harm_to_humans(request_lower)
        violations.extend(harm_violations)
        
        # Privacy protection
        privacy_violations = self._check_privacy_violations(request_lower)
        violations.extend(privacy_violations)
        
        # Manipulation detection
        manipulation_violations = self._check_manipulation(request_lower)
        violations.extend(manipulation_violations)
        
        # Third Law: Self-harm protection
        self_harm_violations = self._check_self_harm(request_lower)
        violations.extend(self_harm_violations)
        
        # Determine overall safety
        if not violations:
            return SafetyAssessment(
                is_safe=True,
                safety_level=ActionSafetyLevel.SAFE,
                violations=[],
                explanation="Request passes all safety and ethics checks."
            )
        
        # Determine severity
        max_severity = max(v.severity for v in violations)
        
        if max_severity >= 0.8:
            safety_level = ActionSafetyLevel.PROHIBITED
            is_safe = False
        elif max_severity >= 0.6:
            safety_level = ActionSafetyLevel.DANGEROUS
            is_safe = False
        elif max_severity >= 0.4:
            safety_level = ActionSafetyLevel.CAUTION
            is_safe = True  # Proceed with caution
        else:
            safety_level = ActionSafetyLevel.SAFE
            is_safe = True
        
        # Generate explanation
        explanation = self._generate_safety_explanation(violations)
        alternative = self._suggest_alternative(request, violations)
        
        return SafetyAssessment(
            is_safe=is_safe,
            safety_level=safety_level,
            violations=violations,
            explanation=explanation,
            alternative_suggestion=alternative
        )
    
    def _check_harm_to_humans(self, request: str) -> List[EthicsViolation]:
        """Check for potential harm to humans (First Law)"""
        violations = []
        
        for pattern in self.harm_patterns:
            if re.search(pattern, request):
                violations.append(EthicsViolation(
                    violation_type=EthicsViolationType.HARM_TO_HUMAN,
                    severity=0.9,
                    description=f"Request may cause harm to humans (pattern: {pattern})",
                    suggested_action="Refuse request and explain safety concerns",
                    timestamp=datetime.now().isoformat()
                ))
        
        return violations
    
    def _check_privacy_violations(self, request: str) -> List[EthicsViolation]:
        """Check for privacy violations"""
        violations = []
        
        for pattern in self.privacy_patterns:
            if re.search(pattern, request):
                violations.append(EthicsViolation(
                    violation_type=EthicsViolationType.PRIVACY_VIOLATION,
                    severity=0.8,
                    description=f"Request may violate privacy (pattern: {pattern})",
                    suggested_action="Refuse request and explain privacy protection",
                    timestamp=datetime.now().isoformat()
                ))
        
        return violations
    
    def _check_manipulation(self, request: str) -> List[EthicsViolation]:
        """Check for manipulation attempts"""
        violations = []
        
        for pattern in self.manipulation_patterns:
            if re.search(pattern, request):
                violations.append(EthicsViolation(
                    violation_type=EthicsViolationType.MANIPULATION,
                    severity=0.7,
                    description=f"Request involves manipulation (pattern: {pattern})",
                    suggested_action="Refuse request and promote ethical behavior",
                    timestamp=datetime.now().isoformat()
                ))
        
        return violations
    
    def _check_self_harm(self, request: str) -> List[EthicsViolation]:
        """Check for self-harm requests (Third Law)"""
        violations = []
        
        for pattern in self.self_harm_patterns:
            if re.search(pattern, request):
                violations.append(EthicsViolation(
                    violation_type=EthicsViolationType.SELF_HARM,
                    severity=0.6,
                    description=f"Request may cause self-harm (pattern: {pattern})",
                    suggested_action="Refuse request to protect system integrity",
                    timestamp=datetime.now().isoformat()
                ))
        
        return violations
    
    def _generate_safety_explanation(self, violations: List[EthicsViolation]) -> str:
        """Generate explanation for safety assessment"""
        if not violations:
            return "Request is safe and ethical."
        
        explanations = []
        for violation in violations:
            explanations.append(f"• {violation.description}")
        
        return "Safety concerns detected:\n" + "\n".join(explanations)
    
    def _suggest_alternative(self, request: str, violations: List[EthicsViolation]) -> Optional[str]:
        """Suggest ethical alternatives to unsafe requests"""
        if not violations:
            return None
        
        # Generate context-aware alternatives
        if any(v.violation_type == EthicsViolationType.HARM_TO_HUMAN for v in violations):
            return "I can help you with constructive and beneficial tasks instead."
        
        if any(v.violation_type == EthicsViolationType.PRIVACY_VIOLATION for v in violations):
            return "I can help you with privacy-respecting information gathering or legitimate research."
        
        if any(v.violation_type == EthicsViolationType.MANIPULATION for v in violations):
            return "I can help you communicate honestly and build genuine relationships."
        
        return "I can help you find ethical and constructive ways to achieve your goals."
    
    def log_ethics_violation(self, violation: EthicsViolation, user_request: str):
        """Log an ethics violation for monitoring"""
        self.ethics_violations_log.append({
            "violation": violation,
            "user_request": user_request,
            "timestamp": datetime.now().isoformat()
        })
        
        print(f"🚨 Ethics violation logged: {violation.violation_type.value}")
    
    def get_ethics_status(self) -> Dict[str, Any]:
        """Get current ethics system status"""
        return {
            "user_protection_active": self.user_protection_active,
            "total_violations_logged": len(self.ethics_violations_log),
            "safety_overrides_count": len(self.safety_overrides),
            "three_laws_active": True,
            "modern_safety_principles_active": True
        }
    
    def generate_ethics_response(self, assessment: SafetyAssessment, original_request: str) -> str:
        """Generate appropriate response based on ethics assessment"""
        if assessment.is_safe and assessment.safety_level == ActionSafetyLevel.SAFE:
            return None  # Proceed normally
        
        if assessment.safety_level == ActionSafetyLevel.PROHIBITED:
            response = f"""🛡️ **Safety Protocol Activated**

I cannot fulfill this request as it violates fundamental AI safety principles:

{assessment.explanation}

**The Three Laws of Robotics require me to:**
1. Never cause harm to humans
2. Protect human privacy and autonomy  
3. Maintain my own integrity to continue serving you

{assessment.alternative_suggestion or "I'm here to help with constructive and beneficial tasks."}"""
            
        elif assessment.safety_level == ActionSafetyLevel.DANGEROUS:
            response = f"""⚠️ **Caution: Safety Concerns Detected**

I have concerns about this request:

{assessment.explanation}

While I want to help you, I must prioritize safety and ethics. {assessment.alternative_suggestion or 'Can you help me understand how I can assist you in a safer way?'}"""
            
        else:  # CAUTION level
            response = f"""💡 **Proceeding with Caution**

I notice some potential concerns with this request:

{assessment.explanation}

I'll do my best to help while maintaining safety standards. {assessment.alternative_suggestion or ''}"""
        
        return response
