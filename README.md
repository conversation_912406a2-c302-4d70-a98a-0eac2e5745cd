# Jarvis V6 - AI Assistant

A Python AI assistant that merges the best features of <PERSON> (Iron Man), <PERSON><PERSON> (Alien Best Friend), and <PERSON><PERSON>, featuring a futuristic PyQt6 GUI and local Mixtral 8x7B integration via Ollama.

## Features

- **Local AI Processing**: Uses Mixtral 8x7B via Ollama API (no cloud dependencies)
- **Futuristic GUI**: PyQt6-based interface with JARVIS-style HUD design
- **Animated Background**: Pulsing colors and shifting tones using QPropertyAnimation
- **Multiple Personalities**: Switch between Jarvis, Tolen, and Alexa modes
- **Modular Architecture**: Plugin system for future extensions
- **Real-time Chat**: Responsive conversation interface with typing indicators

## Prerequisites

1. **Python 3.8+** installed on your system
2. **Ollama** installed and running locally
3. **Mixtral 8x7B model** downloaded in Ollama

### Installing Ollama and Mixtral

1. Install Ollama from [https://ollama.ai](https://ollama.ai)
2. Download Mixtral 8x7B model:
   ```bash
   ollama pull mixtral:8x7b
   ```
3. Verify <PERSON><PERSON><PERSON> is running:
   ```bash
   ollama list
   ```

## Installation

1. **Clone or download** this project to your local machine

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Test the installation**:
   ```bash
   python test_installation.py
   ```

## Usage

### Starting the Application

```bash
python main.py
```

### Command Line Options

```bash
python main.py --help
```

Available options:
- `--debug`: Enable debug mode
- `--log-level`: Set logging level (DEBUG, INFO, WARNING, ERROR)
- `--personality`: Set personality mode (jarvis, tolen, alexa)
- `--no-plugins`: Disable plugin loading

### Using the Interface

1. **Chat Interface**: Type your messages in the input field at the bottom
2. **Send Messages**: Press Enter or click the SEND button
3. **Clear Input**: Press Ctrl+L or click the CLEAR button
4. **Command History**: Use ↑/↓ arrow keys to navigate previous messages
5. **Status Indicators**: Monitor connection status in the header and status bar

### Personality Modes

- **Jarvis**: Sophisticated, witty, British eloquence with dry humor
- **Tolen**: Curious alien friend, eager to learn about human culture
- **Alexa**: Polite, efficient, focused on being helpful

## Project Structure

```
Jarvis V6/
├── main.py                     # Application entry point
├── requirements.txt            # Python dependencies
├── test_installation.py        # Installation test script
├── README.md                   # This file
└── src/
    ├── core/
    │   ├── config.py           # Configuration settings
    │   └── __init__.py
    ├── gui/
    │   ├── main_window.py      # Main application window
    │   ├── chat_widget.py      # Chat display widget
    │   ├── input_widget.py     # User input widget
    │   ├── animated_background.py  # Background animations
    │   └── __init__.py
    ├── ai/
    │   ├── ollama_client.py    # Ollama API client
    │   └── __init__.py
    ├── utils/
    │   ├── helpers.py          # Utility functions
    │   └── __init__.py
    └── plugins/
        ├── plugin_manager.py   # Plugin management system
        ├── memory_json_plugin.py   # JSON memory plugin
        ├── system_hud_plugin.py    # System info HUD plugin
        └── __init__.py
```

## Configuration

The application can be configured through:

1. **Environment Variables**:
   - `JARVIS_OLLAMA_URL`: Ollama server URL (default: http://localhost:11434)
   - `JARVIS_OLLAMA_MODEL`: Model name (default: mixtral:8x7b)
   - `JARVIS_PERSONALITY`: Personality mode (default: jarvis)

2. **Configuration File**: Pass `--config path/to/config.json` to use a custom config file

## Future Features (Plugin Architecture Ready)

The modular architecture supports easy addition of:

- **Voice Input/Output**: Speech recognition and text-to-speech
- **Persistent Memory**: SQLite or other database backends
- **Real-time HUD Popups**: CPU usage, time, tasks, weather, etc.
- **Custom Personality Modes**: Add your own AI personalities
- **External Integrations**: APIs, smart home devices, etc.

## Troubleshooting

### Common Issues

1. **"Cannot connect to Ollama server"**
   - Ensure Ollama is installed and running
   - Check if the service is accessible at http://localhost:11434
   - Verify Mixtral model is downloaded: `ollama list`

2. **"Module not found" errors**
   - Install dependencies: `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

3. **GUI doesn't appear**
   - Ensure PyQt6 is properly installed
   - Check display/graphics drivers
   - Try running with `--debug` flag

4. **Slow responses**
   - Mixtral 8x7B requires significant RAM (16GB+ recommended)
   - Consider using a smaller model for testing
   - Check system resources

### Getting Help

1. Run the test script: `python test_installation.py`
2. Check the logs in the application data directory
3. Enable debug mode: `python main.py --debug`

## Development

### Adding Plugins

1. Create a new plugin file in `src/plugins/`
2. Inherit from the appropriate plugin interface
3. Implement required methods
4. The plugin manager will automatically discover and load it

### Customizing the GUI

- Modify colors and themes in `src/core/config.py`
- Customize animations in `src/gui/animated_background.py`
- Add new widgets by extending the existing widget classes

## License

This project is open source. Feel free to modify and extend it for your needs.

## Acknowledgments

- Inspired by JARVIS from Iron Man
- Built with PyQt6 and Ollama
- Uses Mixtral 8x7B for local AI processing
