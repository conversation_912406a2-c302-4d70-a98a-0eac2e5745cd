#!/usr/bin/env python3
"""
Test script for JARVIS HUD GUI
"""

import sys
import os

def test_imports():
    """Test if all GUI modules can be imported"""
    try:
        print("Testing PyQt6 imports...")
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QColor
        print("✅ PyQt6 imports successful")
        
        print("Testing custom GUI imports...")
        from src.gui.jarvis_hud import JarvisCore
        from src.gui.hud_panels import SystemInfoPanel, TimePanel, StatusPanel
        from src.core.config import Config
        print("✅ Custom GUI imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_gui_creation():
    """Test creating GUI components"""
    try:
        print("Creating QApplication...")
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("✅ QApplication created")
        
        print("Testing config...")
        from src.core.config import Config
        config = Config.load_from_env()
        print("✅ Config loaded")
        
        print("Testing JARVIS core widget...")
        from src.gui.jarvis_hud import JarvisCore
        core = JarvisCore(config)
        print("✅ JARVIS core widget created")
        
        print("Testing HUD panels...")
        from src.gui.hud_panels import SystemInfoPanel, TimePanel, StatusPanel
        system_panel = SystemInfoPanel(config)
        time_panel = TimePanel(config)
        status_panel = StatusPanel(config)
        print("✅ HUD panels created")
        
        # Don't show widgets, just test creation
        return True
        
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run GUI tests"""
    print("=" * 50)
    print("JARVIS HUD GUI TEST")
    print("=" * 50)
    
    if not test_imports():
        print("❌ Import test failed")
        return False
    
    if not test_gui_creation():
        print("❌ GUI creation test failed")
        return False
    
    print("\n🎉 All GUI tests passed!")
    print("The JARVIS HUD interface is ready!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
