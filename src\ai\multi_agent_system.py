"""
Autonomous Multi-Agent AI System for JARVIS V6
A collaborative team of specialized AI agents working together autonomously

This system provides:
- Multiple specialized AI agents with distinct roles
- Inter-agent communication and collaboration
- Task delegation and coordination
- Autonomous decision-making and problem-solving
- Scalable and modular agent architecture
"""

import asyncio
import threading
import time
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import uuid

class AgentRole(Enum):
    """Defines the roles of different agents in the system"""
    PLANNER = "planner"
    CODER = "coder"
    MEMORY = "memory"
    RESEARCHER = "researcher"
    SPEAKER = "speaker"
    TESTER = "tester"
    COORDINATOR = "coordinator"
    MONITOR = "monitor"

class TaskPriority(Enum):
    """Task priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class TaskStatus(Enum):
    """Task status tracking"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    DELEGATED = "delegated"

@dataclass
class AgentMessage:
    """Message structure for inter-agent communication"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender: str = ""
    recipient: str = ""
    message_type: str = "info"  # info, request, response, task, alert
    content: Any = None
    timestamp: datetime = field(default_factory=datetime.now)
    requires_response: bool = False
    response_timeout: int = 30  # seconds

@dataclass
class AgentTask:
    """Task structure for agent work coordination"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    description: str = ""
    assigned_to: str = ""
    created_by: str = ""
    priority: TaskPriority = TaskPriority.MEDIUM
    status: TaskStatus = TaskStatus.PENDING
    dependencies: List[str] = field(default_factory=list)
    result: Any = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_duration: int = 60  # seconds
    context: Dict[str, Any] = field(default_factory=dict)

class BaseAgent:
    """Base class for all AI agents"""
    
    def __init__(self, agent_id: str, role: AgentRole, system_ref=None):
        self.agent_id = agent_id
        self.role = role
        self.system_ref = system_ref
        self.active = True
        self.busy = False
        self.capabilities = []
        self.message_queue = []
        self.current_task = None
        self.completed_tasks = []
        self.performance_metrics = {
            'tasks_completed': 0,
            'success_rate': 1.0,
            'avg_completion_time': 0.0,
            'last_active': datetime.now()
        }
        
        print(f"🤖 Agent {self.agent_id} ({self.role.value}) initialized")
    
    async def process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process incoming message from other agents"""
        print(f"📨 Agent {self.agent_id} received message from {message.sender}: {message.message_type}")
        
        if message.message_type == "task":
            return await self.handle_task_assignment(message)
        elif message.message_type == "request":
            return await self.handle_request(message)
        elif message.message_type == "info":
            await self.handle_info(message)
        
        return None
    
    async def handle_task_assignment(self, message: AgentMessage) -> AgentMessage:
        """Handle task assignment from other agents"""
        task = message.content
        if isinstance(task, dict):
            task = AgentTask(**task)
        
        if self.can_handle_task(task):
            self.current_task = task
            self.busy = True
            task.status = TaskStatus.IN_PROGRESS
            task.started_at = datetime.now()
            
            print(f"🎯 Agent {self.agent_id} accepted task: {task.title}")
            
            # Execute task
            result = await self.execute_task(task)
            
            # Complete task
            task.result = result
            task.status = TaskStatus.COMPLETED if result else TaskStatus.FAILED
            task.completed_at = datetime.now()
            self.completed_tasks.append(task)
            self.current_task = None
            self.busy = False
            
            # Update metrics
            self._update_performance_metrics(task)
            
            return AgentMessage(
                sender=self.agent_id,
                recipient=message.sender,
                message_type="response",
                content={"task_id": task.id, "result": result, "status": task.status.value}
            )
        else:
            print(f"❌ Agent {self.agent_id} cannot handle task: {task.title}")
            return AgentMessage(
                sender=self.agent_id,
                recipient=message.sender,
                message_type="response",
                content={"task_id": task.id, "error": "Cannot handle this task", "status": "rejected"}
            )
    
    async def handle_request(self, message: AgentMessage) -> AgentMessage:
        """Handle information requests from other agents"""
        request_type = message.content.get('type', 'unknown')
        
        if request_type == 'status':
            return AgentMessage(
                sender=self.agent_id,
                recipient=message.sender,
                message_type="response",
                content={
                    'agent_id': self.agent_id,
                    'role': self.role.value,
                    'active': self.active,
                    'busy': self.busy,
                    'current_task': self.current_task.title if self.current_task else None,
                    'capabilities': self.capabilities,
                    'performance': self.performance_metrics
                }
            )
        
        return AgentMessage(
            sender=self.agent_id,
            recipient=message.sender,
            message_type="response",
            content={"error": f"Unknown request type: {request_type}"}
        )
    
    async def handle_info(self, message: AgentMessage):
        """Handle informational messages"""
        print(f"ℹ️ Agent {self.agent_id} received info: {message.content}")
    
    def can_handle_task(self, task: AgentTask) -> bool:
        """Check if agent can handle the given task"""
        return not self.busy and self.active
    
    async def execute_task(self, task: AgentTask) -> Any:
        """Execute the assigned task - to be overridden by specific agents"""
        print(f"🔄 Agent {self.agent_id} executing task: {task.title}")
        await asyncio.sleep(1)  # Simulate work
        return f"Task {task.title} completed by {self.agent_id}"
    
    def _update_performance_metrics(self, task: AgentTask):
        """Update agent performance metrics"""
        self.performance_metrics['tasks_completed'] += 1
        self.performance_metrics['last_active'] = datetime.now()
        
        if task.completed_at and task.started_at:
            duration = (task.completed_at - task.started_at).total_seconds()
            current_avg = self.performance_metrics['avg_completion_time']
            task_count = self.performance_metrics['tasks_completed']
            
            # Update rolling average
            self.performance_metrics['avg_completion_time'] = (
                (current_avg * (task_count - 1) + duration) / task_count
            )
        
        # Update success rate
        successful_tasks = len([t for t in self.completed_tasks if t.status == TaskStatus.COMPLETED])
        self.performance_metrics['success_rate'] = successful_tasks / len(self.completed_tasks) if self.completed_tasks else 1.0

class MultiAgentSystem:
    """Central coordination system for multiple AI agents"""
    
    def __init__(self, config=None):
        self.config = config
        self.agents: Dict[str, BaseAgent] = {}
        self.message_bus = []
        self.task_queue = []
        self.active_tasks = []
        self.completed_tasks = []
        self.system_active = True
        
        # Communication channels
        self.message_handlers = {}
        self.task_coordinators = {}
        
        # Performance tracking
        self.system_metrics = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_agents': 0,
            'system_uptime': datetime.now(),
            'messages_processed': 0
        }
        
        print("🔷 Multi-Agent AI System initialized")
        print("🤖 Ready to deploy specialized AI agents")
    
    def register_agent(self, agent: BaseAgent):
        """Register a new agent with the system"""
        self.agents[agent.agent_id] = agent
        agent.system_ref = self
        self.system_metrics['active_agents'] = len([a for a in self.agents.values() if a.active])
        
        print(f"✅ Registered agent {agent.agent_id} ({agent.role.value})")
        print(f"🤖 Total active agents: {self.system_metrics['active_agents']}")
    
    async def send_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Send message between agents"""
        if message.recipient not in self.agents:
            print(f"❌ Recipient agent {message.recipient} not found")
            return None
        
        recipient_agent = self.agents[message.recipient]
        self.message_bus.append(message)
        self.system_metrics['messages_processed'] += 1
        
        print(f"📤 Sending message from {message.sender} to {message.recipient}")
        
        response = await recipient_agent.process_message(message)
        
        if response:
            self.message_bus.append(response)
            self.system_metrics['messages_processed'] += 1
        
        return response
    
    async def delegate_task(self, task: AgentTask, preferred_agent: str = None) -> bool:
        """Delegate task to appropriate agent"""
        print(f"🎯 Delegating task: {task.title}")
        
        # Find suitable agent
        target_agent = None
        
        if preferred_agent and preferred_agent in self.agents:
            agent = self.agents[preferred_agent]
            if agent.can_handle_task(task):
                target_agent = agent
        
        if not target_agent:
            # Find best available agent
            available_agents = [a for a in self.agents.values() if a.can_handle_task(task)]
            if available_agents:
                # Select agent with best performance and lowest workload
                target_agent = min(available_agents, key=lambda a: (
                    not a.active,  # Prefer active agents
                    a.busy,        # Prefer non-busy agents
                    -a.performance_metrics['success_rate']  # Prefer high success rate
                ))
        
        if target_agent:
            task.assigned_to = target_agent.agent_id
            task.status = TaskStatus.DELEGATED
            
            message = AgentMessage(
                sender="system",
                recipient=target_agent.agent_id,
                message_type="task",
                content=task.__dict__,
                requires_response=True
            )
            
            self.active_tasks.append(task)
            self.system_metrics['total_tasks'] += 1
            
            response = await self.send_message(message)
            
            if response and response.content.get('status') != 'rejected':
                print(f"✅ Task delegated to agent {target_agent.agent_id}")
                return True
            else:
                print(f"❌ Task rejected by agent {target_agent.agent_id}")
                task.status = TaskStatus.FAILED
                return False
        else:
            print(f"❌ No suitable agent found for task: {task.title}")
            task.status = TaskStatus.FAILED
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        active_agents = [a for a in self.agents.values() if a.active]
        busy_agents = [a for a in active_agents if a.busy]
        
        return {
            'system_active': self.system_active,
            'total_agents': len(self.agents),
            'active_agents': len(active_agents),
            'busy_agents': len(busy_agents),
            'pending_tasks': len([t for t in self.active_tasks if t.status == TaskStatus.PENDING]),
            'in_progress_tasks': len([t for t in self.active_tasks if t.status == TaskStatus.IN_PROGRESS]),
            'completed_tasks': len(self.completed_tasks),
            'system_metrics': self.system_metrics,
            'agent_details': {
                agent_id: {
                    'role': agent.role.value,
                    'active': agent.active,
                    'busy': agent.busy,
                    'current_task': agent.current_task.title if agent.current_task else None,
                    'performance': agent.performance_metrics
                }
                for agent_id, agent in self.agents.items()
            }
        }
    
    async def coordinate_complex_task(self, main_task: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Coordinate a complex task across multiple agents"""
        print(f"🎯 Coordinating complex task: {main_task}")
        
        # This will be implemented by specific agent types
        # For now, return a basic coordination result
        return {
            'task': main_task,
            'status': 'coordinated',
            'agents_involved': list(self.agents.keys()),
            'context': context or {}
        }
