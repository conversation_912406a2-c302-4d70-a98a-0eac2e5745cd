"""
Simple Iron Man JARVIS HUD Interface
===================================
A more reliable Iron Man-style interface using standard Qt widgets
with custom styling to achieve the futuristic look

Features:
- Iron Man color scheme (blue/cyan glows)
- Animated elements using QPropertyAnimation
- Real-time system monitoring
- Futuristic layout and styling
- Integrated chat and input
"""

import sys
import random
from typing import Dict, Any
from datetime import datetime

from src.gui.qt_compat import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QFrame, QProgressBar, QTimer,
                               Qt, QPropertyAnimation, QEasingCurve, QFont,
                               QGraphicsOpacityEffect)
from src.gui.chat_widget import ChatWidget
from src.gui.input_widget import InputWidget

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

class AnimatedLabel(QLabel):
    """Label with pulsing glow animation"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        
        # Set up glow effect
        self.glow_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.glow_effect)
        
        # Animation for pulsing effect
        self.animation = QPropertyAnimation(self.glow_effect, b"opacity")
        self.animation.setDuration(2000)  # 2 seconds
        self.animation.setStartValue(0.5)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        self.animation.setLoopCount(-1)  # Infinite loop
        self.animation.start()

class SystemMonitorWidget(QFrame):
    """System monitoring widget with Iron Man styling"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("systemMonitor")
        
        layout = QVBoxLayout(self)
        
        # Title
        title = AnimatedLabel("SYSTEM STATUS")
        title.setObjectName("hudTitle")
        layout.addWidget(title)
        
        # CPU Monitor
        cpu_layout = QHBoxLayout()
        self.cpu_label = QLabel("CPU:")
        self.cpu_label.setObjectName("hudLabel")
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setObjectName("hudProgressBar")
        self.cpu_value = QLabel("0%")
        self.cpu_value.setObjectName("hudValue")
        cpu_layout.addWidget(self.cpu_label)
        cpu_layout.addWidget(self.cpu_bar)
        cpu_layout.addWidget(self.cpu_value)
        layout.addLayout(cpu_layout)
        
        # Memory Monitor
        mem_layout = QHBoxLayout()
        self.mem_label = QLabel("RAM:")
        self.mem_label.setObjectName("hudLabel")
        self.mem_bar = QProgressBar()
        self.mem_bar.setObjectName("hudProgressBar")
        self.mem_value = QLabel("0%")
        self.mem_value.setObjectName("hudValue")
        mem_layout.addWidget(self.mem_label)
        mem_layout.addWidget(self.mem_bar)
        mem_layout.addWidget(self.mem_value)
        layout.addLayout(mem_layout)
        
        # Disk Monitor
        disk_layout = QHBoxLayout()
        self.disk_label = QLabel("DISK:")
        self.disk_label.setObjectName("hudLabel")
        self.disk_bar = QProgressBar()
        self.disk_bar.setObjectName("hudProgressBar")
        self.disk_value = QLabel("0%")
        self.disk_value.setObjectName("hudValue")
        disk_layout.addWidget(self.disk_label)
        disk_layout.addWidget(self.disk_bar)
        disk_layout.addWidget(self.disk_value)
        layout.addLayout(disk_layout)
        
        # Update timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_stats)
        self.timer.start(1000)  # Update every second
    
    def update_stats(self):
        """Update system statistics"""
        if PSUTIL_AVAILABLE:
            try:
                cpu = psutil.cpu_percent()
                memory = psutil.virtual_memory().percent
                disk = psutil.disk_usage('/').percent
            except:
                cpu = random.uniform(10, 80)
                memory = random.uniform(20, 70)
                disk = random.uniform(30, 60)
        else:
            cpu = random.uniform(10, 80)
            memory = random.uniform(20, 70)
            disk = random.uniform(30, 60)
        
        self.cpu_bar.setValue(int(cpu))
        self.cpu_value.setText(f"{cpu:.1f}%")
        
        self.mem_bar.setValue(int(memory))
        self.mem_value.setText(f"{memory:.1f}%")
        
        self.disk_bar.setValue(int(disk))
        self.disk_value.setText(f"{disk:.1f}%")

class AIStatusWidget(QFrame):
    """AI status widget with Iron Man styling"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("aiStatus")
        
        layout = QVBoxLayout(self)
        
        # Title
        title = AnimatedLabel("JARVIS AI STATUS")
        title.setObjectName("hudTitle")
        layout.addWidget(title)
        
        # Status indicators
        self.agents_status = QLabel("AGENTS: 8 ACTIVE")
        self.agents_status.setObjectName("hudStatusGreen")
        layout.addWidget(self.agents_status)
        
        self.memory_status = QLabel("MEMORY: ONLINE")
        self.memory_status.setObjectName("hudStatusGreen")
        layout.addWidget(self.memory_status)
        
        self.learning_status = QLabel("LEARNING: ACTIVE")
        self.learning_status.setObjectName("hudStatusGreen")
        layout.addWidget(self.learning_status)
        
        self.evolution_status = QLabel("EVOLUTION: ENABLED")
        self.evolution_status.setObjectName("hudStatusGreen")
        layout.addWidget(self.evolution_status)
        
        self.autonomous_status = QLabel("AUTONOMOUS: PROACTIVE")
        self.autonomous_status.setObjectName("hudStatusBlue")
        layout.addWidget(self.autonomous_status)

class SimpleIronManHUD(QMainWindow):
    """Simple but effective Iron Man JARVIS HUD"""
    
    def __init__(self, config, main_window, parent=None):
        super().__init__(parent)
        self.config = config
        self.main_window = main_window
        
        self.init_hud_ui()
        self.setup_hud_styling()
        self.setup_connections()
        
        print("🚀 Simple Iron Man HUD initialized")
    
    def init_hud_ui(self):
        """Initialize the HUD UI"""
        self.setWindowTitle("J.A.R.V.I.S. - IRON MAN HUD INTERFACE")
        self.setGeometry(100, 100, 1400, 900)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QGridLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Top section - Title and status
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_layout = QVBoxLayout(title_frame)
        
        main_title = AnimatedLabel("J.A.R.V.I.S.")
        main_title.setObjectName("mainTitle")
        main_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(main_title)
        
        subtitle = QLabel("JUST A RATHER VERY INTELLIGENT SYSTEM")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(subtitle)
        
        main_layout.addWidget(title_frame, 0, 0, 1, 3)
        
        # Left panel - System monitoring
        self.system_monitor = SystemMonitorWidget()
        main_layout.addWidget(self.system_monitor, 1, 0)
        
        # Center panel - Chat
        chat_frame = QFrame()
        chat_frame.setObjectName("chatFrame")
        chat_layout = QVBoxLayout(chat_frame)
        
        chat_title = QLabel("COMMUNICATION INTERFACE")
        chat_title.setObjectName("hudTitle")
        chat_layout.addWidget(chat_title)
        
        self.chat_widget = ChatWidget(self.config)
        self.chat_widget.setMaximumHeight(400)
        chat_layout.addWidget(self.chat_widget)
        
        main_layout.addWidget(chat_frame, 1, 1)
        
        # Right panel - AI status
        self.ai_status = AIStatusWidget()
        main_layout.addWidget(self.ai_status, 1, 2)
        
        # Bottom panel - Input and controls
        bottom_frame = QFrame()
        bottom_frame.setObjectName("bottomFrame")
        bottom_layout = QVBoxLayout(bottom_frame)
        
        # Input section
        input_title = QLabel("COMMAND INTERFACE")
        input_title.setObjectName("hudTitle")
        bottom_layout.addWidget(input_title)
        
        self.input_widget = InputWidget(self.config)
        bottom_layout.addWidget(self.input_widget)
        
        # Control buttons
        controls_layout = QHBoxLayout()
        
        self.exit_button = QPushButton("❌ EXIT HUD")
        self.exit_button.setObjectName("hudButton")
        controls_layout.addWidget(self.exit_button)
        
        self.fullscreen_button = QPushButton("🖥️ FULLSCREEN")
        self.fullscreen_button.setObjectName("hudButton")
        self.fullscreen_button.setCheckable(True)
        controls_layout.addWidget(self.fullscreen_button)
        
        self.voice_button = QPushButton("🎤 VOICE")
        self.voice_button.setObjectName("hudButton")
        self.voice_button.setCheckable(True)
        controls_layout.addWidget(self.voice_button)
        
        bottom_layout.addLayout(controls_layout)
        main_layout.addWidget(bottom_frame, 2, 0, 1, 3)
    
    def setup_hud_styling(self):
        """Setup Iron Man HUD styling"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qradialgradient(cx:0.5, cy:0.5, radius:1,
                    stop:0 rgba(0, 20, 40, 255),
                    stop:0.5 rgba(0, 10, 20, 255),
                    stop:1 rgba(0, 5, 10, 255));
            }}
            
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 30, 60, 0.8),
                    stop:1 rgba(0, 20, 40, 0.6));
                border: 2px solid rgba(0, 150, 255, 0.8);
                border-radius: 15px;
                margin: 5px;
            }}
            
            #mainTitle {{
                color: #00CCFF;
                font-size: 36px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                text-shadow: 0 0 20px #00CCFF;
            }}
            
            #subtitle {{
                color: #0099CC;
                font-size: 14px;
                font-family: 'Consolas', 'Monaco', monospace;
                text-shadow: 0 0 10px #0099CC;
            }}
            
            #hudTitle {{
                color: #00FFFF;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                text-shadow: 0 0 15px #00FFFF;
                margin: 10px;
            }}
            
            #hudLabel {{
                color: #00CCFF;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                min-width: 50px;
            }}
            
            #hudValue {{
                color: #00FFFF;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                min-width: 50px;
                text-shadow: 0 0 10px #00FFFF;
            }}
            
            #hudProgressBar {{
                border: 1px solid rgba(0, 150, 255, 0.8);
                border-radius: 5px;
                background: rgba(0, 20, 40, 0.8);
                text-align: center;
                color: #00FFFF;
            }}
            
            #hudProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 255, 0.8),
                    stop:1 rgba(0, 200, 255, 1.0));
                border-radius: 3px;
            }}
            
            #hudStatusGreen {{
                color: #00FF88;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                text-shadow: 0 0 10px #00FF88;
                margin: 5px;
            }}
            
            #hudStatusBlue {{
                color: #0088FF;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Consolas', 'Monaco', monospace;
                text-shadow: 0 0 10px #0088FF;
                margin: 5px;
            }}
            
            #hudButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0, 100, 200, 0.8),
                    stop:1 rgba(0, 50, 100, 0.8));
                border: 2px solid rgba(0, 150, 255, 0.8);
                border-radius: 10px;
                color: #00CCFF;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                padding: 10px;
                margin: 5px;
                text-shadow: 0 0 10px #00CCFF;
            }}
            
            #hudButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0, 150, 255, 0.9),
                    stop:1 rgba(0, 100, 200, 0.9));
                border: 3px solid rgba(0, 200, 255, 1.0);
            }}
            
            #hudButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0, 255, 100, 0.8),
                    stop:1 rgba(0, 200, 50, 0.8));
                border: 3px solid rgba(0, 255, 150, 1.0);
                color: #00FF00;
            }}
        """)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.input_widget.message_sent.connect(self.handle_message)
        self.input_widget.special_command.connect(self.handle_command)
        
        self.exit_button.clicked.connect(self.exit_hud)
        self.fullscreen_button.clicked.connect(self.toggle_fullscreen)
        self.voice_button.clicked.connect(self.toggle_voice)
    
    def handle_message(self, message: str):
        """Handle message from input"""
        self.chat_widget.add_user_message(message)
        if self.main_window:
            self.main_window.handle_user_message(message)
    
    def handle_command(self, command: str):
        """Handle special command"""
        if self.main_window:
            self.main_window.handle_special_command(command)
    
    def add_ai_response(self, response: str):
        """Add AI response to chat"""
        self.chat_widget.add_ai_message(response)
    
    def exit_hud(self):
        """Exit HUD mode"""
        if self.main_window:
            self.main_window.hud_button.setChecked(False)
            self.main_window.toggle_iron_man_hud()
        self.close()
    
    def toggle_fullscreen(self):
        """Toggle fullscreen"""
        if self.fullscreen_button.isChecked():
            self.showFullScreen()
            self.fullscreen_button.setText("🪟 WINDOW")
        else:
            self.showNormal()
            self.fullscreen_button.setText("🖥️ FULLSCREEN")
    
    def toggle_voice(self):
        """Toggle voice mode"""
        if self.voice_button.isChecked():
            self.voice_button.setText("🔇 VOICE OFF")
        else:
            self.voice_button.setText("🎤 VOICE")
    
    def keyPressEvent(self, event):
        """Handle key press"""
        if event.key() == Qt.Key.Key_Escape:
            self.exit_hud()
        else:
            super().keyPressEvent(event)
