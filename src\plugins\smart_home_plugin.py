"""
JARVIS V6 Smart Home Control Plugin
Supports multiple smart home platforms and devices
"""

import json
import requests
import asyncio
import socket
import struct
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import os
from datetime import datetime

# Import msmart for real Midea AC control
try:
    import msmart
    from msmart.device import air_conditioning as AC
    MSMART_AVAILABLE = True
    print("✅ msmart library loaded successfully")
except ImportError as e:
    MSMART_AVAILABLE = False
    print(f"⚠️ msmart library not available: {e}. Real Midea AC control disabled.")

class DeviceType(Enum):
    LIGHT = "light"
    SWITCH = "switch"
    THERMOSTAT = "thermostat"
    FAN = "fan"
    LOCK = "lock"
    CAMERA = "camera"
    SENSOR = "sensor"
    SPEAKER = "speaker"
    TV = "tv"
    OUTLET = "outlet"
    AC = "ac"  # Air Conditioner

class DeviceState(Enum):
    ON = "on"
    OFF = "off"
    UNKNOWN = "unknown"

@dataclass
class SmartDevice:
    """Represents a smart home device"""
    id: str
    name: str
    device_type: DeviceType
    state: DeviceState
    platform: str
    room: str = "Unknown"
    brightness: Optional[int] = None
    temperature: Optional[float] = None
    color: Optional[str] = None
    last_updated: Optional[datetime] = None
    # AC-specific properties
    target_temperature: Optional[float] = None
    current_temperature: Optional[float] = None
    mode: Optional[str] = None  # cool, heat, auto, fan, dry
    fan_speed: Optional[str] = None  # low, medium, high, auto

class SmartHomePlatform:
    """Base class for smart home platforms"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.devices: Dict[str, SmartDevice] = {}
        self.connected = False
    
    async def connect(self) -> bool:
        """Connect to the platform"""
        raise NotImplementedError
    
    async def discover_devices(self) -> List[SmartDevice]:
        """Discover available devices"""
        raise NotImplementedError
    
    async def turn_on(self, device_id: str) -> bool:
        """Turn on a device"""
        raise NotImplementedError
    
    async def turn_off(self, device_id: str) -> bool:
        """Turn off a device"""
        raise NotImplementedError
    
    async def set_brightness(self, device_id: str, brightness: int) -> bool:
        """Set device brightness (0-100)"""
        raise NotImplementedError
    
    async def set_temperature(self, device_id: str, temperature: float) -> bool:
        """Set device temperature"""
        raise NotImplementedError

class PhilipsHuePlatform(SmartHomePlatform):
    """Philips Hue smart lights integration"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("Philips Hue", config)
        self.bridge_ip = config.get("bridge_ip", "")
        self.username = config.get("username", "")
        self.base_url = f"http://{self.bridge_ip}/api/{self.username}"
    
    async def connect(self) -> bool:
        """Connect to Hue Bridge"""
        try:
            response = requests.get(f"{self.base_url}/lights", timeout=5)
            if response.status_code == 200:
                self.connected = True
                return True
        except Exception as e:
            print(f"Hue connection error: {e}")
        return False
    
    async def discover_devices(self) -> List[SmartDevice]:
        """Discover Hue lights"""
        devices = []
        try:
            response = requests.get(f"{self.base_url}/lights", timeout=5)
            if response.status_code == 200:
                lights = response.json()
                for light_id, light_data in lights.items():
                    device = SmartDevice(
                        id=f"hue_{light_id}",
                        name=light_data.get("name", f"Hue Light {light_id}"),
                        device_type=DeviceType.LIGHT,
                        state=DeviceState.ON if light_data.get("state", {}).get("on", False) else DeviceState.OFF,
                        platform="Philips Hue",
                        brightness=light_data.get("state", {}).get("bri", 0),
                        last_updated=datetime.now()
                    )
                    devices.append(device)
                    self.devices[device.id] = device
        except Exception as e:
            print(f"Hue discovery error: {e}")
        return devices
    
    async def turn_on(self, device_id: str) -> bool:
        """Turn on Hue light"""
        try:
            light_id = device_id.replace("hue_", "")
            response = requests.put(
                f"{self.base_url}/lights/{light_id}/state",
                json={"on": True},
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            print(f"Hue turn on error: {e}")
            return False
    
    async def turn_off(self, device_id: str) -> bool:
        """Turn off Hue light"""
        try:
            light_id = device_id.replace("hue_", "")
            response = requests.put(
                f"{self.base_url}/lights/{light_id}/state",
                json={"on": False},
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            print(f"Hue turn off error: {e}")
            return False
    
    async def set_brightness(self, device_id: str, brightness: int) -> bool:
        """Set Hue light brightness"""
        try:
            light_id = device_id.replace("hue_", "")
            # Hue brightness range is 1-254
            hue_brightness = max(1, min(254, int(brightness * 2.54)))
            response = requests.put(
                f"{self.base_url}/lights/{light_id}/state",
                json={"bri": hue_brightness, "on": True},
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            print(f"Hue brightness error: {e}")
            return False

class TPLinkKasaPlatform(SmartHomePlatform):
    """TP-Link Kasa smart devices integration"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("TP-Link Kasa", config)
        self.devices_config = config.get("devices", [])
    
    async def connect(self) -> bool:
        """Connect to Kasa devices"""
        self.connected = True
        return True
    
    async def discover_devices(self) -> List[SmartDevice]:
        """Discover Kasa devices"""
        devices = []
        for device_config in self.devices_config:
            device = SmartDevice(
                id=f"kasa_{device_config['ip'].replace('.', '_')}",
                name=device_config.get("name", f"Kasa Device"),
                device_type=DeviceType(device_config.get("type", "switch")),
                state=DeviceState.UNKNOWN,
                platform="TP-Link Kasa",
                room=device_config.get("room", "Unknown"),
                last_updated=datetime.now()
            )
            devices.append(device)
            self.devices[device.id] = device
        return devices
    
    async def turn_on(self, device_id: str) -> bool:
        """Turn on Kasa device"""
        # This would require the python-kasa library for actual implementation
        print(f"Turning on Kasa device: {device_id}")
        return True
    
    async def turn_off(self, device_id: str) -> bool:
        """Turn off Kasa device"""
        print(f"Turning off Kasa device: {device_id}")
        return True

class MideaACPlatform(SmartHomePlatform):
    """Midea Air Conditioner integration"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("Midea AC", config)
        self.devices_config = config.get("devices", [])
        self.use_demo = config.get("demo_mode", True)  # Default to demo for testing
        self.device_connections = {}  # Store device connection info
        self.msmart_devices = {}  # Store msmart device objects

    async def connect(self) -> bool:
        """Connect to Midea AC devices"""
        if self.use_demo:
            print("🌡️ Midea AC: Running in demo mode")
            self.connected = True
            return True

        print("🌡️ Midea AC: Connecting to real devices...")
        success_count = 0

        # Skip auto-discovery for now, connect directly to configured devices
        print("🔗 Connecting to configured Midea devices...")

        for device_config in self.devices_config:
            device_ip = device_config.get("ip")
            device_name = device_config.get("name", "Midea AC")
            device_id = device_config.get("id")

            if device_ip:
                # Test network connectivity
                if await self._test_device_connection(device_ip):
                    print(f"✅ {device_name} at {device_ip}: Network connection OK")

                    # Try to create msmart device connection
                    if MSMART_AVAILABLE:
                        try:
                            # Create AC device object with required parameters
                            # Use a default device_id and port 6444 for Midea ACs
                            midea_device = AC(device_ip, device_id=0x123456789, device_port=6444)

                            # Try to authenticate (this might fail without proper credentials)
                            try:
                                midea_device.authenticate()
                                print(f"🔐 {device_name}: Authentication successful")
                                self.msmart_devices[device_id] = midea_device
                            except Exception as auth_error:
                                print(f"⚠️ {device_name}: Authentication failed - {auth_error}")
                                print(f"💡 Device will work in limited mode")
                                self.msmart_devices[device_id] = midea_device

                        except Exception as e:
                            print(f"❌ {device_name}: Failed to create msmart device - {e}")

                    self.device_connections[device_id] = {
                        "ip": device_ip,
                        "mac": device_config.get("mac"),
                        "connected": True,
                        "last_seen": datetime.now()
                    }
                    success_count += 1
                else:
                    print(f"❌ {device_name} at {device_ip}: Network connection failed")
                    self.device_connections[device_id] = {
                        "ip": device_ip,
                        "mac": device_config.get("mac"),
                        "connected": False,
                        "last_seen": None
                    }

        if success_count > 0:
            print(f"🌡️ Midea AC: {success_count}/{len(self.devices_config)} devices connected")
            self.connected = True
            return True
        else:
            print("❌ Midea AC: No devices could be reached")
            return False

    async def discover_devices(self) -> List[SmartDevice]:
        """Discover Midea AC devices"""
        devices = []

        for device_config in self.devices_config:
            device = SmartDevice(
                id=f"midea_{device_config.get('id', 'ac_1')}",
                name=device_config.get("name", "Midea AC"),
                device_type=DeviceType.AC,
                state=DeviceState(device_config.get("state", "off")),
                platform="Midea AC",
                room=device_config.get("room", "Living Room"),
                target_temperature=device_config.get("target_temperature", 24.0),
                current_temperature=device_config.get("current_temperature", 22.0),
                mode=device_config.get("mode", "cool"),
                fan_speed=device_config.get("fan_speed", "auto"),
                last_updated=datetime.now()
            )
            devices.append(device)
            self.devices[device.id] = device

        return devices

    async def turn_on(self, device_id: str) -> bool:
        """Turn on Midea AC"""
        if device_id in self.devices:
            if self.use_demo:
                self.devices[device_id].state = DeviceState.ON
                print(f"🌡️ Demo: Turned ON Midea AC {device_id}")
                return True
            else:
                # Send real command to Midea AC
                command = {
                    "action": "power_on",
                    "device_id": device_id,
                    "timestamp": time.time()
                }

                if await self._send_midea_command(device_id, command):
                    self.devices[device_id].state = DeviceState.ON
                    print(f"🌡️ Real: Turned ON Midea AC {device_id}")
                    return True
                else:
                    print(f"❌ Failed to turn ON Midea AC {device_id}")
                    return False
        return False

    async def turn_off(self, device_id: str) -> bool:
        """Turn off Midea AC"""
        if device_id in self.devices:
            if self.use_demo:
                self.devices[device_id].state = DeviceState.OFF
                print(f"🌡️ Demo: Turned OFF Midea AC {device_id}")
                return True
            else:
                # Send real command to Midea AC
                command = {
                    "action": "power_off",
                    "device_id": device_id,
                    "timestamp": time.time()
                }

                if await self._send_midea_command(device_id, command):
                    self.devices[device_id].state = DeviceState.OFF
                    print(f"🌡️ Real: Turned OFF Midea AC {device_id}")
                    return True
                else:
                    print(f"❌ Failed to turn OFF Midea AC {device_id}")
                    return False
        return False

    async def set_temperature(self, device_id: str, temperature: float) -> bool:
        """Set AC target temperature"""
        if device_id in self.devices:
            if self.use_demo:
                self.devices[device_id].target_temperature = temperature
                self.devices[device_id].state = DeviceState.ON  # AC turns on when temp is set
                print(f"🌡️ Demo: Set Midea AC {device_id} to {temperature}°C")
                return True
            else:
                # Send real temperature command to Midea AC
                command = {
                    "action": "set_temperature",
                    "device_id": device_id,
                    "temperature": temperature,
                    "timestamp": time.time()
                }

                if await self._send_midea_command(device_id, command):
                    self.devices[device_id].target_temperature = temperature
                    self.devices[device_id].state = DeviceState.ON  # AC turns on when temp is set
                    print(f"🌡️ Real: Set Midea AC {device_id} to {temperature}°C")
                    return True
                else:
                    print(f"❌ Failed to set temperature for Midea AC {device_id}")
                    return False
        return False

    async def set_mode(self, device_id: str, mode: str) -> bool:
        """Set AC mode (cool, heat, auto, fan, dry)"""
        if device_id in self.devices:
            valid_modes = ["cool", "heat", "auto", "fan", "dry"]
            if mode.lower() in valid_modes:
                if self.use_demo:
                    self.devices[device_id].mode = mode.lower()
                    print(f"🌡️ Demo: Set Midea AC {device_id} mode to {mode}")
                    return True
                else:
                    # Send real mode command to Midea AC
                    command = {
                        "action": "set_mode",
                        "device_id": device_id,
                        "mode": mode.lower(),
                        "timestamp": time.time()
                    }

                    if await self._send_midea_command(device_id, command):
                        self.devices[device_id].mode = mode.lower()
                        print(f"🌡️ Real: Set Midea AC {device_id} mode to {mode}")
                        return True
                    else:
                        print(f"❌ Failed to set mode for Midea AC {device_id}")
                        return False
        return False

    async def _test_device_connection(self, ip: str, port: int = 6444, timeout: int = 5) -> bool:
        """Test network connectivity to Midea AC device"""
        try:
            # Create a socket connection to test if device is reachable
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)

            # Try to connect to common Midea AC ports
            ports_to_try = [6444, 80, 443, 8080]

            for test_port in ports_to_try:
                try:
                    result = sock.connect_ex((ip, test_port))
                    if result == 0:
                        sock.close()
                        return True
                except:
                    continue

            sock.close()

            # If socket connection fails, try ping-like test
            return await self._ping_device(ip)

        except Exception as e:
            print(f"🌡️ Connection test error for {ip}: {e}")
            return False

    async def _ping_device(self, ip: str) -> bool:
        """Simple ping test to check if device is reachable"""
        try:
            import subprocess
            import platform

            # Use ping command based on OS
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "1", "-w", "3000", ip]
            else:
                cmd = ["ping", "-c", "1", "-W", "3", ip]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            return result.returncode == 0

        except Exception as e:
            print(f"🌡️ Ping test error for {ip}: {e}")
            return False

    async def _send_midea_command(self, device_id: str, command: Dict[str, Any]) -> bool:
        """Send command to real Midea AC device using msmart"""
        if device_id not in self.device_connections:
            return False

        connection = self.device_connections[device_id]
        if not connection.get("connected"):
            return False

        device_ip = connection["ip"]

        try:
            # Use msmart if available and device is connected
            if MSMART_AVAILABLE and device_id in self.msmart_devices:
                midea_device = self.msmart_devices[device_id]
                action = command.get("action")

                print(f"🌡️ Sending {action} command to Midea AC at {device_ip}")

                if action == "power_on":
                    midea_device.power_state = True
                    midea_device.apply()
                    return True

                elif action == "power_off":
                    midea_device.power_state = False
                    midea_device.apply()
                    return True

                elif action == "set_temperature":
                    temperature = command.get("temperature")
                    if temperature:
                        midea_device.target_temperature = int(temperature)
                        midea_device.power_state = True  # Turn on when setting temperature
                        midea_device.apply()
                        return True

                elif action == "set_mode":
                    mode = command.get("mode")
                    if mode:
                        # Map our modes to msmart modes
                        mode_mapping = {
                            "cool": 0,
                            "dry": 1,
                            "wind": 2,
                            "heat": 3,
                            "auto": 4
                        }
                        if mode in mode_mapping:
                            midea_device.operational_mode = mode_mapping[mode]
                            midea_device.apply()
                            return True

                return False

            else:
                # Fallback to basic network communication
                print(f"🌡️ Sending command to Midea AC at {device_ip}: {command}")
                print("⚠️ Using basic mode - msmart not available or device not authenticated")

                # Simulate command for now
                await asyncio.sleep(0.5)
                return True

        except Exception as e:
            print(f"❌ Failed to send command to {device_ip}: {e}")
            return False

class DemoSmartHomePlatform(SmartHomePlatform):
    """Demo platform for testing without real devices"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("Demo Mode", config)
        self.demo_devices = config.get("devices", [])
        self.device_states = {}

    async def connect(self) -> bool:
        """Connect to demo platform"""
        self.connected = True
        return True

    async def discover_devices(self) -> List[SmartDevice]:
        """Discover demo devices"""
        devices = []
        for i, device_config in enumerate(self.demo_devices):
            device = SmartDevice(
                id=f"demo_{i}",
                name=device_config.get("name", f"Demo Device {i}"),
                device_type=DeviceType(device_config.get("type", "light")),
                state=DeviceState(device_config.get("state", "off")),
                platform="Demo Mode",
                room=device_config.get("room", "Unknown"),
                temperature=device_config.get("temperature"),
                last_updated=datetime.now()
            )
            devices.append(device)
            self.devices[device.id] = device
            self.device_states[device.id] = device.state
        return devices

    async def turn_on(self, device_id: str) -> bool:
        """Turn on demo device"""
        if device_id in self.devices:
            self.devices[device_id].state = DeviceState.ON
            self.device_states[device_id] = DeviceState.ON
            return True
        return False

    async def turn_off(self, device_id: str) -> bool:
        """Turn off demo device"""
        if device_id in self.devices:
            self.devices[device_id].state = DeviceState.OFF
            self.device_states[device_id] = DeviceState.OFF
            return True
        return False

    async def set_brightness(self, device_id: str, brightness: int) -> bool:
        """Set demo device brightness"""
        if device_id in self.devices:
            self.devices[device_id].brightness = brightness
            self.devices[device_id].state = DeviceState.ON
            return True
        return False

class SmartHomeManager:
    """Main smart home control manager"""

    def __init__(self):
        self.platforms: Dict[str, SmartHomePlatform] = {}
        self.devices: Dict[str, SmartDevice] = {}
        self.rooms: Dict[str, List[str]] = {}
        self.config_file = "smart_home_config.json"
        self.load_config()
    
    def load_config(self):
        """Load smart home configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.setup_platforms(config)
        except Exception as e:
            print(f"Config load error: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """Create default configuration file"""
        default_config = {
            "platforms": {
                "philips_hue": {
                    "enabled": False,
                    "bridge_ip": "*************",
                    "username": "your_hue_username"
                },
                "tplink_kasa": {
                    "enabled": False,
                    "devices": [
                        {
                            "name": "Living Room Light",
                            "ip": "*************",
                            "type": "light",
                            "room": "Living Room"
                        }
                    ]
                }
            },
            "rooms": {
                "Living Room": [],
                "Bedroom": [],
                "Kitchen": [],
                "Office": []
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
    
    def setup_platforms(self, config: Dict[str, Any]):
        """Setup smart home platforms"""
        platforms_config = config.get("platforms", {})
        
        # Setup Philips Hue
        if platforms_config.get("philips_hue", {}).get("enabled", False):
            hue_config = platforms_config["philips_hue"]
            self.platforms["philips_hue"] = PhilipsHuePlatform(hue_config)
        
        # Setup TP-Link Kasa
        if platforms_config.get("tplink_kasa", {}).get("enabled", False):
            kasa_config = platforms_config["tplink_kasa"]
            self.platforms["tplink_kasa"] = TPLinkKasaPlatform(kasa_config)

        # Setup Midea AC
        if platforms_config.get("midea_ac", {}).get("enabled", False):
            midea_config = platforms_config["midea_ac"]
            self.platforms["midea_ac"] = MideaACPlatform(midea_config)

        # Setup Demo Mode
        if platforms_config.get("demo_mode", {}).get("enabled", False):
            demo_config = platforms_config["demo_mode"]
            self.platforms["demo_mode"] = DemoSmartHomePlatform(demo_config)
    
    async def initialize(self) -> bool:
        """Initialize all platforms and discover devices"""
        success = True
        for platform_name, platform in self.platforms.items():
            try:
                if await platform.connect():
                    devices = await platform.discover_devices()
                    for device in devices:
                        self.devices[device.id] = device
                        # Organize by room
                        if device.room not in self.rooms:
                            self.rooms[device.room] = []
                        self.rooms[device.room].append(device.id)
                    print(f"✅ {platform_name}: {len(devices)} devices discovered")
                else:
                    print(f"❌ {platform_name}: Connection failed")
                    success = False
            except Exception as e:
                print(f"❌ {platform_name}: Error - {e}")
                success = False
        
        return success
    
    async def turn_on_device(self, device_name: str) -> bool:
        """Turn on a device by name"""
        device = self.find_device_by_name(device_name)
        if device:
            platform = self.platforms.get(device.platform.lower().replace(" ", "_").replace("-", "_"))
            if platform:
                return await platform.turn_on(device.id)
        return False
    
    async def turn_off_device(self, device_name: str) -> bool:
        """Turn off a device by name"""
        device = self.find_device_by_name(device_name)
        if device:
            platform = self.platforms.get(device.platform.lower().replace(" ", "_").replace("-", "_"))
            if platform:
                return await platform.turn_off(device.id)
        return False
    
    async def control_room(self, room_name: str, action: str) -> int:
        """Control all devices in a room"""
        # Normalize room name
        normalized_room = room_name.replace("_", " ").title()

        # Find devices in the room
        room_devices = []
        for device in self.devices.values():
            if device.room.lower() == room_name.lower() or device.room.lower() == normalized_room.lower():
                room_devices.append(device)

        if not room_devices:
            return 0

        controlled = 0
        for device in room_devices:
            platform_key = device.platform.lower().replace(" ", "_").replace("-", "_")
            platform = self.platforms.get(platform_key)
            if platform:
                if action.lower() == "on":
                    if await platform.turn_on(device.id):
                        controlled += 1
                elif action.lower() == "off":
                    if await platform.turn_off(device.id):
                        controlled += 1

        return controlled
    
    def find_device_by_name(self, name: str) -> Optional[SmartDevice]:
        """Find device by name (case insensitive)"""
        name_lower = name.lower()

        # First try exact match
        for device in self.devices.values():
            if device.name.lower() == name_lower:
                return device

        # Then try partial match
        for device in self.devices.values():
            if name_lower in device.name.lower():
                return device

        # Try matching without "demo" prefix
        name_without_demo = name_lower.replace("demo ", "")
        for device in self.devices.values():
            device_name_clean = device.name.lower().replace("demo ", "")
            if name_without_demo in device_name_clean:
                return device

        return None
    
    def get_device_status(self) -> Dict[str, Any]:
        """Get status of all devices"""
        status = {
            "total_devices": len(self.devices),
            "platforms": len(self.platforms),
            "rooms": len(self.rooms),
            "devices_by_room": {}
        }
        
        for room, device_ids in self.rooms.items():
            status["devices_by_room"][room] = []
            for device_id in device_ids:
                device = self.devices.get(device_id)
                if device:
                    status["devices_by_room"][room].append({
                        "name": device.name,
                        "type": device.device_type.value,
                        "state": device.state.value,
                        "platform": device.platform
                    })
        
        return status
