"""
Continuous Code Improvement System for JARVIS V6
===============================================
Enables JARVIS to continuously improve his own code automatically

Features:
- Real-time code analysis and optimization
- Automatic performance improvements
- Code quality enhancements
- Bug detection and auto-fixing
- Architecture optimization
- Continuous refactoring
"""

import os
import ast
import time
import threading
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import re

@dataclass
class CodeImprovement:
    file_path: str
    improvement_type: str
    description: str
    original_code: str
    improved_code: str
    confidence: float
    impact: str  # 'low', 'medium', 'high'
    timestamp: str

@dataclass
class PerformanceMetric:
    metric_name: str
    current_value: float
    target_value: float
    improvement_percentage: float
    last_measured: str

class ContinuousImprovementSystem:
    """System for continuous code improvement and optimization"""
    
    def __init__(self, config=None, deepseek_coder=None):
        self.config = config
        self.deepseek_coder = deepseek_coder
        self.improvement_active = True
        self.improvements_applied = []
        self.performance_metrics = {}
        
        # Improvement directories
        self.improvement_dir = "data/continuous_improvement"
        self.improvements_file = os.path.join(self.improvement_dir, "improvements.json")
        self.metrics_file = os.path.join(self.improvement_dir, "metrics.json")
        
        # Files to monitor and improve
        self.monitored_files = [
            "src/gui/main_window.py",
            "src/ai/training_system.py",
            "src/ai/advanced_memory.py",
            "src/ai/self_evolution.py",
            "src/ai/enhanced_ai_processor.py"
        ]
        
        # Improvement patterns
        self.improvement_patterns = {
            'performance': [
                r'for\s+\w+\s+in\s+range\(len\(',  # Use enumerate instead
                r'\.append\(\w+\)\s*$',  # Use list comprehension
                r'if\s+\w+\s+==\s+True:',  # Simplify boolean checks
                r'if\s+\w+\s+==\s+False:',  # Simplify boolean checks
            ],
            'readability': [
                r'def\s+\w+\([^)]*\):\s*$',  # Add docstrings
                r'class\s+\w+[^:]*:\s*$',  # Add class docstrings
            ],
            'error_handling': [
                r'except\s*:\s*$',  # Catch specific exceptions
                r'except\s+Exception\s*:\s*$',  # Be more specific
            ]
        }
        
        # Ensure directories exist
        os.makedirs(self.improvement_dir, exist_ok=True)
        
        # Load existing data
        self._load_improvement_data()
        
        # Start continuous improvement thread
        self.improvement_thread = threading.Thread(target=self._continuous_improvement_loop, daemon=True)
        self.improvement_thread.start()
        
        print("🔧 Continuous Code Improvement System initialized")
        print(f"📊 Monitoring {len(self.monitored_files)} files for improvements")
    
    def _load_improvement_data(self):
        """Load existing improvement data"""
        try:
            if os.path.exists(self.improvements_file):
                import json
                with open(self.improvements_file, 'r') as f:
                    data = json.load(f)
                    self.improvements_applied = [CodeImprovement(**item) for item in data]
            
            if os.path.exists(self.metrics_file):
                import json
                with open(self.metrics_file, 'r') as f:
                    data = json.load(f)
                    self.performance_metrics = {k: PerformanceMetric(**v) for k, v in data.items()}
                    
        except Exception as e:
            print(f"⚠️ Error loading improvement data: {e}")
    
    def _save_improvement_data(self):
        """Save improvement data"""
        try:
            import json
            from dataclasses import asdict
            
            # Save improvements
            improvements_data = [asdict(imp) for imp in self.improvements_applied]
            with open(self.improvements_file, 'w') as f:
                json.dump(improvements_data, f, indent=2)
            
            # Save metrics
            metrics_data = {k: asdict(v) for k, v in self.performance_metrics.items()}
            with open(self.metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2)
                
        except Exception as e:
            print(f"❌ Error saving improvement data: {e}")
    
    def analyze_code_for_improvements(self, file_path: str) -> List[CodeImprovement]:
        """Analyze code file for potential improvements"""
        improvements = []
        
        try:
            if not os.path.exists(file_path):
                return improvements
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Pattern-based improvements
            improvements.extend(self._find_pattern_improvements(file_path, content))
            
            # AST-based improvements
            improvements.extend(self._find_ast_improvements(file_path, content))
            
            # Performance improvements
            improvements.extend(self._find_performance_improvements(file_path, content))
            
            return improvements
            
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
            return improvements
    
    def _find_pattern_improvements(self, file_path: str, content: str) -> List[CodeImprovement]:
        """Find improvements using regex patterns"""
        improvements = []
        lines = content.split('\n')
        
        for improvement_type, patterns in self.improvement_patterns.items():
            for pattern in patterns:
                for line_num, line in enumerate(lines, 1):
                    if re.search(pattern, line):
                        improvement = self._create_pattern_improvement(
                            file_path, improvement_type, line_num, line, pattern
                        )
                        if improvement:
                            improvements.append(improvement)
        
        return improvements
    
    def _create_pattern_improvement(self, file_path: str, improvement_type: str, 
                                  line_num: int, line: str, pattern: str) -> Optional[CodeImprovement]:
        """Create improvement suggestion based on pattern match"""
        try:
            if 'for.*range(len(' in pattern:
                # Suggest enumerate
                improved_line = re.sub(
                    r'for\s+(\w+)\s+in\s+range\(len\((\w+)\)\):',
                    r'for \1, item in enumerate(\2):',
                    line
                )
                return CodeImprovement(
                    file_path=file_path,
                    improvement_type='performance',
                    description=f"Line {line_num}: Use enumerate instead of range(len())",
                    original_code=line.strip(),
                    improved_code=improved_line.strip(),
                    confidence=0.8,
                    impact='medium',
                    timestamp=datetime.now().isoformat()
                )
            
            elif '== True' in line or '== False' in line:
                # Simplify boolean checks
                improved_line = line.replace('== True', '').replace('== False', ' not ')
                return CodeImprovement(
                    file_path=file_path,
                    improvement_type='readability',
                    description=f"Line {line_num}: Simplify boolean comparison",
                    original_code=line.strip(),
                    improved_code=improved_line.strip(),
                    confidence=0.9,
                    impact='low',
                    timestamp=datetime.now().isoformat()
                )
            
            elif 'except:' in line and 'except Exception:' not in line:
                # Improve exception handling
                improved_line = line.replace('except:', 'except Exception as e:')
                return CodeImprovement(
                    file_path=file_path,
                    improvement_type='error_handling',
                    description=f"Line {line_num}: Use specific exception handling",
                    original_code=line.strip(),
                    improved_code=improved_line.strip(),
                    confidence=0.7,
                    impact='high',
                    timestamp=datetime.now().isoformat()
                )
            
        except Exception as e:
            print(f"❌ Error creating pattern improvement: {e}")
        
        return None
    
    def _find_ast_improvements(self, file_path: str, content: str) -> List[CodeImprovement]:
        """Find improvements using AST analysis"""
        improvements = []
        
        try:
            tree = ast.parse(content)
            
            # Find functions without docstrings
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if not ast.get_docstring(node):
                        improvement = CodeImprovement(
                            file_path=file_path,
                            improvement_type='documentation',
                            description=f"Function '{node.name}' missing docstring",
                            original_code=f"def {node.name}(...)",
                            improved_code=f"def {node.name}(...):\n    \"\"\"Add function description here\"\"\"",
                            confidence=0.6,
                            impact='medium',
                            timestamp=datetime.now().isoformat()
                        )
                        improvements.append(improvement)
                
                # Find classes without docstrings
                elif isinstance(node, ast.ClassDef):
                    if not ast.get_docstring(node):
                        improvement = CodeImprovement(
                            file_path=file_path,
                            improvement_type='documentation',
                            description=f"Class '{node.name}' missing docstring",
                            original_code=f"class {node.name}:",
                            improved_code=f"class {node.name}:\n    \"\"\"Add class description here\"\"\"",
                            confidence=0.6,
                            impact='medium',
                            timestamp=datetime.now().isoformat()
                        )
                        improvements.append(improvement)
        
        except SyntaxError:
            # Skip files with syntax errors
            pass
        except Exception as e:
            print(f"❌ Error in AST analysis: {e}")
        
        return improvements
    
    def _find_performance_improvements(self, file_path: str, content: str) -> List[CodeImprovement]:
        """Find performance-related improvements"""
        improvements = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            # Find inefficient string concatenation
            if '+=' in line and 'str' in line.lower():
                improvement = CodeImprovement(
                    file_path=file_path,
                    improvement_type='performance',
                    description=f"Line {line_num}: Consider using join() for string concatenation",
                    original_code=line.strip(),
                    improved_code="# Use ''.join(list) for multiple concatenations",
                    confidence=0.5,
                    impact='medium',
                    timestamp=datetime.now().isoformat()
                )
                improvements.append(improvement)
            
            # Find potential list comprehension opportunities
            if 'for ' in line and 'append(' in content[content.find(line):content.find(line) + 200]:
                improvement = CodeImprovement(
                    file_path=file_path,
                    improvement_type='performance',
                    description=f"Line {line_num}: Consider using list comprehension",
                    original_code=line.strip(),
                    improved_code="# Consider: result = [item for item in iterable if condition]",
                    confidence=0.4,
                    impact='low',
                    timestamp=datetime.now().isoformat()
                )
                improvements.append(improvement)
        
        return improvements
    
    def apply_safe_improvements(self, improvements: List[CodeImprovement]) -> int:
        """Apply safe, high-confidence improvements automatically"""
        applied_count = 0
        
        for improvement in improvements:
            if improvement.confidence >= 0.8 and improvement.impact in ['low', 'medium']:
                try:
                    if self._apply_improvement(improvement):
                        self.improvements_applied.append(improvement)
                        applied_count += 1
                        print(f"✅ Applied improvement: {improvement.description}")
                except Exception as e:
                    print(f"❌ Failed to apply improvement: {e}")
        
        return applied_count
    
    def _apply_improvement(self, improvement: CodeImprovement) -> bool:
        """Apply a single improvement to the code"""
        try:
            # Read current file content
            with open(improvement.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Apply the improvement
            if improvement.original_code in content:
                new_content = content.replace(improvement.original_code, improvement.improved_code)
                
                # Create backup
                backup_path = f"{improvement.file_path}.backup_{int(time.time())}"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # Write improved content
                with open(improvement.file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                return True
            
        except Exception as e:
            print(f"❌ Error applying improvement: {e}")
        
        return False
    
    def measure_performance(self) -> Dict[str, float]:
        """Measure current system performance"""
        metrics = {}
        
        try:
            # Measure startup time (simulated)
            metrics['startup_time'] = 15.0  # seconds
            
            # Measure memory usage (simulated)
            metrics['memory_usage'] = 250.0  # MB
            
            # Measure response time (simulated)
            metrics['response_time'] = 1.2  # seconds
            
            # Update performance metrics
            for metric_name, value in metrics.items():
                if metric_name not in self.performance_metrics:
                    self.performance_metrics[metric_name] = PerformanceMetric(
                        metric_name=metric_name,
                        current_value=value,
                        target_value=value * 0.8,  # Target 20% improvement
                        improvement_percentage=0.0,
                        last_measured=datetime.now().isoformat()
                    )
                else:
                    metric = self.performance_metrics[metric_name]
                    old_value = metric.current_value
                    metric.current_value = value
                    metric.improvement_percentage = ((old_value - value) / old_value) * 100
                    metric.last_measured = datetime.now().isoformat()
            
        except Exception as e:
            print(f"❌ Error measuring performance: {e}")
        
        return metrics
    
    def _continuous_improvement_loop(self):
        """Continuous improvement background process"""
        while self.improvement_active:
            try:
                print("🔧 Running continuous code improvement analysis...")
                
                total_improvements = 0
                
                # Analyze each monitored file
                for file_path in self.monitored_files:
                    improvements = self.analyze_code_for_improvements(file_path)
                    if improvements:
                        applied = self.apply_safe_improvements(improvements)
                        total_improvements += applied
                
                if total_improvements > 0:
                    print(f"✅ Applied {total_improvements} code improvements")
                
                # Measure performance
                self.measure_performance()
                
                # Save data
                self._save_improvement_data()
                
                # Sleep for 30 minutes before next improvement cycle
                time.sleep(1800)
                
            except Exception as e:
                print(f"❌ Error in continuous improvement loop: {e}")
                time.sleep(300)  # Wait 5 minutes on error
    
    def get_improvement_summary(self) -> Dict[str, Any]:
        """Get summary of improvement activities"""
        recent_improvements = [
            imp for imp in self.improvements_applied
            if datetime.fromisoformat(imp.timestamp) > datetime.now() - timedelta(hours=24)
        ]
        
        improvement_types = {}
        for imp in self.improvements_applied:
            improvement_types[imp.improvement_type] = improvement_types.get(imp.improvement_type, 0) + 1
        
        return {
            'total_improvements': len(self.improvements_applied),
            'recent_improvements_24h': len(recent_improvements),
            'improvement_types': improvement_types,
            'monitored_files': len(self.monitored_files),
            'performance_metrics': {k: v.current_value for k, v in self.performance_metrics.items()},
            'improvement_active': self.improvement_active
        }
    
    def stop_improvement(self):
        """Stop continuous improvement"""
        self.improvement_active = False
        self._save_improvement_data()
        print("🛑 Continuous improvement stopped")
    
    def resume_improvement(self):
        """Resume continuous improvement"""
        self.improvement_active = True
        print("▶️ Continuous improvement resumed")
