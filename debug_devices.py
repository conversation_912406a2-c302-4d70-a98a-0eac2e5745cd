#!/usr/bin/env python3
"""
Debug JARVIS Smart Home Device Discovery
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from plugins.smart_home_plugin import SmartHomeManager
from ai.smart_home_commands import SmartHomeCommandProcessor

async def debug_devices():
    """Debug device discovery and naming"""
    print("🔍 JARVIS Smart Home Device Debug")
    print("=" * 50)
    
    # Initialize smart home manager
    manager = SmartHomeManager()
    await manager.initialize()
    
    print(f"\n📱 Discovered Devices:")
    print("-" * 30)
    
    for device_id, device in manager.devices.items():
        print(f"ID: {device_id}")
        print(f"Name: '{device.name}'")
        print(f"Type: {device.device_type.value}")
        print(f"Platform: {device.platform}")
        print(f"Room: {device.room}")
        print(f"State: {device.state.value}")
        print()
    
    print(f"\n🔍 Testing Device Search:")
    print("-" * 30)
    
    test_names = ["ac", "AC", "air conditioning", "midea ac", "austin's ac", "the ac"]
    
    for name in test_names:
        device = manager.find_device_by_name(name)
        if device:
            print(f"✅ '{name}' -> Found: {device.name} (ID: {device.id})")
        else:
            print(f"❌ '{name}' -> Not found")
    
    print(f"\n🗣️ Testing Command Parsing:")
    print("-" * 30)

    processor = SmartHomeCommandProcessor()

    test_commands = [
        "turn off ac",
        "turn off the ac",
        "turn off air conditioning",
        "turn off austin's ac"
    ]

    for command_text in test_commands:
        command = processor.parse_command(command_text)
        if command:
            print(f"'{command_text}' -> {command.command_type.value} '{command.target}' (confidence: {command.confidence:.2f})")
        else:
            print(f"'{command_text}' -> Failed to parse")

    print(f"\n🔧 Testing Device Control:")
    print("-" * 30)

    # Test actual device control
    device = manager.find_device_by_name("ac")
    if device:
        print(f"Found device: {device.name} (ID: {device.id})")
        print(f"Platform: {device.platform}")

        # Check platform mapping
        platform_key = device.platform.lower().replace(" ", "_").replace("-", "_")
        print(f"Platform key: '{platform_key}'")
        print(f"Available platforms: {list(manager.platforms.keys())}")

        platform = manager.platforms.get(platform_key)
        if platform:
            print(f"✅ Platform found: {platform.name}")

            # Test actual turn_off command
            print(f"\n🧪 Testing turn_off command...")
            try:
                result = await platform.turn_off(device.id)
                print(f"Turn off result: {result}")
            except Exception as e:
                print(f"Turn off error: {e}")

            # Test turn_off_device method
            print(f"\n🧪 Testing turn_off_device method...")
            try:
                result = await manager.turn_off_device("ac")
                print(f"Turn off device result: {result}")
            except Exception as e:
                print(f"Turn off device error: {e}")
        else:
            print(f"❌ Platform not found for key: {platform_key}")
    else:
        print("❌ Device 'ac' not found")

if __name__ == "__main__":
    asyncio.run(debug_devices())
