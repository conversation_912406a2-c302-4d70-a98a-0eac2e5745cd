#!/usr/bin/env python3
"""
Test fast and short AI responses
"""

import sys
import time

def test_fast_responses():
    """Test the speed and length of AI responses"""
    print("Testing fast and short AI responses...")
    
    try:
        # Import our modules
        from src.gui.qt_compat import QApplication
        from src.core.config import Config
        from src.ai.ollama_client import OllamaWorker
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Load config
        config = Config.load_from_env()
        
        # Test messages
        test_messages = [
            "Hello, how are you?",
            "What's the weather like?",
            "Tell me a joke",
            "What time is it?",
            "Help me with something"
        ]
        
        print(f"Testing {len(test_messages)} messages for speed and brevity...")
        
        total_time = 0
        total_responses = 0
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n--- Test {i}/{len(test_messages)} ---")
            print(f"Message: '{message}'")
            
            # Track response
            response_received = False
            error_occurred = None
            ai_response = ""
            
            start_time = time.time()
            
            def on_response(response):
                nonlocal response_received, ai_response
                response_received = True
                ai_response = response.strip()
            
            def on_error(error):
                nonlocal error_occurred
                error_occurred = error
            
            # Create worker
            system_prompt = config.get_personality_prompt()
            worker = OllamaWorker(config, message, system_prompt)
            
            # Connect signals
            worker.response_ready.connect(on_response)
            worker.error_occurred.connect(on_error)
            
            # Start worker
            worker.start()
            
            # Wait for completion
            timeout = 15  # 15 second timeout
            while not response_received and error_occurred is None:
                app.processEvents()
                time.sleep(0.1)
                
                if time.time() - start_time > timeout:
                    print("⚠️ Response timed out")
                    break
            
            # Wait for worker to finish
            worker.wait()
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response_received:
                word_count = len(ai_response.split())
                sentence_count = ai_response.count('.') + ai_response.count('!') + ai_response.count('?')
                
                print(f"✅ Response ({duration:.2f}s): '{ai_response}'")
                print(f"   Words: {word_count}, Sentences: {sentence_count}")
                
                # Check if response is short enough
                if word_count <= 25:
                    print("   ✅ Response is appropriately short")
                else:
                    print("   ⚠️ Response might be too long")
                
                # Check if response is fast enough
                if duration <= 8.0:
                    print("   ✅ Response is fast")
                else:
                    print("   ⚠️ Response is slow")
                
                total_time += duration
                total_responses += 1
                
            elif error_occurred:
                print(f"❌ Error: {error_occurred}")
            else:
                print("❌ Timed out")
        
        if total_responses > 0:
            avg_time = total_time / total_responses
            print(f"\n🎉 Test completed!")
            print(f"Average response time: {avg_time:.2f}s")
            print(f"Successful responses: {total_responses}/{len(test_messages)}")
            
            if avg_time <= 6.0:
                print("✅ Responses are fast enough!")
                return True
            else:
                print("⚠️ Responses could be faster")
                return False
        else:
            print("❌ No successful responses")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fast_responses()
    if success:
        print("\n🎉 AI responses are fast and concise!")
    else:
        print("\n❌ AI responses need optimization!")
    
    sys.exit(0 if success else 1)
