#!/usr/bin/env python3
"""
Test script to verify Jarvis V6 installation and dependencies
"""

import sys
import importlib

def test_imports():
    """Test if all required modules can be imported"""
    required_modules = [
        'PyQt6',
        'PyQt6.QtWidgets',
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'requests',
        'aiohttp',
        'pydantic',
        'psutil',
        'json',
        'asyncio',
        'os',
        'datetime'
    ]
    
    print("Testing module imports...")
    failed_imports = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module} - {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\nFailed to import {len(failed_imports)} modules:")
        for module in failed_imports:
            print(f"  - {module}")
        return False
    else:
        print(f"\nAll {len(required_modules)} modules imported successfully!")
        return True

def test_ollama_connection():
    """Test connection to Ollama server"""
    print("\nTesting Ollama connection...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✓ Ollama server is running and accessible")
            
            # Check if Mixtral model is available
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            
            if any('mixtral' in model.lower() for model in models):
                print("✓ Mixtral model is available")
            else:
                print("⚠ Mixtral model not found. Available models:")
                for model in models:
                    print(f"  - {model}")
                    
            return True
        else:
            print(f"✗ Ollama server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Cannot connect to Ollama server: {e}")
        print("  Make sure Ollama is installed and running on localhost:11434")
        return False

def test_gui_creation():
    """Test if GUI can be created"""
    print("\nTesting GUI creation...")
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # Create QApplication (required for any Qt widgets)
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        print("✓ QApplication created successfully")
        
        # Test basic widget creation
        from PyQt6.QtWidgets import QMainWindow, QWidget, QLabel
        
        window = QMainWindow()
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        label = QLabel("Test Label")
        
        print("✓ Basic widgets created successfully")
        
        # Don't show the window, just test creation
        window.close()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI creation failed: {e}")
        return False

def test_project_structure():
    """Test if project structure is correct"""
    print("\nTesting project structure...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        'src/__init__.py',
        'src/core/__init__.py',
        'src/core/config.py',
        'src/gui/__init__.py',
        'src/gui/main_window.py',
        'src/gui/chat_widget.py',
        'src/gui/input_widget.py',
        'src/gui/animated_background.py',
        'src/ai/__init__.py',
        'src/ai/ollama_client.py',
        'src/utils/__init__.py',
        'src/utils/helpers.py',
        'src/plugins/__init__.py',
        'src/plugins/plugin_manager.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        try:
            with open(file_path, 'r') as f:
                pass  # Just check if file can be opened
            print(f"✓ {file_path}")
        except FileNotFoundError:
            print(f"✗ {file_path}")
            missing_files.append(file_path)
        except Exception as e:
            print(f"⚠ {file_path} - {e}")
    
    if missing_files:
        print(f"\nMissing {len(missing_files)} files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print(f"\nAll {len(required_files)} required files found!")
        return True

def main():
    """Run all tests"""
    print("=" * 50)
    print("JARVIS V6 INSTALLATION TEST")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Project Structure", test_project_structure),
        ("GUI Creation", test_gui_creation),
        ("Ollama Connection", test_ollama_connection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"Running: {test_name}")
        print(f"{'-' * 30}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 50}")
    print("TEST SUMMARY")
    print(f"{'=' * 50}")
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Jarvis V6 is ready to run.")
        print("Run 'python main.py' to start the application.")
    else:
        print(f"\n⚠ {len(results) - passed} test(s) failed. Please fix the issues before running.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
